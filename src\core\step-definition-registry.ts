import { expect } from '@playwright/test';
import { PlaywrightWorld } from './playwright-world';
import { Logger } from '../utils/logger';

/**
 * Step Definition Registry
 * 
 * This registry loads and manages all step definitions from the framework,
 * allowing the Playwright-Cucumber runner to execute steps properly.
 */

export interface StepDefinition {
  pattern: RegExp;
  handler: (world: PlaywrightWorld, ...args: any[]) => Promise<void>;
  description: string;
}

export class StepDefinitionRegistry {
  private logger: Logger;
  private stepDefinitions: StepDefinition[] = [];

  constructor() {
    this.logger = new Logger('StepDefinitionRegistry');
    this.loadStepDefinitions();
  }

  /**
   * Load all step definitions from the framework
   */
  private loadStepDefinitions(): void {
    this.logger.info('Loading step definitions...');

    // Navigation steps
    this.registerStep(
      /^I navigate to "([^"]*)"$/,
      async (world: PlaywrightWorld, url: string) => {
        await world.navigateTo(url);
      },
      'Navigate to a URL'
    );

    this.registerStep(
      /^I navigate to URL "([^"]*)"$/,
      async (world: PlaywrightWorld, url: string) => {
        await world.navigateTo(url);
      },
      'Navigate to a URL'
    );

    this.registerStep(
      /^I visit "([^"]*)"$/,
      async (world: PlaywrightWorld, url: string) => {
        await world.navigateTo(url);
      },
      'Visit a URL'
    );

    // Click steps
    this.registerStep(
      /^I click on "([^"]*)"$/,
      async (world: PlaywrightWorld, selector: string) => {
        await world.clickElement(selector);
      },
      'Click on an element'
    );

    this.registerStep(
      /^I click the "([^"]*)" button$/,
      async (world: PlaywrightWorld, selector: string) => {
        await world.clickElement(selector);
      },
      'Click a button'
    );

    // Input steps
    this.registerStep(
      /^I fill "([^"]*)" with "([^"]*)"$/,
      async (world: PlaywrightWorld, selector: string, value: string) => {
        await world.fillField(selector, value);
      },
      'Fill an input field'
    );

    this.registerStep(
      /^I enter "([^"]*)" in "([^"]*)"$/,
      async (world: PlaywrightWorld, value: string, selector: string) => {
        await world.fillField(selector, value);
      },
      'Enter text in an input field'
    );

    this.registerStep(
      /^I type "([^"]*)" in "([^"]*)"$/,
      async (world: PlaywrightWorld, value: string, selector: string) => {
        await world.typeInField(selector, value);
      },
      'Type text in an input field'
    );

    // Assertion steps
    this.registerStep(
      /^I should see "([^"]*)"$/,
      async (world: PlaywrightWorld, text: string) => {
        await world.verifyTextPresent(text);
      },
      'Verify text is visible on page'
    );

    this.registerStep(
      /^the page should contain "([^"]*)"$/,
      async (world: PlaywrightWorld, text: string) => {
        await world.verifyTextPresent(text);
      },
      'Verify page contains text'
    );

    this.registerStep(
      /^"([^"]*)" should be visible$/,
      async (world: PlaywrightWorld, selector: string) => {
        await world.verifyElementVisible(selector);
      },
      'Verify element is visible'
    );

    this.registerStep(
      /^the page title should contain "([^"]*)"$/,
      async (world: PlaywrightWorld, text: string) => {
        await world.verifyTitleContains(text);
      },
      'Verify page title contains text'
    );

    this.registerStep(
      /^"([^"]*)" should contain text "([^"]*)"$/,
      async (world: PlaywrightWorld, selector: string, text: string) => {
        await world.verifyElementContainsText(selector, text);
      },
      'Verify element contains text'
    );

    // Wait steps
    this.registerStep(
      /^I wait for (\d+) seconds?$/,
      async (world: PlaywrightWorld, seconds: string) => {
        const duration = parseInt(seconds) * 1000;
        await world.wait(duration);
      },
      'Wait for specified seconds'
    );

    this.registerStep(
      /^I wait for "([^"]*)" to be visible$/,
      async (world: PlaywrightWorld, selector: string) => {
        await world.waitForElement(selector);
      },
      'Wait for element to be visible'
    );

    this.registerStep(
      /^I wait for element "([^"]*)" to be visible with timeout (\d+)$/,
      async (world: PlaywrightWorld, selector: string, timeout: string) => {
        await world.waitForElement(selector, parseInt(timeout));
      },
      'Wait for element to be visible with timeout'
    );

    this.registerStep(
      /^I wait for "([^"]*)" to be visible with timeout (\d+)$/,
      async (world: PlaywrightWorld, selector: string, timeout: string) => {
        await world.waitForElement(selector, parseInt(timeout));
      },
      'Wait for element to be visible with timeout (alternative syntax)'
    );

    this.registerStep(
      /^I wait for page to load$/,
      async (world: PlaywrightWorld) => {
        await world.waitForPageLoad();
      },
      'Wait for page to load'
    );

    this.registerStep(
      /^I wait for page title to contain "([^"]*)"$/,
      async (world: PlaywrightWorld, text: string) => {
        // Wait for title to contain the text with robust navigation handling
        let attempts = 0;
        const maxAttempts = 60; // 60 seconds for SharePoint navigation

        this.logger.info(`Waiting for page title to contain: "${text}"`);

        while (attempts < maxAttempts) {
          try {
            // Get title directly without waiting for network idle (SharePoint has ongoing requests)
            const title = await world.page.title();
            this.logger.info(`Current title: "${title}" (attempt ${attempts + 1})`);

            if (title.includes(text)) {
              this.logger.info(`✅ Title contains expected text: "${text}"`);
              return;
            }

            // Wait a bit before next attempt
            await world.wait(1000);
            attempts++;

          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.logger.warn(`Error getting title, retrying... (${errorMessage})`);
            await world.wait(2000);
            attempts++;
          }
        }

        // Final attempt to get current title for error message
        try {
          const finalTitle = await world.page.title();
          throw new Error(`Timeout waiting for page title to contain "${text}". Current title: "${finalTitle}"`);
        } catch {
          throw new Error(`Timeout waiting for page title to contain "${text}". Could not retrieve current title.`);
        }
      },
      'Wait for page title to contain text'
    );

    this.registerStep(
      /^I wait for (\d+) milliseconds$/,
      async (world: PlaywrightWorld, milliseconds: string) => {
        const duration = parseInt(milliseconds);
        await world.wait(duration);
      },
      'Wait for specified milliseconds'
    );

    // SharePoint specific steps
    this.registerStep(
      /^I login to Microsoft with stored credentials$/,
      async (world: PlaywrightWorld) => {
        this.logger.info(`🔐 Logging into Microsoft`);
        // This is a placeholder - in real implementation, this would handle Microsoft login
        await world.wait(2000);
      },
      'Login to Microsoft with stored credentials'
    );

    // Generic steps
    this.registerStep(
      /^I press "([^"]*)"$/,
      async (world: PlaywrightWorld, key: string) => {
        await world.pressKey(key);
      },
      'Press a keyboard key'
    );

    this.registerStep(
      /^I scroll to "([^"]*)"$/,
      async (world: PlaywrightWorld, selector: string) => {
        await world.scrollToElement(selector);
      },
      'Scroll to an element'
    );

    this.registerStep(
      /^I take a screenshot$/,
      async (world: PlaywrightWorld) => {
        await world.takeScreenshot();
      },
      'Take a screenshot'
    );

    // Store credentials step (placeholder)
    this.registerStep(
      /^I store "([^"]*)" as "([^"]*)"$/,
      async (world: PlaywrightWorld, value: string, key: string) => {
        this.logger.info(`💾 Storing ${value} as ${key}`);
        // This is a placeholder for credential storage
      },
      'Store a value for later use'
    );

    // URL parameter validation steps
    this.registerStep(
      /^the URL parameter "([^"]*)" should be "([^"]*)"$/,
      async (world: PlaywrightWorld, paramName: string, expectedValue: string) => {
        this.logger.info(`Verifying URL parameter ${paramName} equals: ${expectedValue}`);
        const currentUrl = new URL(world.page.url());
        const actualValue = currentUrl.searchParams.get(paramName);
        if (actualValue !== expectedValue) {
          throw new Error(`Expected URL parameter "${paramName}" to be "${expectedValue}", but got "${actualValue}"`);
        }
      },
      'Verify URL parameter value'
    );

    // Enhanced wait for element with better SharePoint dialog handling
    this.registerStep(
      /^I wait for "([^"]*)" to be visible with timeout (\d+)$/,
      async (world: PlaywrightWorld, selector: string, timeout: string) => {
        this.logger.info(`Waiting for element to be visible: ${selector} (timeout: ${timeout}ms)`);

        // Special handling for SharePoint dialogs and panels
        if (selector.includes('getByLabel') && selector.includes('Datasources')) {
          await this.waitForSharePointDialog(world, selector, parseInt(timeout));
        } else {
          await world.waitForElement(selector, parseInt(timeout));
        }
      },
      'Wait for element to be visible with timeout (SharePoint enhanced)'
    );

    this.logger.info(`✅ Loaded ${this.stepDefinitions.length} step definitions`);
  }

  /**
   * Register a step definition
   */
  private registerStep(
    pattern: RegExp,
    handler: (world: PlaywrightWorld, ...args: any[]) => Promise<void>,
    description: string
  ): void {
    this.stepDefinitions.push({
      pattern,
      handler,
      description
    });
  }

  /**
   * Find and execute a step
   */
  async executeStep(world: PlaywrightWorld, stepText: string): Promise<void> {
    const cleanStepText = stepText.replace(/^(Given|When|Then|And|But)\s+/, '').trim();
    
    for (const stepDef of this.stepDefinitions) {
      const match = cleanStepText.match(stepDef.pattern);
      if (match) {
        this.logger.info(`🎯 Matched step: ${stepDef.description}`);
        const args = match.slice(1); // Remove the full match, keep capture groups
        await stepDef.handler(world, ...args);
        return;
      }
    }

    // If no step definition found, log a warning but don't fail
    this.logger.warn(`⚠️ No step definition found for: "${stepText}"`);
    this.logger.info(`💡 Available step patterns:`);
    this.stepDefinitions.forEach(stepDef => {
      this.logger.info(`   - ${stepDef.description}: ${stepDef.pattern.source}`);
    });
    
    // Add a small delay to simulate step execution
    await world.page.waitForTimeout(500);
  }

  /**
   * Get all registered step definitions
   */
  getStepDefinitions(): StepDefinition[] {
    return [...this.stepDefinitions];
  }

  /**
   * Get step definitions count
   */
  getStepCount(): number {
    return this.stepDefinitions.length;
  }

  /**
   * Wait for SharePoint dialog/panel to be visible
   */
  private async waitForSharePointDialog(world: PlaywrightWorld, selector: string, timeout: number): Promise<void> {
    this.logger.info(`Waiting for SharePoint dialog: ${selector}`);

    let attempts = 0;
    const maxAttempts = Math.floor(timeout / 1000); // Convert to seconds

    while (attempts < maxAttempts) {
      try {
        // Parse the complex selector for SharePoint dialogs
        const element = world.page.locator(selector);

        // Check if element exists and is visible
        const isVisible = await element.isVisible();
        if (isVisible) {
          this.logger.info(`✅ SharePoint dialog element found and visible: ${selector}`);
          return;
        }

        // Try alternative approaches for SharePoint dialogs
        // Sometimes the dialog needs to be triggered or is in a different state

        // Check if there's a panel or dialog container
        const panelContainer = world.page.locator('[role="dialog"], .ms-Panel, .ms-Callout');
        const panelExists = await panelContainer.count() > 0;

        if (panelExists) {
          this.logger.info('Found SharePoint panel/dialog container, checking for target element...');

          // Look for the element within the panel
          const elementInPanel = panelContainer.locator(selector.replace(/^getByLabel\('([^']+)'\)\./, '')).first();
          const isElementInPanelVisible = await elementInPanel.isVisible().catch(() => false);

          if (isElementInPanelVisible) {
            this.logger.info(`✅ Found element in SharePoint panel: ${selector}`);
            return;
          }
        }

        // Wait and retry
        await world.wait(1000);
        attempts++;

        this.logger.info(`SharePoint dialog attempt ${attempts}/${maxAttempts} for: ${selector}`);

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        this.logger.warn(`SharePoint dialog check failed, retrying... (${errorMessage})`);
        await world.wait(1000);
        attempts++;
      }
    }

    // Final attempt with detailed error
    try {
      const element = world.page.locator(selector);
      await element.waitFor({ state: 'visible', timeout: 5000 });
    } catch (error) {
      // Provide detailed debugging information
      const panelCount = await world.page.locator('[role="dialog"], .ms-Panel, .ms-Callout').count();
      const allDialogs = await world.page.locator('[role="dialog"]').count();

      throw new Error(
        `SharePoint dialog element not found: ${selector}\n` +
        `Panels found: ${panelCount}, Dialogs found: ${allDialogs}\n` +
        `This might be a timing issue with SharePoint's dynamic UI loading.\n` +
        `Original error: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }
}
