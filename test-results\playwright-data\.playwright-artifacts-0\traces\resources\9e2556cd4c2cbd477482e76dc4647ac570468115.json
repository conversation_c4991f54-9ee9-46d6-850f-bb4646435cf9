{"MicrosoftSearch": {"searchux.activityItemsNumberOfHubSites": 100, "searchux.activityItemsNumberOfHubActivities": 25, "searchux.activityItemsNumberOfSiteActivities": 100, "searchux.shouldFetchVerticalsFromDepartmentId": true, "searchux.cosmicThorEndpoint": "https://prod-config.thor.aesir.office.com", "searchux.tokenProviderURL": "https://outlook.office365.com/search", "searchux.serviceEnvironmentConfig": "Prod", "searchux.addOneUpQueryParamToVRoomFileUrl": true, "searchux.sortHubSiteActivities": true, "searchux.allTabConnectorClusterViaAdminKnob": true, "searchux.isQuickActionsEnabled": true, "searchBoxQuickAction.CopyEmailId": {"position": 40}, "searchBoxQuickAction.contactCardWithLoader": {"position": 50}, "searchBoxQuickAction.OpenContactCard": null, "searchBoxQuickAction.CopySiteLink": {}, "searchBoxQuickAction.ManagePQH": {}, "searchBoxQuickAction.TopicCenter": {}, "searchBoxQuickAction.CopyBookmarkLink": {}, "searchBoxQuickAction.Download": {}, "searchux.enable3SEventsLogging": true, "searchux.enableDotNetCoreEndPoint": true, "searchux.enableSmallerGroupHeading": true, "searchux.focusModeDimElementParentId": "CenterRegion", "searchBoxPluginConfiguration.SearchBoxFocusModeHandler": {"position": 250}, "searchux.isTopicBrandingEnabled": true, "suggestionProviderGroups.vRoomNews": {"position": 10, "optimalCount": 3, "component": "searchux.suggestionComponent.news.default", "heading": "searchux.strings.suggestionGroup.news.heading", "ariaLabel": "searchux.strings.suggestionGroup.news.ariaLabel", "providers.vRoomNews": {"position": 10}}, "suggestionProviderGroups.news": null, "suggestionProviderGroups.vRoomFiles": {"position": 20, "optimalCount": 2, "component": "searchux.suggestionComponent.file.default", "heading": "searchux.strings.suggestionGroup.file.heading", "ariaLabel": "searchux.strings.suggestionGroup.file.ariaLabel", "providers.vRoomFiles": {"position": 10}}, "suggestionProviderGroups.file": null, "suggestionProviderGroups.vRoomSites": {"position": 30, "optimalCount": 2, "component": "searchux.suggestionComponent.site.default", "heading": "searchux.strings.suggestionGroup.site.heading", "ariaLabel": "searchux.strings.suggestionGroup.site.ariaLabel", "providers.vRoomSites": {"position": 10}}, "suggestionProviderGroups.site": null, "searchux.isPCSOverviewVerticalEnabled": true, "searchux.isPeopleCentricSearchHeaderEnabled": true, "EnablePeopleCentricSearchController": false, "searchux.telemetryBoundary": "EU", "searchux.killSwitches.fd103984-5798-4920-987c-c6fe11cbbad1": false, "searchux.enableSharePointResultsCustomization": true, "searchux.checkMRTConfigurations": true, "searchux.tenantFeedback": true, "searchux.useOdsFeedback": true, "searchux.generalFeedback": true, "searchux.killSwitches.37e97136-4dde-408d-8ef7-497036f543bd": true, "searchux.isTopicTypesSchemaEnabled": false, "searchux.isTopicLabelV2Enabled": true, "searchux.isTopicExternalLinkEnabled": true, "enableClientTypeFilter": false, "searchux.enableSharepointCustomVerticals": true, "searchux.enableVerticalsViaConfig": true, "enableVerticalsViaConfig": true, "searchux.enableQueryStringParam": true, "searchux.enableCustomRefiners": true, "searchux.enableRefinerValuesCap": true, "searchux.refinerValuesCapLimit": 50, "searchux.enableCustomRefinersViaFlight": false, "searchux.flightedCustomRefinerIds": "", "searchux.pfp.preloadConfiguredRefiners": true, "siteadmin.EnableSharePointAsAContentSource": true, "siteadmin.EnableOOBVerticalsinMicrosoftSearch": true, "suiteux.prepareTokenForGlobalScope": true, "searchux.deserializer.waitForSuggestionComponents": true, "suiteux.payForPlayPrefetchEnabled": true, "searchux.enableMrtCacheFlight": false, "searchux.enableRPCCall": true, "searchux.updateCacheInBackground": true, "searchux.webEqualsOneForPdf": true, "searchux.webEqualsOneQueryParameter": true, "searchux.fileFormatsOpenedInPreviewer": "url,pdf", "searchBoxPluginConfiguration.SearchBoxClearButton": null, "searchBoxPluginConfiguration.SearchBoxSubmitButton": null, "searchBoxPluginConfiguration.SearchBoxMagnifierButton": null, "searchBoxPluginConfiguration.SearchBoxHideableMagnifierButton": {"position": 10}, "searchBoxPluginConfiguration.SearchBoxExitButton": {"position": 20}, "searchBoxPluginConfiguration.SearchBoxHideableMagnifierSubmitButton": {"position": 120}, "searchux.exitSearchInSearchBox": true, "searchux.enableUserQualificationChecks": false, "userQualificationCheckTimeoutInMiliseconds": 1000, "siteadmin.EnableMultipleConnectionSupportForVerticals": true, "siteadmin.EnableCaptionsInResultTypes": true, "siteadmin.EnableResultClusterForMicrosoftSearch": true, "siteadmin.isResultCaptionsInResultTypesEnabled": true, "searchBoxLoggingDelay": 0, "searchux.useLssViewCount": true, "serpConfigCacheTime": 360, "searchux.substrateDiagnosticsLogging": true, "suiteux.zeroTermPrefetchEnabled": true, "searchBoxScopes": [{"id": "global", "appId": "SPHomeWeb"}, {"id": "person", "appId": "PeoplePill"}, {"id": "ScopeSPHomeWeb", "appId": "SPHomeWeb"}], "searchux.substrateSearchClientFlights": " ", "searchBoxPluginConfiguration.BasicSuggestionProviderHost": {"position": 210}, "searchBoxPluginConfiguration.SubmitSearchSuggestion": {"position": 240}, "searchux.suggestionGroupStreamPolicy": "searchux.suggestionProvider.streamPolicy.eager", "searchux.clientSideRelevance": false, "searchux.isCrossGeoEnabled": false, "searchux.showPersonalResults": false, "searchux.use3SFileSuggestions": false, "searchux.use3SPeopleSuggestions": false, "searchux.use3SSiteSuggestions": false, "searchux.useSearchApp": false, "searchux.useSearchBoxInTheSuiteNav": false, "searchBoxSuggestionProviderConfiguration": {"streamPolicy": "searchux.suggestionProvider.streamPolicy.eager", "groups": [{"id": "news", "optimalCount": 3, "providers": ["searchux.suggestionProvider.provider.sharePointSearchNewsSuggestions"], "component": "searchux.suggestionComponent.news.default", "heading": "searchux.strings.suggestionGroup.news.heading", "ariaLabel": "searchux.strings.suggestionGroup.news.ariaLabel"}, {"id": "file", "optimalCount": 2, "providers": ["searchux.suggestionProvider.provider.sharePointHomePrefetchFilesSiteScoped"], "component": "searchux.suggestionComponent.file.default", "heading": "searchux.strings.suggestionGroup.file.heading", "ariaLabel": "searchux.strings.suggestionGroup.file.ariaLabel"}, {"id": "site", "optimalCount": 2, "providers": ["searchux.suggestionProvider.provider.sharePointAssociatedSites"], "component": "searchux.suggestionComponent.site.default", "heading": "searchux.strings.suggestionGroup.site.heading", "ariaLabel": "searchux.strings.suggestionGroup.site.ariaLabel"}]}, "disableSharepointInfo": true, "enableDebugPanel": false, "searchux.excludeBestBets": false, "searchux.enableCustomSERPConfiguration": false, "searchux.enableCultureOnSubstrateRequestBody": true, "searchux.enableTelemetryEventsForOobRefiners": true, "searchux.serpVersion": "1.20250519.3.0"}, "Headers": {"ETag": "\"+MEJxGVRDCzutiThL1QrFGBrnj4zOSedIxQbK1IA2q4=\"", "Expires": "Thu, 05 Jun 2025 06:10:56 GMT", "CountryCode": "IN", "StatusCode": "200"}, "ConfigIDs": {"MicrosoftSearch": "P-R-1275275-3-5,P-R-1266138-11-3,P-R-1257332-4-6,P-R-1254770-11-3,P-R-1254763-4-3,P-R-1236267-4-5,P-R-1161809-4-3,P-R-1065942-4-6,P-R-1100081-2-13,P-R-1087266-4-33,P-R-1056220-4-5,P-R-1051698-1-6,P-R-1048896-1-8,P-R-1048746-4-3,P-R-1047446-2-9,P-R-1047186-4-17,P-R-1039673-11-7,P-R-1036816-2-17,P-R-102223-7-108,P-R-1025564-4-3,P-R-1017675-4-7,P-R-1016878-11-7,P-R-1004431-6-9,P-R-1003339-4-6,P-R-114066-7-35,P-R-113159-7-38,P-R-110459-2-19,P-R-106802-3-77,P-R-106259-3-78,P-R-105368-4-5,P-R-96649-4-6,P-R-96081-4-8,P-R-85485-12-3,P-R-89633-15-33,P-R-86415-6-7,P-R-81904-12-23,P-R-71200-1-47,P-R-68771-3-75,P-R-60693-1-10,P-R-54363-1-5,P-R-45563-1-8,P-R-43263-6-10,P-R-42299-12-28,P-D-1473379-1-5,P-D-108282-1-2,P-D-68653-1-20,P-D-67262-3-24,P-D-66948-12-3,P-D-63889-2-52,P-D-31828-1-66"}}