const { EdgeInPrivateManager } = require('./dist/src/core/edge-inprivate-manager.js');

async function testEdgeInPrivateFinal() {
  console.log('🔒 Testing Edge InPrivate Manager...');
  
  const manager = new EdgeInPrivateManager();
  
  try {
    // Test the Edge InPrivate launch
    console.log('Step 1: Launching Edge in InPrivate mode...');
    const launched = await manager.launchEdgeInPrivate('https://www.google.com');
    
    if (launched) {
      console.log('✅ Edge launched in InPrivate mode successfully!');
      console.log('🔍 Check your screen - Edge should be open in InPrivate mode');
      console.log('   Look for "InPrivate" indicator in the Edge window');
      
      // Wait for user to verify
      console.log('Waiting 10 seconds for verification...');
      await new Promise(resolve => setTimeout(resolve, 10000));
      
    } else {
      console.log('❌ Failed to launch Edge in InPrivate mode');
    }
    
  } catch (error) {
    console.error('❌ Error during test:', error.message);
  } finally {
    // Cleanup
    await manager.cleanup();
    console.log('✅ Test completed and cleaned up');
  }
}

// Run the test
testEdgeInPrivateFinal().catch(console.error);
