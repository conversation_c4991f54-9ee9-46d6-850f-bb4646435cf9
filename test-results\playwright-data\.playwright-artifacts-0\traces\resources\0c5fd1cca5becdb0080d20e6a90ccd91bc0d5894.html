<script type="text/javascript">!function(t,e){for(var s in e)t[s]=e[s]}(this,function(t){function e(n){if(s[n])return s[n].exports;var i=s[n]={exports:{},id:n,loaded:!1};return t[n].call(i.exports,i,i.exports,e),i.loaded=!0,i.exports}var s={};return e.m=t,e.c=s,e.p="",e(0)}([function(t,e){function s(t){for(var e=f[S],s=0,n=e.length;s<n;++s)if(e[s]===t)return!0;return!1}function n(t){if(!t)return null;for(var e=t+"=",s=document.cookie.split(";"),n=0,i=s.length;n<i;n++){var a=s[n].replace(/^\s*(\w+)\s*=\s*/,"$1=").replace(/(\s+$)/,"");if(0===a.indexOf(e))return a.substring(e.length)}return null}function i(t,e,s){if(t)for(var n=t.split(":"),i=null,a=0,r=n.length;a<r;++a){var c=null,S=n[a].split("$");if(0===a&&(i=parseInt(S.shift()),!i))return;var l=S.length;if(l>=1){var p=o(i,S[0]);if(!p||s[p])continue;c={signInName:p,idp:"msa",isSignedIn:!0}}if(l>=3&&(c.firstName=o(i,S[1]),c.lastName=o(i,S[2])),l>=4){var f=S[3],d=f.split("|");c.otherHashedAliases=d}if(l>=5){var h=parseInt(S[4],16);h&&(c.isSignedIn=h===g.SignedInToRP||h===g.SignedInToIDP)}if(l>=6){var m=parseInt(S[5],16);m&&(c.isWindowsSso=0!==(m&u.IsWindowsSso))}if(l>=7){var v=parseInt(S[6],16);v&&(c.remoteIdpFlags=v)}l>=8&&(c.sessionId=S[7]),c&&(e.push(c),s[c.signInName]=!0)}}function a(t){if(t&&s(t.origin)&&t.data)try{var e=JSON.parse(t.data);if("startStaticMe"!==e.messageType)return;r(t.origin,e)}catch(t){return}}function o(t,e){return 1===t?e:t>=2?decodeURIComponent(e):null}function r(t,e){var s=n("JSH"),a=n("JSHP"),o=!0,r={transientState:"",persistentState:"",hasStorageAccess:0},S={messageType:"msaMeCached",version:2,userList:[],tilesState:r};try{var l={};i(s,S.userList,l),i(a,S.userList,l),console.log(e.useMsaSessionState),e.useMsaSessionState===!0&&(0!=S.userList.length?(S.tilesState.hasStorageAccess=2,S.tilesState.transientState=s,S.tilesState.persistentState=a):"function"==typeof document.hasStorageAccess?(o=!1,document.hasStorageAccess().then(function(n){console.log("Storage access:",n),n?(S.tilesState.hasStorageAccess=2,S.tilesState.transientState=s,S.tilesState.persistentState=a):e.sessionState&&(S.tilesState.hasStorageAccess=1,e.sessionState.transientState&&(S.tilesState.transientState=e.sessionState.transientState,i(e.sessionState.transientState,S.userList,l)),e.sessionState.persistentState&&(S.tilesState.persistentState=e.sessionState.persistentState,i(e.sessionState.persistentState,S.userList,l))),t&&c.parent.postMessage(JSON.stringify(S),t)}).catch(function(e){console.error("Error checking storage access:",e),S.error=e.message,t&&c.parent.postMessage(JSON.stringify(S),t)})):(S.tilesState.hasStorageAccess=2,S.tilesState.transientState=s,S.tilesState.persistentState=a))}catch(t){S.error=t.message}t&&o&&c.parent.postMessage(JSON.stringify(S),t)}var c=window,S="prod",l="",p="",g={None:0,SignedInToRP:1,SignedInToIDP:2,Remembered:3},u={None:0,IsWindowsSso:1},f={dev:[l,p],int:["https://login.windows-ppe.net"],prod:["https://login.microsoftonline.com","https://login.microsoft.com","https://device.login.microsoftonline.com","https://login.windows-ppe.net","https://login.windows.net","https://login.microsoftonline.de","https://login.partner.microsoftonline.cn","https://login.chinacloudapi.cn","https://login.microsoftonline.us","https://login-us.microsoftonline.com","https://logincert.microsoftonline.com"]};!function(){c.postMessage&&(c.addEventListener?c.addEventListener("message",a):c.attachEvent("message",a))}()}]));</script>
