(self.webpackJsonp_21e471d3_ec7b_4ee6_b15d_659db7eb969c=self.webpackJsonp_21e471d3_ec7b_4ee6_b15d_659db7eb969c||[]).push([["search-logger"],{MeAX:function(e,t,n){var r,i;e=n.nmd(e),r=[n("DEsW")],i=(r=>(()=>{"use strict";var i={59:(e,t)=>{var n;Object.defineProperty(t,"__esModule",{value:!0}),(n=t._BondDataType||(t._BondDataType={}))[n._BT_STOP=0]="_BT_STOP",n[n._BT_STOP_BASE=1]="_BT_STOP_BASE",n[n._BT_BOOL=2]="_BT_BOOL",n[n._BT_DOUBLE=8]="_BT_DOUBLE",n[n._BT_STRING=9]="_BT_STRING",n[n._BT_STRUCT=10]="_BT_STRUCT",n[n._BT_LIST=11]="_BT_LIST",n[n._BT_MAP=13]="_BT_MAP",n[n._BT_INT32=16]="_BT_INT32",n[n._BT_INT64=17]="_BT_INT64"},938:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=n(205),i=n(744),o=n(562);t._Utf8_GetBytes=function(e){for(var t=[],n=0;n<e.length;++n){var r=e.charCodeAt(n);r<128?t.push(r):r<2048?t.push(192|r>>6,128|63&r):r<55296||r>=57344?t.push(224|r>>12,128|r>>6&63,128|63&r):(r=65536+((1023&r)<<10|1023&e.charCodeAt(++n)),t.push(240|r>>18,128|r>>12&63,128|r>>6&63,128|63&r))}return t},t._Base64_GetString=function(e){for(var t,n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",r=[],i=e.length%3,o=0,s=e.length-i;o<s;o+=3){var a=(e[o]<<16)+(e[o+1]<<8)+e[o+2];r.push([n.charAt((t=a)>>18&63),n.charAt(t>>12&63),n.charAt(t>>6&63),n.charAt(63&t)].join(""))}switch(i){case 1:a=e[e.length-1],r.push(n.charAt(a>>2)),r.push(n.charAt(a<<4&63)),r.push("==");break;case 2:var u=(e[e.length-2]<<8)+e[e.length-1];r.push(n.charAt(u>>10)),r.push(n.charAt(u>>4&63)),r.push(n.charAt(u<<2&63)),r.push("=")}return r.join("")},t._Varint_GetBytes=function(e){for(var t=[];4294967168&e;)t.push(127&e|128),e>>>=7;return t.push(127&e),t},t._Varint64_GetBytes=function(e){for(var t=e.low,n=e.high,r=[];n||4294967168&t;)r.push(127&t|128),t=(127&n)<<25|t>>>7,n>>>=7;return r.push(127&t),r},t._Double_GetBytes=function(e){if(o.BrowserChecker._IsDataViewSupport()){var t=new DataView(new ArrayBuffer(8));t.setFloat64(0,e,!0);for(var n=[],r=0;r<8;++r)n.push(t.getUint8(r));return n}return i.FloatUtils._ConvertNumberToArray(e,!0)},t._Zigzag_EncodeZigzag32=function(e){return(e=r.Number._ToInt32(e))<<1^e>>31},t._Zigzag_EncodeZigzag64=function(e){var t=e.low,n=e.high,i=n<<1|t>>>31,o=t<<1;2147483648&n&&(i=~i,o=~o);var s=new r.UInt64("0");return s.low=o,s.high=i,s}},744:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(){}return e._ConvertNumberToArray=function(e,t){if(!e)return t?this._doubleZero:this._floatZero;var n=t?52:23,r=(1<<(t?11:8)-1)-1,i=1-r,o=r,s=e<0?1:0;e=Math.abs(e);for(var a=Math.floor(e),u=e-a,c=2*(r+2)+n,l=new Array(c),d=0;d<c;)l[d++]=0;for(d=r+2;d&&a;)l[--d]=a%2,a=Math.floor(a/2);for(d=r+1;d<c-1&&u>0;)(u*=2)>=1?(l[++d]=1,--u):l[++d]=0;for(var h=0;h<c&&!l[h];)h++;var f=r+1-h,p=h+n;if(l[p+1]){for(d=p;d>h&&(l[d]=1-l[d],!l);--d);d===h&&++f}if(f>o||a)return s?t?this._doubleNegInifinity:this._floatNegInifinity:t?this._doubleInifinity:this._floatInifinity;if(f<i)return t?this._doubleZero:this._floatZero;if(t){var v=0;for(d=0;d<20;++d)v=v<<1|l[++h];for(var g=0;d<52;++d)g=g<<1|l[++h];return[255&g,g>>8&255,g>>16&255,g>>>24,255&(v=s<<31|2147483647&(v|=f+r<<20)),v>>8&255,v>>16&255,v>>>24]}var y=0;for(d=0;d<23;++d)y=y<<1|l[++h];return[255&(y=s<<31|2147483647&(y|=f+r<<23)),y>>8&255,y>>16&255,y>>>24]},e._floatZero=[0,0,0,0],e._doubleZero=[0,0,0,0,0,0,0,0],e._floatInifinity=[0,0,128,127],e._floatNegInifinity=[0,0,128,255],e._doubleInifinity=[0,0,0,0,0,0,240,127],e._doubleNegInifinity=[0,0,0,0,0,0,240,255],e}();t.FloatUtils=n},527:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=n(205),i=function(){function e(){this._buffer=[]}return e.prototype._WriteByte=function(e){this._buffer.push(r.Number._ToByte(e))},e.prototype._Write=function(e,t,n){for(;n--;)this._WriteByte(e[t++])},e.prototype._GetBuffer=function(){return this._buffer},e}();t.MemoryStream=i},997:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=n(59);t._BondDataType=r._BondDataType;var i=n(938);t._Encoding=i;var o=n(527);t.IO=o;var s=n(205);t.Int64=s.Int64,t.UInt64=s.UInt64,t.Number=s.Number;var a=function(){function e(e){this._stream=e}return e.prototype._WriteBlob=function(e){this._stream._Write(e,0,e.length)},e.prototype._WriteBool=function(e){this._stream._WriteByte(e?1:0)},e.prototype._WriteContainerBegin=function(e,t){this._WriteUInt8(t),this._WriteUInt32(e)},e.prototype._WriteMapContainerBegin=function(e,t,n){this._WriteUInt8(t),this._WriteUInt8(n),this._WriteUInt32(e)},e.prototype._WriteDouble=function(e){var t=i._Double_GetBytes(e);this._stream._Write(t,0,t.length)},e.prototype._WriteFieldBegin=function(e,t,n){t<=5?this._stream._WriteByte(e|t<<5):t<=255?(this._stream._WriteByte(192|e),this._stream._WriteByte(t)):(this._stream._WriteByte(224|e),this._stream._WriteByte(t),this._stream._WriteByte(t>>8))},e.prototype._WriteInt32=function(e){e=i._Zigzag_EncodeZigzag32(e),this._WriteUInt32(e)},e.prototype._WriteInt64=function(e){this._WriteUInt64(i._Zigzag_EncodeZigzag64(e))},e.prototype._WriteString=function(e){if(""===e)this._WriteUInt32(0);else{var t=i._Utf8_GetBytes(e);this._WriteUInt32(t.length),this._stream._Write(t,0,t.length)}},e.prototype._WriteStructEnd=function(e){this._WriteUInt8(e?r._BondDataType._BT_STOP_BASE:r._BondDataType._BT_STOP)},e.prototype._WriteUInt32=function(e){var t=i._Varint_GetBytes(s.Number._ToUInt32(e));this._stream._Write(t,0,t.length)},e.prototype._WriteUInt64=function(e){var t=i._Varint64_GetBytes(e);this._stream._Write(t,0,t.length)},e.prototype._WriteUInt8=function(e){this._stream._WriteByte(s.Number._ToUInt8(e))},e}();t.CompactBinaryProtocolWriter=a},205:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e){this.low=0,this.high=0,this.low=parseInt(e,10),this.low<0&&(this.high=-1)}return e.prototype._Equals=function(t){var n=new e(t);return this.low===n.low&&this.high===n.high},e}();t.Int64=n;var r=function(){function e(e){this.low=0,this.high=0,this.low=parseInt(e,10)}return e.prototype._Equals=function(t){var n=new e(t);return this.low===n.low&&this.high===n.high},e}();t.UInt64=r;var i=function(){function e(){}return e._ToByte=function(e){return this._ToUInt8(e)},e._ToUInt8=function(e){return 255&e},e._ToInt32=function(e){return 2147483647&e|2147483648&e},e._ToUInt32=function(e){return 4294967295&e},e}();t.Number=i},562:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(){}return e._IsDataViewSupport=function(){return"undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},e}();t.BrowserChecker=n},995:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e){this.clockSkewRefreshDurationInMins=e,this._reset()}return e.prototype.allowRequestSending=function(){return this._isFirstRequest&&!this._clockSkewSet?(this._isFirstRequest=!1,this._allowRequestSending=!1,!0):this._allowRequestSending},e.prototype.shouldAddClockSkewHeaders=function(){return this._shouldAddClockSkewHeaders},e.prototype.getClockSkewHeaderValue=function(){return this._clockSkewHeaderValue},e.prototype.setClockSkew=function(e){this._clockSkewSet||(e?this._clockSkewHeaderValue=e:this._shouldAddClockSkewHeaders=!1,this._clockSkewSet=!0,this._allowRequestSending=!0)},e.prototype._reset=function(){var e=this;this._isFirstRequest=!0,this._clockSkewSet=!1,this._allowRequestSending=!0,this._shouldAddClockSkewHeaders=!0,this._clockSkewHeaderValue="use-collector-delta",this.clockSkewRefreshDurationInMins>0&&setTimeout((function(){return e._reset()}),6e4*this.clockSkewRefreshDurationInMins)},e}();t.default=n},176:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(){this._killedTokenDictionary={}}return e.prototype.setKillSwitchTenants=function(e,t){if(e&&t)try{var n=e.split(",");if("this-request-only"===t)return n;for(var r=1e3*parseInt(t,10),i=0;i<n.length;++i)this._killedTokenDictionary[n[i]]=Date.now()+r}catch(e){return[]}return[]},e.prototype.isTenantKilled=function(e){return void 0!==this._killedTokenDictionary[e]&&this._killedTokenDictionary[e]>Date.now()||(delete this._killedTokenDictionary[e],!1)},e}();t.default=n},832:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(){}return e.addNotificationListener=function(e){this.listeners.push(e)},e.removeNotificationListener=function(e){for(var t=this.listeners.indexOf(e);t>-1;)this.listeners.splice(t,1),t=this.listeners.indexOf(e)},e.eventsSent=function(e){for(var t=this,n=function(n){r.listeners[n].eventsSent&&setTimeout((function(){return t.listeners[n].eventsSent(e)}),0)},r=this,i=0;i<this.listeners.length;++i)n(i)},e.eventsDropped=function(e,t){for(var n=this,r=function(r){i.listeners[r].eventsDropped&&setTimeout((function(){return n.listeners[r].eventsDropped(e,t)}),0)},i=this,o=0;o<this.listeners.length;++o)r(o)},e.eventsRetrying=function(e){for(var t=this,n=function(n){r.listeners[n].eventsRetrying&&setTimeout((function(){return t.listeners[n].eventsRetrying(e)}),0)},r=this,i=0;i<this.listeners.length;++i)n(i)},e.eventsRejected=function(e,t){for(var n=this,r=function(r){i.listeners[r].eventsRejected&&setTimeout((function(){return n.listeners[r].eventsRejected(e,t)}),0)},i=this,o=0;o<this.listeners.length;++o)r(o)},e.listeners=[],e}();t.default=n},962:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=n(158),i=function(){function e(e,t){this._outboundQueue=e,this._maxNumberOfEvents=t,this._currentBatch={},this._currentNumEventsInBatch=0}return e.prototype.addEventToBatch=function(e){if(e.priority===r.AWTEventPriority.Immediate_sync){var t={};return t[e.apiKey]=[e],t}return this._currentNumEventsInBatch>=this._maxNumberOfEvents&&this.flushBatch(),void 0===this._currentBatch[e.apiKey]&&(this._currentBatch[e.apiKey]=[]),this._currentBatch[e.apiKey].push(e),this._currentNumEventsInBatch++,null},e.prototype.flushBatch=function(){this._currentNumEventsInBatch>0&&(this._outboundQueue.push(this._currentBatch),this._currentBatch={},this._currentNumEventsInBatch=0)},e.prototype.hasBatch=function(){return this._currentNumEventsInBatch>0},e}();t.default=i},190:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(){}return e.shouldRetryForStatus=function(e){return!(e>=300&&e<500&&408!==e||501===e||505===e)},e.getMillisToBackoffForRetry=function(e){var t,n=Math.floor(1200*Math.random())+2400;return t=Math.pow(4,e)*n,Math.min(t,12e4)},e}();t.default=n},124:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=n(997),i=n(158),o=n(832),s=n(685),a=2936012,u=function(){function e(){}return e.getPayloadBlob=function(e,t){var n,u=!1,c=new r.IO.MemoryStream,l=new r.CompactBinaryProtocolWriter(c);for(var d in l._WriteFieldBegin(r._BondDataType._BT_MAP,3,null),l._WriteMapContainerBegin(t,r._BondDataType._BT_STRING,r._BondDataType._BT_LIST),e)if(u)n||(n={}),n[d]=e[d],delete e[d];else if(e.hasOwnProperty(d)){l._WriteString(d);var h=e[d];l._WriteContainerBegin(1,r._BondDataType._BT_STRUCT),l._WriteFieldBegin(r._BondDataType._BT_STRING,2,null),l._WriteString("act_default_source"),l._WriteFieldBegin(r._BondDataType._BT_STRING,5,null),l._WriteString(s.newGuid()),l._WriteFieldBegin(r._BondDataType._BT_INT64,6,null),l._WriteInt64(s.numberToBondInt64(Date.now())),l._WriteFieldBegin(r._BondDataType._BT_LIST,8,null);var f=c._GetBuffer().length+1;l._WriteContainerBegin(e[d].length,r._BondDataType._BT_STRUCT);for(var p=c._GetBuffer().length-f,v=0;v<h.length;++v){var g=c._GetBuffer().length;if(this.writeEvent(h[v],l),c._GetBuffer().length-g>a)o.default.eventsRejected([h[v]],i.AWTEventsRejectedReason.SizeLimitExceeded),h.splice(v--,1),c._GetBuffer().splice(g),this._addNewDataPackageSize(h.length,c,p,f);else if(c._GetBuffer().length>a){c._GetBuffer().splice(g),n||(n={}),e[d]=h.splice(0,v),n[d]=h,this._addNewDataPackageSize(e[d].length,c,p,f),u=!0;break}}l._WriteStructEnd(!1)}return l._WriteStructEnd(!1),{payloadBlob:c._GetBuffer(),remainingRequest:n}},e._addNewDataPackageSize=function(e,t,n,i){for(var o=r._Encoding._Varint_GetBytes(r.Number._ToUInt32(e)),s=0;s<n;++s){if(!(s<o.length)){t._GetBuffer().slice(i+s,n-s);break}t._GetBuffer()[i+s]=o[s]}},e.writeEvent=function(e,t){t._WriteFieldBegin(r._BondDataType._BT_STRING,1,null),t._WriteString(e.id),t._WriteFieldBegin(r._BondDataType._BT_INT64,3,null),t._WriteInt64(s.numberToBondInt64(e.timestamp)),t._WriteFieldBegin(r._BondDataType._BT_STRING,5,null),t._WriteString(e.type),t._WriteFieldBegin(r._BondDataType._BT_STRING,6,null),t._WriteString(e.name);var n={},o=0,a={},u=0,c={},l=0,d={},h=0,f={},p=0,v={},g=0,y={},_=0;for(var m in e.properties)if(e.properties.hasOwnProperty(m))if((b=e.properties[m]).cc>0)y[m]=b,_++;else if(b.pii>0)v[m]=b,g++;else switch(b.type){case i.AWTPropertyType.String:n[m]=b.value,o++;break;case i.AWTPropertyType.Int64:a[m]=b.value,u++;break;case i.AWTPropertyType.Double:c[m]=b.value,l++;break;case i.AWTPropertyType.Boolean:d[m]=b.value,h++;break;case i.AWTPropertyType.Date:f[m]=b.value,p++}if(o)for(var m in t._WriteFieldBegin(r._BondDataType._BT_MAP,13,null),t._WriteMapContainerBegin(o,r._BondDataType._BT_STRING,r._BondDataType._BT_STRING),n)if(n.hasOwnProperty(m)){var S=n[m];t._WriteString(m),t._WriteString(S.toString())}if(g)for(var m in t._WriteFieldBegin(r._BondDataType._BT_MAP,30,null),t._WriteMapContainerBegin(g,r._BondDataType._BT_STRING,r._BondDataType._BT_STRUCT),v)if(v.hasOwnProperty(m)){var b=v[m];t._WriteString(m),t._WriteFieldBegin(r._BondDataType._BT_INT32,1,null),t._WriteInt32(1),t._WriteFieldBegin(r._BondDataType._BT_INT32,2,null),t._WriteInt32(b.pii),t._WriteFieldBegin(r._BondDataType._BT_STRING,3,null),t._WriteString(b.value.toString()),t._WriteStructEnd(!1)}if(h)for(var m in t._WriteFieldBegin(r._BondDataType._BT_MAP,31,null),t._WriteMapContainerBegin(h,r._BondDataType._BT_STRING,r._BondDataType._BT_BOOL),d)d.hasOwnProperty(m)&&(S=d[m],t._WriteString(m),t._WriteBool(S));if(p)for(var m in t._WriteFieldBegin(r._BondDataType._BT_MAP,32,null),t._WriteMapContainerBegin(p,r._BondDataType._BT_STRING,r._BondDataType._BT_INT64),f)f.hasOwnProperty(m)&&(S=f[m],t._WriteString(m),t._WriteInt64(s.numberToBondInt64(S)));if(u)for(var m in t._WriteFieldBegin(r._BondDataType._BT_MAP,33,null),t._WriteMapContainerBegin(u,r._BondDataType._BT_STRING,r._BondDataType._BT_INT64),a)a.hasOwnProperty(m)&&(S=a[m],t._WriteString(m),t._WriteInt64(s.numberToBondInt64(S)));if(l)for(var m in t._WriteFieldBegin(r._BondDataType._BT_MAP,34,null),t._WriteMapContainerBegin(l,r._BondDataType._BT_STRING,r._BondDataType._BT_DOUBLE),c)c.hasOwnProperty(m)&&(S=c[m],t._WriteString(m),t._WriteDouble(S));if(_)for(var m in t._WriteFieldBegin(r._BondDataType._BT_MAP,36,null),t._WriteMapContainerBegin(_,r._BondDataType._BT_STRING,r._BondDataType._BT_STRUCT),y)y.hasOwnProperty(m)&&(b=y[m],t._WriteString(m),t._WriteFieldBegin(r._BondDataType._BT_INT32,1,null),t._WriteInt32(b.cc),t._WriteFieldBegin(r._BondDataType._BT_STRING,2,null),t._WriteString(b.value.toString()),t._WriteStructEnd(!1));t._WriteStructEnd(!1)},e.base64Encode=function(e){return r._Encoding._Base64_GetString(e)},e}();t.default=u},942:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=n(685),i=n(832),o=n(158),s=function(){function e(){}return e.initialize=function(e){var t=this;this._sendStats=e,this._isInitalized=!0,i.default.addNotificationListener({eventsSent:function(e){t._addStat("records_sent_count",e.length,e[0].apiKey)},eventsDropped:function(e,n){switch(n){case o.AWTEventsDroppedReason.NonRetryableStatus:t._addStat("d_send_fail",e.length,e[0].apiKey),t._addStat("records_dropped_count",e.length,e[0].apiKey);break;case o.AWTEventsDroppedReason.MaxRetryLimit:t._addStat("d_retry_limit",e.length,e[0].apiKey),t._addStat("records_dropped_count",e.length,e[0].apiKey);break;case o.AWTEventsDroppedReason.QueueFull:t._addStat("d_queue_full",e.length,e[0].apiKey)}},eventsRejected:function(e,n){switch(n){case o.AWTEventsRejectedReason.InvalidEvent:t._addStat("r_inv",e.length,e[0].apiKey);break;case o.AWTEventsRejectedReason.KillSwitch:t._addStat("r_kl",e.length,e[0].apiKey);break;case o.AWTEventsRejectedReason.SizeLimitExceeded:t._addStat("r_size",e.length,e[0].apiKey)}t._addStat("r_count",e.length,e[0].apiKey)},eventsRetrying:null}),setTimeout((function(){return t.flush()}),6e4)},e.teardown=function(){this._isInitalized&&(this.flush(),this._isInitalized=!1)},e.eventReceived=function(t){e._addStat("records_received_count",1,t)},e.flush=function(){var e=this;if(this._isInitalized){for(var t in this._stats)this._stats.hasOwnProperty(t)&&this._sendStats(this._stats[t],t);this._stats={},setTimeout((function(){return e.flush()}),6e4)}},e._addStat=function(e,t,n){if(this._isInitalized&&n!==r.StatsApiKey){var i=r.getTenantId(n);this._stats[i]||(this._stats[i]={}),this._stats[i][e]?this._stats[i][e]=this._stats[i][e]+t:this._stats[i][e]=t}},e._isInitalized=!1,e._stats={},e}();t.default=s},158:(e,t)=>{var n,r,i,o,s,a;Object.defineProperty(t,"__esModule",{value:!0}),(a=t.AWTPropertyType||(t.AWTPropertyType={}))[a.Unspecified=0]="Unspecified",a[a.String=1]="String",a[a.Int64=2]="Int64",a[a.Double=3]="Double",a[a.Boolean=4]="Boolean",a[a.Date=5]="Date",(s=t.AWTPiiKind||(t.AWTPiiKind={}))[s.NotSet=0]="NotSet",s[s.DistinguishedName=1]="DistinguishedName",s[s.GenericData=2]="GenericData",s[s.IPV4Address=3]="IPV4Address",s[s.IPv6Address=4]="IPv6Address",s[s.MailSubject=5]="MailSubject",s[s.PhoneNumber=6]="PhoneNumber",s[s.QueryString=7]="QueryString",s[s.SipAddress=8]="SipAddress",s[s.SmtpAddress=9]="SmtpAddress",s[s.Identity=10]="Identity",s[s.Uri=11]="Uri",s[s.Fqdn=12]="Fqdn",s[s.IPV4AddressLegacy=13]="IPV4AddressLegacy",(o=t.AWTCustomerContentKind||(t.AWTCustomerContentKind={}))[o.NotSet=0]="NotSet",o[o.GenericContent=1]="GenericContent",(i=t.AWTEventPriority||(t.AWTEventPriority={}))[i.Low=1]="Low",i[i.Normal=2]="Normal",i[i.High=3]="High",i[i.Immediate_sync=5]="Immediate_sync",(r=t.AWTEventsDroppedReason||(t.AWTEventsDroppedReason={}))[r.NonRetryableStatus=1]="NonRetryableStatus",r[r.QueueFull=3]="QueueFull",r[r.MaxRetryLimit=4]="MaxRetryLimit",(n=t.AWTEventsRejectedReason||(t.AWTEventsRejectedReason={}))[n.InvalidEvent=1]="InvalidEvent",n[n.SizeLimitExceeded=2]="SizeLimitExceeded",n[n.KillSwitch=3]="KillSwitch"},685:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=n(205),i=n(158),o=/[xy]/g,s=null;t.EventNameAndTypeRegex=/^[a-zA-Z]([a-zA-Z0-9]|_){2,98}[a-zA-Z0-9]$/,t.EventNameDotRegex=/\./g,t.PropertyNameRegex=/^[a-zA-Z](([a-zA-Z0-9|_|\.]){0,98}[a-zA-Z0-9])?$/,t.StatsApiKey="a387cfcf60114a43a7699f9fbb49289e-9bceb9fe-1c06-460f-96c5-6a0b247358bc-7238";var a=s,u=s,c=s;function l(e){return"string"==typeof e}function d(e){return"number"==typeof e}function h(e){return"boolean"==typeof e}function f(e){return e instanceof Date}function p(e){return 1e4*(e+621355968e5)}function v(){return!("undefined"==typeof navigator||!navigator.product)&&"ReactNative"===navigator.product}function g(){return"object"==typeof self&&"ServiceWorkerGlobalScope"===self.constructor.name}function y(e){return e<10?"0"+e:e.toString()}function _(e){return void 0===e||e===s||""===e}t.numberToBondInt64=function(e){var t=new r.Int64("0");return t.low=4294967295&e,t.high=Math.floor(e/4294967296),t},t.newGuid=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(o,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))},t.isString=l,t.isNumber=d,t.isBoolean=h,t.isDate=f,t.msToTicks=p,t.getTenantId=function(e){var t=e.indexOf("-");return t>-1?e.substring(0,t):""},t.isBeaconsSupported=function(){return a===s&&(a="undefined"!=typeof navigator&&Boolean(navigator.sendBeacon)),a},t.isUint8ArrayAvailable=function(){return u===s&&(u="undefined"!=typeof Uint8Array&&!function(){if("undefined"!=typeof navigator&&navigator.userAgent){var e=navigator.userAgent.toLowerCase();if((e.indexOf("safari")>=0||e.indexOf("firefox")>=0)&&e.indexOf("chrome")<0)return!0}return!1}()&&!v()),u},t.isPriority=function(e){return!(!d(e)||!(e>=1&&e<=3||5===e))},t.sanitizeProperty=function(e,n){return!t.PropertyNameRegex.test(e)||_(n)?s:(_(n.value)&&(n={value:n,type:i.AWTPropertyType.Unspecified}),n.type=function(e,t){switch(t=function(e){return!!(d(e)&&e>=0&&e<=4)}(t)?t:i.AWTPropertyType.Unspecified,t){case i.AWTPropertyType.Unspecified:return function(e){switch(typeof e){case"string":return i.AWTPropertyType.String;case"boolean":return i.AWTPropertyType.Boolean;case"number":return i.AWTPropertyType.Double;case"object":return f(e)?i.AWTPropertyType.Date:s}return s}(e);case i.AWTPropertyType.String:return l(e)?t:s;case i.AWTPropertyType.Boolean:return h(e)?t:s;case i.AWTPropertyType.Date:return f(e)&&NaN!==e.getTime()?t:s;case i.AWTPropertyType.Int64:return d(e)&&e%1==0?t:s;case i.AWTPropertyType.Double:return d(e)?t:s}return s}(n.value,n.type),n.type?(f(n.value)&&(n.value=p(n.value.getTime())),n.pii>0&&n.cc>0?s:n.pii?d(r=n.pii)&&r>=0&&r<=13?n:s:n.cc?function(e){return!!(d(e)&&e>=0&&e<=1)}(n.cc)?n:s:n):s);var r},t.getISOString=function(e){return e.getUTCFullYear()+"-"+y(e.getUTCMonth()+1)+"-"+y(e.getUTCDate())+"T"+y(e.getUTCHours())+":"+y(e.getUTCMinutes())+":"+y(e.getUTCSeconds())+"."+((t=e.getUTCMilliseconds())<10?"00"+t:t<100?"0"+t:t.toString())+"Z";var t},t.useXDomainRequest=function(){if(c===s){var e=new XMLHttpRequest;c=void 0===e.withCredentials&&"undefined"!=typeof XDomainRequest}return c},t.useFetchRequest=function(){return v()||g()},t.isReactNative=v,t.isServiceWorkerGlobalScope=g},966:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=n(685),i="MicrosoftApplicationsTelemetryDeviceId",o="MicrosoftApplicationsTelemetryFirstLaunchTime",s="MSIE",a="Chrome",u="Firefox",c="Safari",l="Edge",d="Electron",h="SkypeShell",f="PhantomJS",p="Windows",v="Mac OS X",g="Android",y=/(windows|win32)/i,_=/ arm;/i,m=/windows\sphone\s\d+\.\d+/i,S=/(macintosh|mac os x)/i,b=/(iPad|iPhone|iPod)(?=.*like Mac OS X)/i,T=/(linux|joli|[kxln]?ubuntu|debian|[open]*suse|gentoo|arch|slackware|fedora|mandriva|centos|pclinuxos|redhat|zenwalk)/i,w=/android/i,I=/CrOS/i,C={5.1:"XP","6.0":"Vista",6.1:"7",6.2:"8",6.3:"8.1","10.0":"10"},E="([\\d,.]+)",A="Unknown",P="undefined",k=function(){function e(){}return e.addPropertyStorageOverride=function(e){return!!e&&(this._propertyStorage=e,!0)},e.autoCollect=function(e,t,n){if(this._semanticContext=e,this._disableCookies=t,this._autoCollect(),n||typeof navigator===P||(n=navigator.userAgent||""),this._autoCollectFromUserAgent(n),this._disableCookies&&!this._propertyStorage)return this._deleteCookie(i),void this._deleteCookie(o);(this._propertyStorage||this._areCookiesAvailable&&!this._disableCookies)&&this._autoCollectDeviceId()},e.checkAndSaveDeviceId=function(e){if(e){var t=this._getData(i),n=this._getData(o);t!==e&&(n=r.getISOString(new Date)),this._saveData(i,e),this._saveData(o,n),this._setFirstLaunchTime(n)}},e._autoCollectDeviceId=function(){var e=this._getData(i);e||(e=r.newGuid()),this._semanticContext.setDeviceId(e)},e._autoCollect=function(){typeof document!==P&&document.documentElement&&this._semanticContext.setAppLanguage(document.documentElement.lang),typeof navigator!==P&&this._semanticContext.setUserLanguage(navigator.userLanguage||navigator.language);var e=(new Date).getTimezoneOffset(),t=e%60,n=(e-t)/60,r="+";n>0&&(r="-"),n=Math.abs(n),t=Math.abs(t),this._semanticContext.setUserTimeZone(r+(n<10?"0"+n:n.toString())+":"+(t<10?"0"+t:t.toString()))},e._autoCollectFromUserAgent=function(e){if(e){var t=this._getBrowserName(e);this._semanticContext.setDeviceBrowserName(t),this._semanticContext.setDeviceBrowserVersion(this._getBrowserVersion(e,t));var n=this._getOsName(e);this._semanticContext.setDeviceOsName(n),this._semanticContext.setDeviceOsVersion(this._getOsVersion(e,n))}},e._getBrowserName=function(e){return this._userAgentContainsString("OPR/",e)?"Opera":this._userAgentContainsString(f,e)?f:this._userAgentContainsString(l,e)||this._userAgentContainsString("Edg",e)?l:this._userAgentContainsString(d,e)?d:this._userAgentContainsString(a,e)?a:this._userAgentContainsString("Trident",e)?s:this._userAgentContainsString(u,e)?u:this._userAgentContainsString(c,e)?c:this._userAgentContainsString(h,e)?h:A},e._setFirstLaunchTime=function(e){if(!isNaN(e)){var t=new Date;t.setTime(parseInt(e,10)),e=r.getISOString(t)}this.firstLaunchTime=e},e._userAgentContainsString=function(e,t){return t.indexOf(e)>-1},e._getBrowserVersion=function(e,t){if(t===s)return this._getIeVersion(e);if(t===l){var n=this._getOtherVersion(t,e);return n===A?this._getOtherVersion("Edg",e):n}return this._getOtherVersion(t,e)},e._getIeVersion=function(e){var t=e.match(new RegExp(s+" "+E));if(t)return t[1];var n=e.match(new RegExp("rv:"+E));return n?n[1]:void 0},e._getOtherVersion=function(e,t){e===c&&(e="Version");var n=t.match(new RegExp(e+"/"+E));return n?n[1]:A},e._getOsName=function(e){return e.match(m)?"Windows Phone":e.match(_)?"Windows RT":e.match(b)?"iOS":e.match(w)?g:e.match(T)?"Linux":e.match(S)?v:e.match(y)?p:e.match(I)?"Chrome OS":A},e._getOsVersion=function(e,t){return t===p?this._getGenericOsVersion(e,"Windows NT"):t===g?this._getGenericOsVersion(e,t):t===v?this._getMacOsxVersion(e):A},e._getGenericOsVersion=function(e,t){var n=e.match(new RegExp(t+" "+E));return n?C[n[1]]?C[n[1]]:n[1]:A},e._getMacOsxVersion=function(e){var t=e.match(new RegExp(v+" ([\\d,_,.]+)"));if(t){var n=t[1].replace(/_/g,".");if(n){var r=this._getDelimiter(n);return r?n.split(r)[0]:n}}return A},e._getDelimiter=function(e){return e.indexOf(".")>-1?".":e.indexOf("_")>-1?"_":null},e._saveData=function(e,t){if(this._propertyStorage)this._propertyStorage.setProperty(e,t);else if(this._areCookiesAvailable){var n=new Date;n.setTime(n.getTime()+31536e6);var r="expires="+n.toUTCString();document.cookie=e+"="+t+"; "+r}},e._getData=function(e){if(this._propertyStorage)return this._propertyStorage.getProperty(e)||"";if(this._areCookiesAvailable){e+="=";for(var t=document.cookie.split(";"),n=0;n<t.length;n++){for(var r=t[n],i=0;" "===r.charAt(i);)i++;if(0===(r=r.substring(i)).indexOf(e))return r.substring(e.length,r.length)}}return""},e._deleteCookie=function(e){this._areCookiesAvailable&&(document.cookie=e+"=;expires=Thu, 01 Jan 1970 00:00:01 GMT;")},e._disableCookies=!1,e._areCookiesAvailable=typeof document!==P&&typeof document.cookie!==P,e}();t.default=k},585:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=n(685),i=n(158),o=function(){function e(e){this._event={name:"",properties:{}},e&&this.setName(e)}return e.prototype.setName=function(e){this._event.name=e},e.prototype.getName=function(){return this._event.name},e.prototype.setType=function(e){this._event.type=e},e.prototype.getType=function(){return this._event.type},e.prototype.setTimestamp=function(e){this._event.timestamp=e},e.prototype.getTimestamp=function(){return this._event.timestamp},e.prototype.setEventPriority=function(e){this._event.priority=e},e.prototype.getEventPriority=function(){return this._event.priority},e.prototype.setProperty=function(e,t,n){void 0===n&&(n=i.AWTPropertyType.Unspecified);var o={value:t,type:n,pii:i.AWTPiiKind.NotSet,cc:i.AWTCustomerContentKind.NotSet};null!==(o=r.sanitizeProperty(e,o))?this._event.properties[e]=o:delete this._event.properties[e]},e.prototype.setPropertyWithPii=function(e,t,n,o){void 0===o&&(o=i.AWTPropertyType.Unspecified);var s={value:t,type:o,pii:n,cc:i.AWTCustomerContentKind.NotSet};null!==(s=r.sanitizeProperty(e,s))?this._event.properties[e]=s:delete this._event.properties[e]},e.prototype.setPropertyWithCustomerContent=function(e,t,n,o){void 0===o&&(o=i.AWTPropertyType.Unspecified);var s={value:t,type:o,pii:i.AWTPiiKind.NotSet,cc:n};null!==(s=r.sanitizeProperty(e,s))?this._event.properties[e]=s:delete this._event.properties[e]},e.prototype.getPropertyMap=function(){return this._event.properties},e.prototype.getEvent=function(){return this._event},e}();t.default=o},283:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=n(158),i=n(124),o=n(190),s=n(176),a=n(995),u=n(150),c=n(685),l=n(832),d=n(72),h="POST",f=function(){function e(e,t,n,r,i){var o=this;this._requestQueue=e,this._queueManager=n,this._httpInterface=r,this._urlString="?qsp=true&content-type=application%2Fbond-compact-binary&client-id=NO_AUTH&sdk-version="+u.FullVersionString,this._killSwitch=new s.default,this._paused=!1,this._useBeacons=!1,this._activeConnections=0,this._clockSkewManager=new a.default(i),c.isUint8ArrayAvailable()||(this._urlString+="&content-encoding=base64"),this._urlString=t+this._urlString,this._httpInterface||(this._useBeacons=!c.isReactNative(),this._httpInterface={sendPOST:function(e,t,n,r,i,s){try{if(c.useFetchRequest())fetch(e,{body:t,method:h}).then((function(e){var t={};e.headers&&e.headers.forEach((function(e,n){t[n]=e})),i(e.status,t)})).catch((function(e){r(0,{})}));else if(c.useXDomainRequest()){var a=new XDomainRequest;a.open(h,e),a.onload=function(){i(200,null)},a.onerror=function(){r(400,null)},a.ontimeout=function(){n(500,null)},a.send(t)}else{var u=new XMLHttpRequest;u.open(h,e,!s),u.onload=function(){i(u.status,o._convertAllHeadersToMap(u.getAllResponseHeaders()))},u.onerror=function(){r(u.status,o._convertAllHeadersToMap(u.getAllResponseHeaders()))},u.ontimeout=function(){n(u.status,o._convertAllHeadersToMap(u.getAllResponseHeaders()))},u.send(t)}}catch(e){r(400,null)}}})}return e.prototype.hasIdleConnection=function(){return this._activeConnections<2},e.prototype.sendQueuedRequests=function(){for(;this.hasIdleConnection()&&!this._paused&&this._requestQueue.length>0&&this._clockSkewManager.allowRequestSending();)this._activeConnections++,this._sendRequest(this._requestQueue.shift(),0,!1);this.hasIdleConnection()&&d.default.scheduleTimer()},e.prototype.isCompletelyIdle=function(){return 0===this._activeConnections},e.prototype.teardown=function(){for(;this._requestQueue.length>0;)this._sendRequest(this._requestQueue.shift(),0,!0)},e.prototype.pause=function(){this._paused=!0},e.prototype.resume=function(){this._paused=!1,this.sendQueuedRequests()},e.prototype.removeQueuedRequests=function(){this._requestQueue.length=0},e.prototype.sendSynchronousRequest=function(e,t){this._paused&&(e[t][0].priority=r.AWTEventPriority.High),this._activeConnections++,this._sendRequest(e,0,!1,!0)},e.prototype._sendRequest=function(e,t,n,o){var s=this;void 0===o&&(o=!1);try{if(this._paused)return this._activeConnections--,void this._queueManager.addBackRequest(e);var a=0,u="";for(var d in e)e.hasOwnProperty(d)&&(this._killSwitch.isTenantKilled(d)?(l.default.eventsRejected(e[d],r.AWTEventsRejectedReason.KillSwitch),delete e[d]):(u.length>0&&(u+=","),u+=d,a++));if(a>0){var h=i.default.getPayloadBlob(e,a);h.remainingRequest&&this._requestQueue.push(h.remainingRequest);var f,p=this._urlString+"&x-apikey="+u+"&client-time-epoch-millis="+Date.now().toString();for(var d in this._clockSkewManager.shouldAddClockSkewHeaders()&&(p=p+"&time-delta-to-apply-millis="+this._clockSkewManager.getClockSkewHeaderValue()),f=c.isUint8ArrayAvailable()?new Uint8Array(h.payloadBlob):i.default.base64Encode(h.payloadBlob),e)if(e.hasOwnProperty(d))for(var v=0;v<e[d].length;++v)e[d][v].sendAttempt>0?e[d][v].sendAttempt++:e[d][v].sendAttempt=1;if(this._useBeacons&&n&&c.isBeaconsSupported()&&navigator.sendBeacon(p,f))return;this._httpInterface.sendPOST(p,f,(function(r,i){s._retryRequestIfNeeded(r,i,e,a,u,t,n,o)}),(function(r,i){s._retryRequestIfNeeded(r,i,e,a,u,t,n,o)}),(function(r,i){s._retryRequestIfNeeded(r,i,e,a,u,t,n,o)}),n||o)}else n||this._handleRequestFinished(!1,{},n,o)}catch(e){this._handleRequestFinished(!1,{},n,o)}},e.prototype._retryRequestIfNeeded=function(e,t,n,i,s,a,u,c){var h=this,f=!0;if(void 0!==e){if(t){var p=this._killSwitch.setKillSwitchTenants(t["kill-tokens"],t["kill-duration-seconds"]);this._clockSkewManager.setClockSkew(t["time-delta-millis"]);for(var v=0;v<p.length;++v)l.default.eventsRejected(n[p[v]],r.AWTEventsRejectedReason.KillSwitch),delete n[p[v]],i--}else this._clockSkewManager.setClockSkew(null);if(200===e)return void this._handleRequestFinished(!0,n,u,c);(!o.default.shouldRetryForStatus(e)||i<=0)&&(f=!1)}if(f)if(c)this._activeConnections--,n[s][0].priority=r.AWTEventPriority.High,this._queueManager.addBackRequest(n);else if(a<1){for(var g in n)n.hasOwnProperty(g)&&l.default.eventsRetrying(n[g]);setTimeout((function(){return h._sendRequest(n,a+1,!1)}),o.default.getMillisToBackoffForRetry(a))}else this._activeConnections--,d.default.backOffTransmission(),this._queueManager.addBackRequest(n);else this._handleRequestFinished(!1,n,u,c)},e.prototype._handleRequestFinished=function(e,t,n,i){for(var o in e&&d.default.clearBackOff(),t)t.hasOwnProperty(o)&&(e?l.default.eventsSent(t[o]):l.default.eventsDropped(t[o],r.AWTEventsDroppedReason.NonRetryableStatus));this._activeConnections--,i||n||this.sendQueuedRequests()},e.prototype._convertAllHeadersToMap=function(e){var t={};if(e)for(var n=e.split("\n"),r=0;r<n.length;++r){var i=n[r].split(": ");t[i[0]]=i[1]}return t},e}();t.default=f},663:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=n(158),i=n(936),o=n(446),s=n(782),a=n(72),u=n(832),c=n(966),l=function(){function e(){}return e.initialize=function(e,t){if(void 0===t&&(t={}),!this._isInitialized)return this._isInitialized=!0,o.default.defaultTenantToken=e,this._overrideValuesFromConfig(t),this._config.disableCookiesUsage&&!this._config.propertyStorageOverride&&(o.default.sessionEnabled=!1),c.default.addPropertyStorageOverride(this._config.propertyStorageOverride),c.default.autoCollect(o.default.semanticContext,this._config.disableCookiesUsage,this._config.userAgent),a.default.initialize(this._config),o.default.loggingEnabled=!0,this._config.enableAutoUserSession&&(this.getLogger().logSession(i.AWTSessionState.Started),window.addEventListener("beforeunload",this.flushAndTeardown)),this.getLogger()},e.getSemanticContext=function(){return o.default.semanticContext},e.flush=function(e){this._isInitialized&&!this._isDestroyed&&a.default.flush(e)},e.flushAndTeardown=function(){this._isInitialized&&!this._isDestroyed&&(this._config.enableAutoUserSession&&this.getLogger().logSession(i.AWTSessionState.Ended),a.default.flushAndTeardown(),o.default.loggingEnabled=!1,this._isDestroyed=!0)},e.pauseTransmission=function(){this._isInitialized&&!this._isDestroyed&&a.default.pauseTransmission()},e.resumeTransmision=function(){this._isInitialized&&!this._isDestroyed&&a.default.resumeTransmision()},e.setTransmitProfile=function(e){this._isInitialized&&!this._isDestroyed&&a.default.setTransmitProfile(e)},e.loadTransmitProfiles=function(e){this._isInitialized&&!this._isDestroyed&&a.default.loadTransmitProfiles(e)},e.setContext=function(e,t,n){void 0===n&&(n=r.AWTPropertyType.Unspecified),o.default.logManagerContext.setProperty(e,t,n)},e.setContextWithPii=function(e,t,n,i){void 0===i&&(i=r.AWTPropertyType.Unspecified),o.default.logManagerContext.setPropertyWithPii(e,t,n,i)},e.setContextWithCustomerContent=function(e,t,n,i){void 0===i&&(i=r.AWTPropertyType.Unspecified),o.default.logManagerContext.setPropertyWithCustomerContent(e,t,n,i)},e.getLogger=function(e){var t=e;return t&&t!==o.default.defaultTenantToken||(t=""),this._loggers[t]||(this._loggers[t]=new s.default(t)),this._loggers[t]},e.addNotificationListener=function(e){u.default.addNotificationListener(e)},e.removeNotificationListener=function(e){u.default.removeNotificationListener(e)},e._overrideValuesFromConfig=function(e){e.collectorUri&&(this._config.collectorUri=e.collectorUri),e.cacheMemorySizeLimitInNumberOfEvents>0&&(this._config.cacheMemorySizeLimitInNumberOfEvents=e.cacheMemorySizeLimitInNumberOfEvents),e.httpXHROverride&&e.httpXHROverride.sendPOST&&(this._config.httpXHROverride=e.httpXHROverride),e.propertyStorageOverride&&e.propertyStorageOverride.getProperty&&e.propertyStorageOverride.setProperty&&(this._config.propertyStorageOverride=e.propertyStorageOverride),e.userAgent&&(this._config.userAgent=e.userAgent),e.disableCookiesUsage&&(this._config.disableCookiesUsage=e.disableCookiesUsage),e.canSendStatEvent&&(this._config.canSendStatEvent=e.canSendStatEvent),e.enableAutoUserSession&&"undefined"!=typeof window&&window.addEventListener&&(this._config.enableAutoUserSession=e.enableAutoUserSession),e.clockSkewRefreshDurationInMins>0&&(this._config.clockSkewRefreshDurationInMins=e.clockSkewRefreshDurationInMins)},e._loggers={},e._isInitialized=!1,e._isDestroyed=!1,e._config={collectorUri:"https://browser.pipe.aria.microsoft.com/Collector/3.0/",cacheMemorySizeLimitInNumberOfEvents:1e4,disableCookiesUsage:!1,canSendStatEvent:function(e){return!0},clockSkewRefreshDurationInMins:0},e}();t.default=l},446:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=n(585),i=n(871),o=function(){function e(){}return e.logManagerContext=new r.default,e.sessionEnabled=!0,e.loggingEnabled=!1,e.defaultTenantToken="",e.semanticContext=new i.default(!0,e.logManagerContext),e}();t.default=o},782:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=n(158),i=n(936),o=n(585),s=n(685),a=n(942),u=n(832),c=n(72),l=n(446),d=n(150),h=n(871),f=n(966),p=function(){function e(e){this._apiKey=e,this._contextProperties=new o.default,this._semanticContext=new h.default(!1,this._contextProperties),this._sessionStartTime=0,this._createInitId()}return e.prototype.setContext=function(e,t,n){void 0===n&&(n=r.AWTPropertyType.Unspecified),this._contextProperties.setProperty(e,t,n)},e.prototype.setContextWithPii=function(e,t,n,i){void 0===i&&(i=r.AWTPropertyType.Unspecified),this._contextProperties.setPropertyWithPii(e,t,n,i)},e.prototype.setContextWithCustomerContent=function(e,t,n,i){void 0===i&&(i=r.AWTPropertyType.Unspecified),this._contextProperties.setPropertyWithCustomerContent(e,t,n,i)},e.prototype.getSemanticContext=function(){return this._semanticContext},e.prototype.logEvent=function(t){if(l.default.loggingEnabled){this._apiKey||(this._apiKey=l.default.defaultTenantToken,this._createInitId());var n=!0;s.isString(t)?t={name:t}:t instanceof o.default&&(t=t.getEvent(),n=!1),a.default.eventReceived(this._apiKey),e._logEvent(e._getInternalEvent(t,this._apiKey,n),this._contextProperties)}},e.prototype.logSession=function(t,n){if(l.default.sessionEnabled){var o={name:"session",type:"session",properties:{}};if(e._addPropertiesToEvent(o,n),o.priority=r.AWTEventPriority.High,t===i.AWTSessionState.Started){if(this._sessionStartTime>0)return;this._sessionStartTime=(new Date).getTime(),this._sessionId=s.newGuid(),this.setContext("Session.Id",this._sessionId),o.properties["Session.State"]="Started"}else{if(t!==i.AWTSessionState.Ended)return;if(0===this._sessionStartTime)return;var a=Math.floor(((new Date).getTime()-this._sessionStartTime)/1e3);o.properties["Session.Id"]=this._sessionId,o.properties["Session.State"]="Ended",o.properties["Session.Duration"]=a.toString(),o.properties["Session.DurationBucket"]=e._getSessionDurationFromTime(a),this._sessionStartTime=0,this.setContext("Session.Id",null),this._sessionId=void 0}o.properties["Session.FirstLaunchTime"]=f.default.firstLaunchTime,this.logEvent(o)}},e.prototype.getSessionId=function(){return this._sessionId},e.prototype.logFailure=function(t,n,i,o,s){if(t&&n){var a={name:"failure",type:"failure",properties:{}};e._addPropertiesToEvent(a,s),a.properties["Failure.Signature"]=t,a.properties["Failure.Detail"]=n,i&&(a.properties["Failure.Category"]=i),o&&(a.properties["Failure.Id"]=o),a.priority=r.AWTEventPriority.High,this.logEvent(a)}},e.prototype.logPageView=function(t,n,r,i,o,s){if(t&&n){var a={name:"pageview",type:"pageview",properties:{}};e._addPropertiesToEvent(a,s),a.properties["PageView.Id"]=t,a.properties["PageView.Name"]=n,r&&(a.properties["PageView.Category"]=r),i&&(a.properties["PageView.Uri"]=i),o&&(a.properties["PageView.ReferrerUri"]=o),this.logEvent(a)}},e.prototype._createInitId=function(){!e._initIdMap[this._apiKey]&&this._apiKey&&(e._initIdMap[this._apiKey]=s.newGuid())},e._addPropertiesToEvent=function(e,t){if(t)for(var n in t instanceof o.default&&(t=t.getEvent()),t.name&&(e.name=t.name),t.priority&&(e.priority=t.priority),t.properties)t.properties.hasOwnProperty(n)&&(e.properties[n]=t.properties[n])},e._getSessionDurationFromTime=function(e){return e<0?"Undefined":e<=3?"UpTo3Sec":e<=10?"UpTo10Sec":e<=30?"UpTo30Sec":e<=60?"UpTo60Sec":e<=180?"UpTo3Min":e<=600?"UpTo10Min":e<=1800?"UpTo30Min":"Above30Min"},e._logEvent=function(e,t){e.name&&s.isString(e.name)?(e.name=e.name.toLowerCase(),e.name=e.name.replace(s.EventNameDotRegex,"_"),e.type&&s.isString(e.type)?e.type=e.type.toLowerCase():e.type="custom",s.EventNameAndTypeRegex.test(e.name)&&s.EventNameAndTypeRegex.test(e.type)?((!s.isNumber(e.timestamp)||e.timestamp<0)&&(e.timestamp=(new Date).getTime()),e.properties||(e.properties={}),this._addContextIfAbsent(e,t.getPropertyMap()),this._addContextIfAbsent(e,l.default.logManagerContext.getPropertyMap()),this._setDefaultProperty(e,"EventInfo.InitId",this._getInitId(e.apiKey)),this._setDefaultProperty(e,"EventInfo.Sequence",this._getSequenceId(e.apiKey)),this._setDefaultProperty(e,"EventInfo.SdkVersion",d.FullVersionString),this._setDefaultProperty(e,"EventInfo.Name",e.name),this._setDefaultProperty(e,"EventInfo.Time",new Date(e.timestamp).toISOString()),s.isPriority(e.priority)||(e.priority=r.AWTEventPriority.Normal),this._sendEvent(e)):u.default.eventsRejected([e],r.AWTEventsRejectedReason.InvalidEvent)):u.default.eventsRejected([e],r.AWTEventsRejectedReason.InvalidEvent)},e._addContextIfAbsent=function(e,t){if(t)for(var n in t)t.hasOwnProperty(n)&&(e.properties[n]||(e.properties[n]=t[n]))},e._setDefaultProperty=function(e,t,n){e.properties[t]={value:n,pii:r.AWTPiiKind.NotSet,type:r.AWTPropertyType.String}},e._sendEvent=function(e){c.default.sendEvent(e)},e._getInternalEvent=function(e,t,n){if(e.properties=e.properties||{},n)for(var r in e.properties)e.properties.hasOwnProperty(r)&&(e.properties[r]=s.sanitizeProperty(r,e.properties[r]),null===e.properties[r]&&delete e.properties[r]);var i=e;return i.id=s.newGuid(),i.apiKey=t,i},e._getInitId=function(t){return e._initIdMap[t]},e._getSequenceId=function(t){return void 0===e._sequenceIdMap[t]&&(e._sequenceIdMap[t]=0),(++e._sequenceIdMap[t]).toString()},e._sequenceIdMap={},e._initIdMap={},e}();t.default=p},564:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=n(158),i=n(283),o=n(72),s=n(962),a=n(832),u=n(685),c=function(){function e(e,t,n,o){this._queueSizeLimit=t,this._isCurrentlyUploadingNow=!1,this._uploadNowQueue=[],this._shouldDropEventsOnPause=!1,this._paused=!1,this._queueSize=0,this._outboundQueue=[],this._inboundQueues={},this._inboundQueues[r.AWTEventPriority.High]=[],this._inboundQueues[r.AWTEventPriority.Normal]=[],this._inboundQueues[r.AWTEventPriority.Low]=[],this._addEmptyQueues(),this._batcher=new s.default(this._outboundQueue,500),this._httpManager=new i.default(this._outboundQueue,e,this,n,o)}return e.prototype.addEvent=function(e){u.isPriority(e.priority)||(e.priority=r.AWTEventPriority.Normal),e.priority===r.AWTEventPriority.Immediate_sync?this._httpManager.sendSynchronousRequest(this._batcher.addEventToBatch(e),e.apiKey):this._queueSize<this._queueSizeLimit||this._dropEventWithPriorityOrLess(e.priority)?this._addEventToProperQueue(e):a.default.eventsDropped([e],r.AWTEventsDroppedReason.QueueFull)},e.prototype.sendEventsForPriorityAndAbove=function(e){this._batchEvents(e),this._httpManager.sendQueuedRequests()},e.prototype.hasEvents=function(){return(this._inboundQueues[r.AWTEventPriority.High][0].length>0||this._inboundQueues[r.AWTEventPriority.Normal][0].length>0||this._inboundQueues[r.AWTEventPriority.Low][0].length>0||this._batcher.hasBatch())&&this._httpManager.hasIdleConnection()},e.prototype.addBackRequest=function(e){if(!this._paused||!this._shouldDropEventsOnPause){for(var t in e)if(e.hasOwnProperty(t))for(var n=0;n<e[t].length;++n)e[t][n].sendAttempt<6?this.addEvent(e[t][n]):a.default.eventsDropped([e[t][n]],r.AWTEventsDroppedReason.MaxRetryLimit);o.default.scheduleTimer()}},e.prototype.teardown=function(){this._paused||(this._batchEvents(r.AWTEventPriority.Low),this._httpManager.teardown())},e.prototype.uploadNow=function(e){var t=this;this._addEmptyQueues(),this._isCurrentlyUploadingNow?this._uploadNowQueue.push(e):(this._isCurrentlyUploadingNow=!0,setTimeout((function(){return t._uploadNow(e)}),0))},e.prototype.pauseTransmission=function(){this._paused=!0,this._httpManager.pause(),this.shouldDropEventsOnPause&&(this._queueSize-=this._inboundQueues[r.AWTEventPriority.High][0].length+this._inboundQueues[r.AWTEventPriority.Normal][0].length+this._inboundQueues[r.AWTEventPriority.Low][0].length,this._inboundQueues[r.AWTEventPriority.High][0]=[],this._inboundQueues[r.AWTEventPriority.Normal][0]=[],this._inboundQueues[r.AWTEventPriority.Low][0]=[],this._httpManager.removeQueuedRequests())},e.prototype.resumeTransmission=function(){this._paused=!1,this._httpManager.resume()},e.prototype.shouldDropEventsOnPause=function(e){this._shouldDropEventsOnPause=e},e.prototype._removeFirstQueues=function(){this._inboundQueues[r.AWTEventPriority.High].shift(),this._inboundQueues[r.AWTEventPriority.Normal].shift(),this._inboundQueues[r.AWTEventPriority.Low].shift()},e.prototype._addEmptyQueues=function(){this._inboundQueues[r.AWTEventPriority.High].push([]),this._inboundQueues[r.AWTEventPriority.Normal].push([]),this._inboundQueues[r.AWTEventPriority.Low].push([])},e.prototype._addEventToProperQueue=function(e){this._paused&&this._shouldDropEventsOnPause||(this._queueSize++,this._inboundQueues[e.priority][this._inboundQueues[e.priority].length-1].push(e))},e.prototype._dropEventWithPriorityOrLess=function(e){for(var t=r.AWTEventPriority.Low;t<=e;){if(this._inboundQueues[t][this._inboundQueues[t].length-1].length>0)return a.default.eventsDropped([this._inboundQueues[t][this._inboundQueues[t].length-1].shift()],r.AWTEventsDroppedReason.QueueFull),!0;t++}return!1},e.prototype._batchEvents=function(e){for(var t=r.AWTEventPriority.High;t>=e;){for(;this._inboundQueues[t][0].length>0;){var n=this._inboundQueues[t][0].pop();this._queueSize--,this._batcher.addEventToBatch(n)}t--}this._batcher.flushBatch()},e.prototype._uploadNow=function(e){var t=this;this.hasEvents()&&this.sendEventsForPriorityAndAbove(r.AWTEventPriority.Low),this._checkOutboundQueueEmptyAndSent((function(){t._removeFirstQueues(),null!=e&&e(),t._uploadNowQueue.length>0?setTimeout((function(){return t._uploadNow(t._uploadNowQueue.shift())}),0):(t._isCurrentlyUploadingNow=!1,t.hasEvents()&&o.default.scheduleTimer())}))},e.prototype._checkOutboundQueueEmptyAndSent=function(e){var t=this;this._httpManager.isCompletelyIdle()?e():setTimeout((function(){return t._checkOutboundQueueEmptyAndSent(e)}),250)},e}();t.default=c},871:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=n(966),i=n(158),o=n(936),s="UserInfo.IdType",a=function(){function e(e,t){this._allowDeviceFields=e,this._properties=t}return e.prototype.setAppId=function(e){this._addContext("AppInfo.Id",e)},e.prototype.setAppVersion=function(e){this._addContext("AppInfo.Version",e)},e.prototype.setAppLanguage=function(e){this._addContext("AppInfo.Language",e)},e.prototype.setDeviceId=function(e){this._allowDeviceFields&&(r.default.checkAndSaveDeviceId(e),this._addContext("DeviceInfo.Id",e))},e.prototype.setDeviceOsName=function(e){this._allowDeviceFields&&this._addContext("DeviceInfo.OsName",e)},e.prototype.setDeviceOsVersion=function(e){this._allowDeviceFields&&this._addContext("DeviceInfo.OsVersion",e)},e.prototype.setDeviceBrowserName=function(e){this._allowDeviceFields&&this._addContext("DeviceInfo.BrowserName",e)},e.prototype.setDeviceBrowserVersion=function(e){this._allowDeviceFields&&this._addContext("DeviceInfo.BrowserVersion",e)},e.prototype.setDeviceMake=function(e){this._allowDeviceFields&&this._addContext("DeviceInfo.Make",e)},e.prototype.setDeviceModel=function(e){this._allowDeviceFields&&this._addContext("DeviceInfo.Model",e)},e.prototype.setUserId=function(e,t,n){if(!isNaN(n)&&null!==n&&n>=0&&n<=12)this._addContext(s,n.toString());else{var r=void 0;switch(t){case i.AWTPiiKind.SipAddress:r=o.AWTUserIdType.SipAddress;break;case i.AWTPiiKind.PhoneNumber:r=o.AWTUserIdType.PhoneNumber;break;case i.AWTPiiKind.SmtpAddress:r=o.AWTUserIdType.EmailAddress;break;default:r=o.AWTUserIdType.Unknown}this._addContext(s,r.toString())}if(isNaN(t)||null===t||t===i.AWTPiiKind.NotSet||t>13)switch(n){case o.AWTUserIdType.Skype:t=i.AWTPiiKind.Identity;break;case o.AWTUserIdType.EmailAddress:t=i.AWTPiiKind.SmtpAddress;break;case o.AWTUserIdType.PhoneNumber:t=i.AWTPiiKind.PhoneNumber;break;case o.AWTUserIdType.SipAddress:t=i.AWTPiiKind.SipAddress;break;default:t=i.AWTPiiKind.NotSet}this._addContextWithPii("UserInfo.Id",e,t)},e.prototype.setUserAdvertisingId=function(e){this._addContext("UserInfo.AdvertisingId",e)},e.prototype.setUserTimeZone=function(e){this._addContext("UserInfo.TimeZone",e)},e.prototype.setUserLanguage=function(e){this._addContext("UserInfo.Language",e)},e.prototype._addContext=function(e,t){"string"==typeof t&&this._properties.setProperty(e,t)},e.prototype._addContextWithPii=function(e,t,n){"string"==typeof t&&this._properties.setPropertyWithPii(e,t,n)},e}();t.default=a},72:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=n(5),i=n(158),o=n(564),s=n(942),a=n(585),u=n(663),c=n(685),l="awt_stats",d=function(){function e(){}return e.setEventsHandler=function(e){this._eventHandler=e},e.getEventsHandler=function(){return this._eventHandler},e.scheduleTimer=function(){var e=this,t=this._profiles[this._currentProfile][2];this._timeout<0&&t>=0&&!this._paused&&(this._eventHandler.hasEvents()?(0===t&&this._currentBackoffCount>0&&(t=1),this._timeout=setTimeout((function(){return e._batchAndSendEvents()}),t*(1<<this._currentBackoffCount)*1e3)):this._timerCount=0)},e.initialize=function(e){var t=this;this._newEventsAllowed=!0,this._config=e,this._eventHandler=new o.default(e.collectorUri,e.cacheMemorySizeLimitInNumberOfEvents,e.httpXHROverride,e.clockSkewRefreshDurationInMins),this._initializeProfiles(),s.default.initialize((function(e,n){if(t._config.canSendStatEvent(l)){var r=new a.default(l);for(var o in r.setEventPriority(i.AWTEventPriority.High),r.setProperty("TenantId",n),e)e.hasOwnProperty(o)&&r.setProperty(o,e[o].toString());u.default.getLogger(c.StatsApiKey).logEvent(r)}}))},e.setTransmitProfile=function(e){this._currentProfile!==e&&void 0!==this._profiles[e]&&(this.clearTimeout(),this._currentProfile=e,this.scheduleTimer())},e.loadTransmitProfiles=function(e){for(var t in this._resetTransmitProfiles(),e)if(e.hasOwnProperty(t)){if(3!==e[t].length)continue;for(var n=2;n>=0;--n)if(e[t][n]<0){for(var r=n;r>=0;--r)e[t][r]=-1;break}for(n=2;n>0;--n)if(e[t][n]>0&&e[t][n-1]>0){var i=e[t][n-1]/e[t][n];e[t][n-1]=Math.ceil(i)*e[t][n]}this._profiles[t]=e[t]}},e.sendEvent=function(e){this._newEventsAllowed&&(this._currentBackoffCount>0&&e.priority===i.AWTEventPriority.Immediate_sync&&(e.priority=i.AWTEventPriority.High),this._eventHandler.addEvent(e),this.scheduleTimer())},e.flush=function(e){var t=(new Date).getTime();!this._paused&&this._lastUploadNowCall+3e4<t&&(this._lastUploadNowCall=t,this._timeout>-1&&(clearTimeout(this._timeout),this._timeout=-1),this._eventHandler.uploadNow(e))},e.pauseTransmission=function(){this._paused||(this.clearTimeout(),this._eventHandler.pauseTransmission(),this._paused=!0)},e.resumeTransmision=function(){this._paused&&(this._paused=!1,this._eventHandler.resumeTransmission(),this.scheduleTimer())},e.flushAndTeardown=function(){s.default.teardown(),this._newEventsAllowed=!1,this.clearTimeout(),this._eventHandler.teardown()},e.backOffTransmission=function(){this._currentBackoffCount<4&&(this._currentBackoffCount++,this.clearTimeout(),this.scheduleTimer())},e.clearBackOff=function(){this._currentBackoffCount>0&&(this._currentBackoffCount=0,this.clearTimeout(),this.scheduleTimer())},e._resetTransmitProfiles=function(){this.clearTimeout(),this._initializeProfiles(),this._currentProfile=r.AWT_REAL_TIME,this.scheduleTimer()},e.clearTimeout=function(){this._timeout>0&&(clearTimeout(this._timeout),this._timeout=-1,this._timerCount=0)},e._batchAndSendEvents=function(){var e=i.AWTEventPriority.High;this._timerCount++,this._timerCount*this._profiles[this._currentProfile][2]===this._profiles[this._currentProfile][0]?(e=i.AWTEventPriority.Low,this._timerCount=0):this._timerCount*this._profiles[this._currentProfile][2]===this._profiles[this._currentProfile][1]&&(e=i.AWTEventPriority.Normal),this._eventHandler.sendEventsForPriorityAndAbove(e),this._timeout=-1,this.scheduleTimer()},e._initializeProfiles=function(){this._profiles={},this._profiles[r.AWT_REAL_TIME]=[4,2,1],this._profiles[r.AWT_NEAR_REAL_TIME]=[12,6,3],this._profiles[r.AWT_BEST_EFFORT]=[36,18,9]},e._newEventsAllowed=!1,e._currentProfile=r.AWT_REAL_TIME,e._timeout=-1,e._currentBackoffCount=0,e._paused=!1,e._timerCount=0,e._lastUploadNowCall=0,e}();t.default=d},5:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.AWT_REAL_TIME="REAL_TIME",t.AWT_NEAR_REAL_TIME="NEAR_REAL_TIME",t.AWT_BEST_EFFORT="BEST_EFFORT"},936:(e,t)=>{var n,r;Object.defineProperty(t,"__esModule",{value:!0}),(r=t.AWTUserIdType||(t.AWTUserIdType={}))[r.Unknown=0]="Unknown",r[r.MSACID=1]="MSACID",r[r.MSAPUID=2]="MSAPUID",r[r.ANID=3]="ANID",r[r.OrgIdCID=4]="OrgIdCID",r[r.OrgIdPUID=5]="OrgIdPUID",r[r.UserObjectId=6]="UserObjectId",r[r.Skype=7]="Skype",r[r.Yammer=8]="Yammer",r[r.EmailAddress=9]="EmailAddress",r[r.PhoneNumber=10]="PhoneNumber",r[r.SipAddress=11]="SipAddress",r[r.MUID=12]="MUID",(n=t.AWTSessionState||(t.AWTSessionState={}))[n.Started=0]="Started",n[n.Ended=1]="Ended"},150:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Version="1.8.6",t.FullVersionString="AWT-Web-JS-"+t.Version},74:(e,t,n)=>{n.r(t),n.d(t,{default:()=>O,isResourceLoggingEnabled:()=>R});var r=n(51),i=n(717),o=((0,i.AU)("measure"),(0,i.AU)("mark")&&window.performance.mark.bind(window.performance),(0,i.AU)("getEntriesByType")?window.performance.getEntriesByType.bind(window.performance):function(){return[]}),s=((0,i.AU)("getEntriesByName")&&window.performance.getEntriesByName.bind(window.performance),n(663));function a(e,t){return e&&t?e.toUpperCase()===t.toUpperCase():e===t}function u(e,t){return void 0===t&&(t=!1),e?function(e,t){var n=e.match(/^\{(.*)\}$/);return n?t?n[0]:n[1]:t?"{"+e+"}":e}(e.toUpperCase(),t):""}var c="enableKillSwitches",l="disableKillSwitches",d="KillSwitchOverrides",h="".concat(d,"_").concat(c),f="".concat(d,"_").concat(l);function p(e){try{var t=document.cookie.match("(^|;)\\s*"+e+"\\s*=\\s*([^;]+)");return t?t.pop():""}catch(e){return""}}function v(e){var t=[],n=[];for(var r in e)(e[r]?t:n).push(r);try{document.cookie="".concat(h,"=").concat(t.join(","),";path=/;samesite=none;secure;").concat(t.length?"":"expires=Thu, 01 Jan 1970 00:00:01 GMT;"),document.cookie="".concat(f,"=").concat(n.join(","),";path=/;samesite=none;secure;").concat(n.length?"":"expires=Thu, 01 Jan 1970 00:00:01 GMT;")}catch(e){}}var g,y={},_=function(){y=function(e,t,n,r){if(t)for(var i=0,o=t.split(",");i<o.length;i++)e[u(o[i],!1)]=!0;if(n)for(var s=0,a=n.split(",");s<a.length;s++)e[u(a[s],!1)]=!1;return e}({},p(h),p(f));var e=function(e){var t={};if(e)for(var n=e.split("&"),r=0;r<n.length;r++){var i=n[r].split("=");void 0!==i[1]&&(i[1]=i[1].replace(/\+/g," "),t[i[0]]=decodeURIComponent(i[1]))}return t}(function(){try{return location.search?location.search.substring(1):""}catch(e){return""}}());e[c],e[l],e.debugKillSwitches,v(y);try{window.__debugSetKillSwitch=function(e,t){void 0===t&&(t=!0),y[u(e,!1)]=!!t,v(y)}}catch(e){}_=function(){}};function m(e,t){g&&g(e,t)}var S,b=function(){function e(){}return e.initKillSwitches=function(t){e._killSwitch=T({killSwitches:t||{}})},e.isActivated=function(t,n,r){var i;if(_(),e._killSwitch)return e.isActivated=e._killSwitch.isActivated,e._killSwitch.isActivated(t,n,r);try{if(window._spPageContextInfo){var o=T(window._spPageContextInfo);return(null===(i=window._spPageContextInfo)||void 0===i?void 0:i.killSwitches)&&(e.isActivated=o.isActivated),o.isActivated(t,n,r)}if(window.Flight){var s=void 0;if(window.Flight.KillSwitches){s={};for(var a=0,u=Object.keys(window.Flight.KillSwitches);a<u.length;a++){var c=u[a];s[window.Flight.KillSwitches[c]]=!0}}var l=T({killSwitches:s});return s&&(e.isActivated=l.isActivated),l.isActivated(t,n,r)}}catch(e){return m(t,!1),!1}return m(t,!1),!1},e}();function T(e){var t=e&&e.killSwitches;return{isActivated:function(e,n,r){var i=!1;if(e){var o=u(e,!1);o in y?i=!!y[o]:t&&(i=!!t[o])}return m(e,i),i}}}!function(e){e[e.scheme=0]="scheme",e[e.authority=1]="authority",e[e.path=2]="path",e[e.query=3]="query"}(S||(S={}));var w=/[;\/?:@&=$,]/,I=/[\/?]/;function C(e){var t={};for(var n in e)e.hasOwnProperty(n)&&(t[n.toLowerCase()]=e[n].toLowerCase());return t}const E=function(){function e(e,t){this._scheme="",this._user="",this._host="",this._port="",this._path="",this._pathSegments=[],this._pathEncoded="",this._query={},this._fragment="",t&&(this._queryCaseInsensitive=!!t.queryCaseInsensitive,this._pathCaseInsensitive=!!t.pathCaseInsensitive),this._isCatchParsePathDecodeExceptionKSActive=b.isActivated("e759149e-00c5-412d-bc6d-d63d0bb5fe84","08/24/2022","Catch decoding errors while parsing the URI path"),this._parseURI(e)}return e.concatenate=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];for(var r="",i=0;i<t.length;i++){var o=t[i];i>0&&(o=e.ensureNoPrecedingSlash(o)),i<t.length-1&&(o=e.ensureTrailingSlash(o)),r+=o}return r},e.ensureNoPrecedingSlash=function(e){return"/"===e[0]?e.substr(1):e},e.ensureTrailingSlash=function(e){return"/"!==e[e.length-1]?e+"/":e},e.prototype.getScheme=function(){return this._scheme},e.prototype.setScheme=function(e){this._scheme=e},e.prototype.getAuthority=function(){return this._getAuthority(!1)},e.prototype.setAuthority=function(e){this._parseAuthority(e)},e.prototype.getUser=function(){return this._user},e.prototype.getHost=function(){return this._host},e.prototype.getPort=function(){return this._port},e.prototype.getPath=function(e){var t=this._path;return Boolean(e)&&null!==t&&t.lastIndexOf("/")===t.length-1&&(t=t.slice(0,-1)),t},e.prototype.getLeftPart=function(e){var t=this._scheme+"://";return e===S.authority&&(t+=this.getAuthority()),e===S.path&&(t+=this.getPath()),e===S.query&&(t+=this.getQuery()),t},e.prototype.setPath=function(e){"mailto"!==this.getScheme().toLowerCase()&&e&&"/"!==e[0]&&(e="/"+e),this._parsePath(e)},e.prototype.getPathSegments=function(){return this._pathSegments},e.prototype.getLastPathSegment=function(){var e=this._pathSegments;return e[e.length-1]||""},e.prototype.getQuery=function(e){return this._serializeQuery(e)},e.prototype.setQuery=function(e){this.setQueryFromObject(this._deserializeQuery(e))},e.prototype.getQueryAsObject=function(){return this._query},e.prototype.setQueryFromObject=function(e){for(var t in this._query={},e)e.hasOwnProperty(t)&&this.setQueryParameter(t,e[t])},e.prototype.getQueryParameter=function(e){var t=null,n=this._query;if(this._queryCaseInsensitive)for(var r in e=e.toLowerCase(),n)n.hasOwnProperty(r)&&r.toLowerCase()===e&&(t=n[r]);else t=n[e];return t||null},e.prototype.setQueryParameter=function(e,t,n){void 0===n&&(n=!0);var r=this._decodeQueryString(t);(r||n)&&(this._query[this._decodeQueryString(e)]=r)},e.prototype.removeQueryParameter=function(e){delete this._query[this._decodeQueryString(e)]},e.prototype.getFragment=function(){return this._fragment},e.prototype.setFragment=function(e){"#"===e[0]&&(e=e.substring(1)),this._fragment=this._decodeQueryString(e)},e.prototype.equals=function(e){return a(this._scheme,e.getScheme())&&this._user===e.getUser()&&a(this._host,e.getHost())&&this._port===e.getPort()&&this._fragment===e.getFragment()&&this._equalsCaseAppropriate(this.getPath(!0),e.getPath(!0),this._pathCaseInsensitive)&&this._equalsCaseAppropriate(this.getQuery(),e.getQuery(),this._queryCaseInsensitive)},e.prototype.equivalent=function(e){return a(this._scheme,e.getScheme())&&a(this._user,e.getUser())&&a(this._host,e.getHost())&&a(this._port,e.getPort())&&a(this.getPath(!0),e.getPath(!0))&&(t=C(this.getQueryAsObject()),n=C(e.getQueryAsObject()),r=[],i=[],o=function(e,t){return e===t},function e(t,n){if(t===n)return!0;if(null===t||null===n)return!1;if("object"!=typeof t||"object"!=typeof n)return!1;var s=Object.keys(t).sort(),a=Object.keys(n).sort();return s.length===a.length&&!!s.every((function(s,u){if(s!==a[u])return!1;if("function"==typeof t[s]||"function"==typeof n[s])return!0;if(o(t[s],n[s]))return!0;if("object"==typeof t[s]){if(-1!==r.indexOf(t[s]))throw new Error("Cannot perform DeepCompare() because a circular reference was encountered, object: ".concat(t,", ")+"property: ".concat(s));if(r.push(t[s]),-1!==i.indexOf(n[s]))throw new Error("Cannot perform DeepCompare() because a circular reference was encountered, object: ".concat(n,", ")+"property: ".concat(s));return i.push(n[s]),!!e(t[s],n[s])&&(r.pop(),i.pop(),!0)}return!1}))}(t,n))&&a(this._fragment,e.getFragment());var t,n,r,i,o},e.prototype.toString=function(e){return this._getStringInternal(!0,e)},e.prototype.getDecodedStringForDisplay=function(){return this._getStringInternal(!1)},e.prototype.getStringWithoutQueryAndFragment=function(){return this._getStringWithoutQueryAndFragmentInternal(!0)},e.prototype._equalsCaseAppropriate=function(e,t,n){return n?a(e,t):e===t},e.prototype._getStringInternal=function(e,t){var n=this._getStringWithoutQueryAndFragmentInternal(e,t),r=this.getQuery(e);return r&&(n+="?"+r),this._fragment&&(n+="#"+(e?encodeURIComponent(this._fragment):this._fragment)),n},e.prototype._getStringWithoutQueryAndFragmentInternal=function(e,t){var n="";this._scheme&&(n+=(e?encodeURIComponent(this._scheme):this._scheme)+":");var r=this._getAuthority(e,t);return r&&(n+="//"+r),this._pathEncoded&&(n+=e?this._pathEncoded:this._path),n},e.prototype._deserializeQuery=function(e){var t={};0===e.indexOf("?")&&(e=e.substring(1));for(var n=0,r=e.split(/[;&]+/);n<r.length;n++){var i=r[n],o=i.indexOf("=");o<0&&(o=i.length),o>0&&(t[i.substr(0,o)]=i.substr(o+1))}return t},e.prototype._serializeQuery=function(e){var t="";for(var n in this._query)if(this._query.hasOwnProperty(n)){var r=n,i=this._query[n];e&&(r=encodeURIComponent(r),i=encodeURIComponent(i)),t+=null===i||""===i?r+"=&":r+"="+i+"&"}return""!==t&&(t=t.slice(0,-1)),t},e.prototype._parseURI=function(e){var t=e,n=t.indexOf("#");if(n>=0){var r=t.substring(n+1);this.setFragment(r),t=t.substring(0,n)}var i=t.search(w);if(i>=0){":"===t[i]&&(this.setScheme(t.substring(0,i)),t=t.substring(i+1));var o="";if(0===t.indexOf("//")){var s=(t=t.substring(2)).search(I);if(s>=0?(o=t.substring(0,s),t=t.substring(s)):(o=t,t=""),this.setAuthority(o),!t)return void this.setPath("")}var a=t.indexOf("?");a>=0&&(this.setQuery(t.substring(a+1)),t=t.substring(0,a)),this.setPath(t)}else this.setPath(t)},e.prototype._parseAuthority=function(e){this._host=e;var t=e.lastIndexOf("@");t>=0&&(this._host=this._host.substring(t+1));var n=this._host.indexOf(":");if(!(t<0&&n<0)){var r=e;t<0?this._host=r:(this._user=r.substring(0,t),this._host=r.substring(t+1)),n>=0&&(this._port=this._host.substring(n+1),this._host=this._host.substring(0,n)),this._user=decodeURIComponent(this._user),this._host=decodeURIComponent(this._host)}},e.prototype._parsePath=function(e){if(this._isCatchParsePathDecodeExceptionKSActive)this._path=decodeURIComponent(e);else try{this._path=decodeURIComponent(e)}catch(t){this._path=e}var t=this._pathSegments=[];this._pathEncoded=e;for(var n=e.split("/"),r=0;r<n.length;++r)if(this._isCatchParsePathDecodeExceptionKSActive)t[r]=decodeURIComponent(n[r]);else try{t[r]=decodeURIComponent(n[r])}catch(e){t[r]=n[r]}""===t[0]&&t.shift(),""===t[t.length-1]&&t.pop()},e.prototype._getAuthority=function(e,t){void 0===t&&(t={});var n,r,i,o=t&&t.doNotPercentEncodeHost,s="";return e?(n=encodeURIComponent(this._user).replace("%3A",":"),r=o?this._host:encodeURIComponent(this._host),i=encodeURIComponent(this._port)):(n=this._user,r=this._host,i=this._port),""!==n&&(s=n+"@"),""!==this._host&&(s+=r),""!==this._port&&(s+=":"+i),s},e.prototype._decodeQueryString=function(e){var t=e;try{t=decodeURIComponent(e.replace(/\+/g," "))}catch(e){}return t},e}();var A=n(567),P="resource";function k(e){e.forEach((function(e){var t=function(e){try{return new E(e||"").getQueryParameter("suxrid")}catch(e){return""}}(e.name);t&&function(e,t,n){(0,A.getDispatcher)().dispatch({eventType:"QOSSTOP",name:"resource",nameDetail:t,result:"SUCCESS",startTimestamp:Math.round(e.startTime),totalTime:Math.round(e.duration),properties:{resourceName:void 0,initiatorType:e.initiatorType,entryType:e.entryType,connectStart:Math.round(e.connectStart),connectEnd:Math.round(e.connectEnd),domainLookupStart:Math.round(e.domainLookupStart),domainLookupEnd:Math.round(e.domainLookupEnd),fetchStart:Math.round(e.fetchStart),requestStart:Math.round(e.requestStart),responseStart:Math.round(e.responseStart),responseEnd:Math.round(e.responseEnd),encodedBodySize:e.encodedBodySize,decodedBodySize:e.decodedBodySize,transferSize:e.transferSize}})}(e,t)}))}var R=(0,r.regs)("searchux.isResourceLoggingEnabled",!1,!0);const O=(0,r.listen)("searchux.isResourceLoggingEnabled",(function(e,t){t&&function(){if(window.PerformanceObserver)return k(o(P)),void new window.PerformanceObserver((function(e){k(e.getEntries())})).observe({entryTypes:[P]});window.addEventListener("beforeunload",(function(){k(o(P)),s.default.flushAndTeardown()}))}()}))},717:(e,t,n)=>{n.d(t,{tB:()=>a,AU:()=>s,ni:()=>i});var r=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==n.g)return n.g;throw new Error("unable to locate global object")}(),i=(new Date).valueOf(),o=r,s=function(e){return Boolean(o.performance&&performance[e])},a=s("now")?o.performance.now.bind(o.performance):function(){return(new Date).valueOf()-i}},919:(e,t,n)=>{n.r(t),n.d(t,{allowEUPIScrubbingIfNoTelemetryBoundarySet:()=>u,default:()=>a});var r=n(51),i=n(783),o=function(e,t){function n(n){return function(e,t,n,r){if(!e.exists(t))return n;var o=e.get(t);return function(e,t){return(void 0===e?"undefined":(0,i._)(e))===t}(o,r)?o:(console.error("Type mismatch for setting '".concat(t,"': expected '").concat(r,"' but got '").concat(void 0===o?"undefined":(0,i._)(o),"' instead")),n)}(n,e,t,(0,i._)(t))}return n.settingName=e,n.defaultValue=t,n}("searchux.telemetryBoundary","NotSet"),s=n(386);const a=(0,r.regs)(o.settingName,o.defaultValue,{required:!1,isReadOnly:!0,includeInLogs:!0,validator:function(e){return-1!==s.G8.indexOf(e)}});var u=(0,r.regs)("searchux.allowEUPIScrubbingIfNoTelemetryBoundarySet",!0)},386:(e,t,n)=>{n.d(t,{Dq:()=>o,G8:()=>r,Hz:()=>i});var r=["NotSet","ROW","EU"],i=["prod"],o=["ResponseReceived","ClientLayout","ResultsRendered","ClientDataSource","SearchFeedback","SearchActions","SearchEntityActions","CachedContentRendered","CounterFactualInformation"]},375:(e,t,n)=>{n.r(t),n.d(t,{isKillSwitchActivatedSetting:()=>o,killSwitchesSetting:()=>i});var r=n(51),i=(0,r.regs)("searchux.killSwitches",{}),o=(0,r.regs)("searchux.isKillSwitchActivated",(function(){return!1}))},51:(e,t,n)=>{n.r(t),n.d(t,{allUnsafe:()=>F,automaticallyApplyPreviouslyIgnoredSettings:()=>D,createConf:()=>h,existsUnsafe:()=>U,getCreator:()=>j,getPreviouslyIgnoredSettings:()=>x,getSettingsForLogging:()=>I,getUnsafe:()=>L,gets:()=>w,importerWithConf:()=>l,initialize:()=>S,isRegistered:()=>C,isString:()=>m,keys:()=>E,listen:()=>A,regs:()=>P,reset:()=>k,resetAndApply:()=>R,setNewSettings:()=>M,sets:()=>O,setsFromObject:()=>B,setsSafe:()=>N,settings:()=>q,utils:()=>_,valid:()=>g,validate:()=>W,version:()=>d});var r=n(437);const i={};var o,s=((o="@1js/search-conf")in i||(i[o]=void 0),{get:()=>i[o],set:e=>i[o]=e});function a(){return s.get()}function u(e){s.set(e)}function c(e){throw new Error(e)}function l(e){return function(t){return e().then((function(e){return t(e.conf),e}))}}var d=1;function h(e){return new p(e)}var f="(unnamed)",p=function e(t){var n=this,i=this;(0,r._)(this,e),this.applyPreviouslyIgnoredSettings=!1,this.sets=function(e,t){if("function"==typeof e)return n.sets(e.settingName,t);e=e||"";var r=n.data.settings;if(!(e in r))return _.warn("Ignored setting non-registered setting '".concat(e,"'.")),void(n.data.previouslyIgnoredSettings[e]=t);var i=r[e];!i.validator||i.validator(t)?i.isReadOnly&&i.numWrites>0?_.warn("Ignored re-setting readonly setting '".concat(e,"'.")):(i.values.unshift({val:t,at:_.date()}),i.numWrites++,i.listeners.forEach((function(n){return n(e,t)}))):_.warn("Ignored setting invalid '".concat(e,"' value."))},this.setsSafe=this.sets,this.getPreviouslyIgnoredSettings=function(){return n.data.previouslyIgnoredSettings},this.setsFromObject=function(e){var t=Object.keys(e),r=n.data.settings;t.forEach((function(t){t in r?n.sets(t,e[t]):n.data.previouslyIgnoredSettings[t]=e[t]}))},this.setNewSettings=function(e){var t=n.data.settings;Object.keys(t).forEach((function(r){r in e&&0===t[r].numWrites&&n.sets(r,e[r])}))},this.resetAndApply=function(e){n.keys().forEach((function(e){return n.reset(e,!1)})),n.setsFromObject(e)},this.reset=function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];e=e||"";var n=i.data.settings;if(e in n){var r=n[e];if(r.isReadOnly)_.warn("Ignored resetting readonly setting '".concat(e,"'."));else{var o=r.values[r.values.length-1].val;r.values.unshift({val:o,at:_.date()}),r.numWrites++,t&&r.listeners.forEach((function(t){return t(e,o)}))}}else _.warn("Ignored resetting non-registered setting '".concat(e,"'."))},this.gets=function(e,t){e=e||"";var r=n.data.settings;!(e in r)&&c("Setting '".concat(e||"","' does not exist."));var i=r[e],o=i.values,s=i.numWrites,a=i.required,u=i.wrapper;a&&o.length<2&&!i.numReads&&_.warn("Setting '".concat(e,"' accessed before being set.")),i.numReads++;var l=i.values[0].val,d=u?u(l):l;return t?{val:d,numWrites:s}:d},this.getUnsafe=function(e){return n.isRegistered(e)?n.gets(e):e in n.data.previouslyIgnoredSettings?n.data.previouslyIgnoredSettings[e]:void c("Setting '".concat(e,"' does not exist."))},this.existsUnsafe=function(e){return n.isRegistered(e)||e in n.data.previouslyIgnoredSettings},this.allUnsafe=function(){return n.data.previouslyIgnoredSettings},this.regs=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if("function"==typeof e)return i.regs(e.settingName,e.settingInitialValue,e.settingOptions);if("boolean"==typeof n)return i.regs(e,t,{required:n});!v.test(e)&&c("Setting '".concat(e,"' must have a namespace."));var r=i.data.settings;e in r&&c("Setting '".concat(e,"' has already been registered."));var o=n.required,s=n.validator,a=void 0===s?g:s,u=n.wrapper,l=n.isReadOnly,d=void 0!==l&&l;!a(t)&&c("Setting '".concat(e,"' must have a valid default value.")),r[e]={name:e,values:[{val:t,at:_.date()}],listeners:[],numReads:0,numWrites:0,required:o,includeInLogs:n.includeInLogs||!1,validator:a,wrapper:u,isReadOnly:d};var h=function(t){return i.gets(e,null!=t&&t)};return h.settingName=e,h.settingInitialValue=t,h.settingOptions={required:o,validator:a,wrapper:u,isReadOnly:d},i.applyPreviouslyIgnoredSettings&&e in i.data.previouslyIgnoredSettings&&i.sets(e,i.data.previouslyIgnoredSettings[e]),h},this.isRegistered=function(e){return e in n.data.settings},this.keys=function(){var e=n.data.settings;return e&&Object.keys(e)||[]},this.validate=function(){var e=n.data.settings,t=!0,r=!1,i=void 0;try{for(var o,s=Object.keys(e)[Symbol.iterator]();!(t=(o=s.next()).done);t=!0){var a=o.value,u=e[a];u.required&&u.values.length<2&&_.warn("Setting '".concat(a,"' is required and has not been set."))}}catch(e){r=!0,i=e}finally{try{t||null==s.return||s.return()}finally{if(r)throw i}}},this.listen=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{skipDefaultValue:!1},r=i.data.settings;return(e=e||"")in r?(r[e].listeners.unshift(t),r[e].values.length>(n.skipDefaultValue?1:0)&&t(e,r[e].values[0].val),function(){return i.unlisten(e,t)}):(_.warn("Ignored listening to non-registered setting '".concat(e,"'.")),function(){})},this.unlisten=function(e,t){var r=n.data.settings;if((e=e||"")in r){for(var i=r[e].listeners,o=0;o<i.length;o++)if(i[o]===t)return void i.splice(o,1);_.warn("Unlistening to setting '".concat(e,"' with unknown listener."))}else _.warn("Ignored unlistening to non-registered setting '".concat(e,"'."))},this.getSettingsForLogging=function(){var e=n.data.settings,t=n.keys(),r={};return t.filter((function(t){return e[t].includeInLogs})).forEach((function(t){r[e[t].name]=e[t].values.length&&e[t].values[0]&&e[t].values[0].val})),r},this.getCreator=function(){return n.data.createdBy},this.automaticallyApplyPreviouslyIgnoredSettings=function(){n.applyPreviouslyIgnoredSettings=!0},t&&!m(t)&&c("Conf instance name must be string."),this.data={version:d,createdBy:t||f,createdAt:new Date,settings:{},previouslyIgnoredSettings:{}}},v=/^.+\../,g=function(){return!0},y=(new Date).valueOf(),_={date:"undefined"!=typeof window&&window.performance&&performance.now?function(){return performance.now()}:function(){return(new Date).valueOf()-y},warn:function(e){"undefined"!=typeof window&&window.console&&"function"==typeof console.warn&&console.warn(e)}};function m(e){return"string"==typeof e}function S(e){var t=a();t&&t.data.createdBy!==e&&t.data.createdBy!==f?c("Conf module can not be initialized with two different names."):t?t.data.createdBy=e:u(new p(e))}function b(){var e=a();return e||u(e=new p),e}function T(e){return function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];var i=b();return i[e].apply(i,n)}}var w=T("gets"),I=T("getSettingsForLogging"),C=T("isRegistered"),E=T("keys"),A=T("listen"),P=T("regs"),k=T("reset"),R=T("resetAndApply"),O=T("sets"),B=T("setsFromObject"),M=T("setNewSettings"),x=T("getPreviouslyIgnoredSettings"),D=T("automaticallyApplyPreviouslyIgnoredSettings"),N=T("setsSafe"),W=T("validate"),j=T("getCreator"),L=T("getUnsafe"),U=T("existsUnsafe"),F=T("allUnsafe");function q(){return b().data.settings}},567:e=>{e.exports=r},437:(e,t,n)=>{function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}n.d(t,{_:()=>r})},783:(e,t,n)=>{function r(e){return e&&"undefined"!=typeof Symbol&&e.constructor===Symbol?"symbol":typeof e}n.d(t,{_:()=>r})}},o={};function s(e){var t=o[e];if(void 0!==t)return t.exports;var n=o[e]={exports:{}};return i[e](n,n.exports,s),n.exports}s.d=(e,t)=>{for(var n in t)s.o(t,n)&&!s.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},s.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var a={};function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){u(e,t,n[t])}))}return e}function l(e,t,n,r,i,o,s){try{var a=e[o](s),u=a.value}catch(e){return void n(e)}a.done?t(u):Promise.resolve(u).then(r,i)}function d(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function s(e){l(o,r,i,s,a,"next",e)}function a(e){l(o,r,i,s,a,"throw",e)}s(void 0)}))}}function h(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function f(e,t){var n,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},s=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return s.next=a(0),s.throw=a(1),s.return=a(2),"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(a){return function(u){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;s&&(s=0,a[0]&&(o=0)),o;)try{if(n=1,r&&(i=2&a[0]?r.return:a[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,a[1])).done)return i;switch(r=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,r=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(!((i=(i=o.trys).length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){o=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){o.label=a[1];break}if(6===a[0]&&o.label<i[1]){o.label=i[1],i=a;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(a);break}i[2]&&o.ops.pop(),o.trys.pop();continue}a=t.call(e,o)}catch(e){a=[6,e],r=0}finally{n=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}}function p(e,t,n){return e.register((function(r){var i=!1,o=t.reduce((function(t,n){return n in r?(i=!0,t[n]=r[n]):t[n]=e.props[n],t}),{});i&&n(o)}))}s.r(a),s.d(a,{conf:()=>lo,enable3SEventsLogger:()=>co,enableAriaLogger:()=>uo}),Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;var v=s(437);function g(e,t){return t}function y(e){return y=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},y(e)}function _(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_=function(){return!!e})()}var m=s(783);function S(e,t){return S=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},S(e,t)}var b=Object.prototype;const T=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||b)},w=function(e,t){return function(n){return e(t(n))}},I=w(Object.keys,Object);var C=Object.prototype.hasOwnProperty;const E=function(e){if(!T(e))return I(e);var t=[];for(var n in Object(e))C.call(e,n)&&"constructor"!=n&&t.push(n);return t},A="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g;var P="object"==typeof self&&self&&self.Object===Object&&self;const k=A||P||Function("return this")(),R=k.Symbol;var O=Object.prototype,B=O.hasOwnProperty,M=O.toString,x=R?R.toStringTag:void 0,D=Object.prototype.toString,N=R?R.toStringTag:void 0;const W=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":N&&N in Object(e)?function(e){var t=B.call(e,x),n=e[x];try{e[x]=void 0;var r=!0}catch(e){}var i=M.call(e);return r&&(t?e[x]=n:delete e[x]),i}(e):function(e){return D.call(e)}(e)},j=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)},L=function(e){if(!j(e))return!1;var t=W(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t},U=k["__core-js_shared__"];var F,q=(F=/[^.]+$/.exec(U&&U.keys&&U.keys.IE_PROTO||""))?"Symbol(src)_1."+F:"",H=Function.prototype.toString;const V=function(e){if(null!=e){try{return H.call(e)}catch(e){}try{return e+""}catch(e){}}return""};var z=/^\[object .+?Constructor\]$/,Q=Function.prototype,K=Object.prototype,G=Q.toString,J=K.hasOwnProperty,X=RegExp("^"+G.call(J).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");const Z=function(e,t){var n=function(e,t){return null==e?void 0:e[t]}(e,t);return function(e){return!(!j(e)||(t=e,q&&q in t))&&(L(e)?X:z).test(V(e));var t}(n)?n:void 0},$=Z(k,"DataView"),Y=Z(k,"Map"),ee=Z(k,"Promise"),te=Z(k,"Set"),ne=Z(k,"WeakMap");var re="[object Map]",ie="[object Promise]",oe="[object Set]",se="[object WeakMap]",ae="[object DataView]",ue=V($),ce=V(Y),le=V(ee),de=V(te),he=V(ne),fe=W;($&&fe(new $(new ArrayBuffer(1)))!=ae||Y&&fe(new Y)!=re||ee&&fe(ee.resolve())!=ie||te&&fe(new te)!=oe||ne&&fe(new ne)!=se)&&(fe=function(e){var t=W(e),n="[object Object]"==t?e.constructor:void 0,r=n?V(n):"";if(r)switch(r){case ue:return ae;case ce:return re;case le:return ie;case de:return oe;case he:return se}return t});const pe=fe,ve=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991},ge=function(e){return null!=e&&ve(e.length)&&!L(e)},ye=Array.isArray,_e=function(e){return null!=e&&"object"==typeof e},me=function(e){return function(t){return null==t?void 0:t[e]}},Se=me("length");var be=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]"),Te="\\ud800-\\udfff",we="["+Te+"]",Ie="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",Ce="\\ud83c[\\udffb-\\udfff]",Ee="[^"+Te+"]",Ae="(?:\\ud83c[\\udde6-\\uddff]){2}",Pe="[\\ud800-\\udbff][\\udc00-\\udfff]",ke="(?:"+Ie+"|"+Ce+")?",Re="[\\ufe0e\\ufe0f]?",Oe=Re+ke+"(?:\\u200d(?:"+[Ee,Ae,Pe].join("|")+")"+Re+ke+")*",Be="(?:"+[Ee+Ie+"?",Ie,Ae,Pe,we].join("|")+")",Me=RegExp(Ce+"(?="+Ce+")|"+Be+Oe,"g");const xe=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r&&!1!==t(e[n],n,e););return e},De=function(e,t,n){for(var r=-1,i=Object(e),o=n(e),s=o.length;s--;){var a=o[++r];if(!1===t(i[a],a,i))break}return e},Ne=function(e){return _e(e)&&"[object Arguments]"==W(e)};var We=Object.prototype,je=We.hasOwnProperty,Le=We.propertyIsEnumerable;const Ue=Ne(function(){return arguments}())?Ne:function(e){return _e(e)&&je.call(e,"callee")&&!Le.call(e,"callee")};var Fe=t&&!t.nodeType&&t,qe=Fe&&e&&!e.nodeType&&e,He=qe&&qe.exports===Fe?k.Buffer:void 0;const Ve=(He?He.isBuffer:void 0)||function(){return!1};var ze=/^(?:0|[1-9]\d*)$/;const Qe=function(e,t){var n=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==n||"symbol"!=n&&ze.test(e))&&e>-1&&e%1==0&&e<t};var Ke={};Ke["[object Float32Array]"]=Ke["[object Float64Array]"]=Ke["[object Int8Array]"]=Ke["[object Int16Array]"]=Ke["[object Int32Array]"]=Ke["[object Uint8Array]"]=Ke["[object Uint8ClampedArray]"]=Ke["[object Uint16Array]"]=Ke["[object Uint32Array]"]=!0,Ke["[object Arguments]"]=Ke["[object Array]"]=Ke["[object ArrayBuffer]"]=Ke["[object Boolean]"]=Ke["[object DataView]"]=Ke["[object Date]"]=Ke["[object Error]"]=Ke["[object Function]"]=Ke["[object Map]"]=Ke["[object Number]"]=Ke["[object Object]"]=Ke["[object RegExp]"]=Ke["[object Set]"]=Ke["[object String]"]=Ke["[object WeakMap]"]=!1;var Ge=t&&!t.nodeType&&t,Je=Ge&&e&&!e.nodeType&&e,Xe=Je&&Je.exports===Ge&&A.process,Ze=function(){try{return Je&&Je.require&&Je.require("util").types||Xe&&Xe.binding&&Xe.binding("util")}catch(e){}}(),$e=Ze&&Ze.isTypedArray;const Ye=$e?(et=$e,function(e){return et(e)}):function(e){return _e(e)&&ve(e.length)&&!!Ke[W(e)]};var et,tt=Object.prototype.hasOwnProperty;const nt=function(e,t){var n=ye(e),r=!n&&Ue(e),i=!n&&!r&&Ve(e),o=!n&&!r&&!i&&Ye(e),s=n||r||i||o,a=s?function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}(e.length,String):[],u=a.length;for(var c in e)!t&&!tt.call(e,c)||s&&("length"==c||i&&("offset"==c||"parent"==c)||o&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||Qe(c,u))||a.push(c);return a},rt=function(e){return ge(e)?nt(e):E(e)},it=function(e,t){if(null==e)return e;if(!ge(e))return function(e,t){return e&&De(e,t,rt)}(e,t);for(var n=e.length,r=-1,i=Object(e);++r<n&&!1!==t(i[r],r,i););return e},ot=function(e){return e},st=function(e,t){return(ye(e)?xe:it)(e,"function"==typeof(n=t)?n:ot);var n};function at(e,t){var n,r,i,o,s,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,u=arguments.length>3?arguments[3]:void 0,c=arguments.length>4?arguments[4]:void 0,l=arguments.length>5?arguments[5]:void 0,h=!1,p=!1;function v(e){return e.then((function(e){return g(),e}),(function(e){throw g(),e}))}function g(){if(c&&n){var e=r||ut(n,!1,"soft timeout; retry succeeded");c(e,o,s)}}function y(){return _.apply(this,arguments)}function _(){return(_=d((function(){var t,d,v,g;return f(this,(function(f){switch(f.label){case 0:n={startTime:new Date},f.label=1;case 1:return f.trys.push([1,3,,8]),[4,e()];case 2:return t=f.sent(),h=!0,c&&(r=ut(n,!0)),[2,t];case 3:if(d=f.sent(),c&&(r=ut(n,!1,d)),l||u&&d&&u(d))return p=!0,[2,Promise.reject(d)];if(p)return[2,Promise.reject("Another retry has already been attempted.")];p=!0,i={startTime:new Date,isDelayedRetry:!1},f.label=4;case 4:return f.trys.push([4,6,,7]),[4,a()];case 5:return v=f.sent(),s=!h,h=!0,c&&(o=ut(i,!0)),[2,v];case 6:return g=f.sent(),c&&(o=ut(i,!1,g)),[2,Promise.reject(g)];case 7:return[3,8];case 8:return[2]}}))}))).apply(this,arguments)}if(!t)return v(y());var m,S=new Promise((function(e,n){var r=function(){if(h||p)return clearInterval(u),clearTimeout(l),n()},u=setInterval(r,20);r();var l=setTimeout((function(){if(clearInterval(u),h||p)return n();p=!0,i={startTime:new Date,isDelayedRetry:!0},a().then((function(t){c&&i&&(s=!h,o=ut(i,!0)),e(t)}),(function(e){c&&i&&(o=ut(i,!1,e)),n(e)}))}),t)}));return v((m=[y(),S],new Promise((function(e,t){var n,r=0,i=function(e){if(null==e)return 0;if(ge(e))return"string"==typeof(t=e)||!ye(t)&&_e(t)&&"[object String]"==W(t)?function(e){return function(e){return be.test(e)}(e)?function(e){for(var t=Me.lastIndex=0;Me.test(e);)++t;return t}(e):Se(e)}(e):e.length;var t,n=pe(e);return"[object Map]"==n||"[object Set]"==n?e.size:E(e).length}(m);0===i&&e(),st(m,(function(o){o.then((function(t){return e(t)}),(function(e){if(r++,null!=e&&(n=e),r>=i)return t(n)}))}))}))))}function ut(e,t,n){return h(c({},e),{durationInMs:(new Date).getTime()-e.startTime.getTime(),succeeded:t,error:n})}console.log,console.warn,console.error;var ct=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!1},lt="search-3s-logging";function dt(){var e=function(){return Math.floor(65536*(1+Math.random())).toString(16).substring(1)};return e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()}function ht(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function ft(){for(var e=function(){return"object"===("undefined"==typeof window?"undefined":(0,m._)(window))&&window.crypto&&(e=new Uint8Array(31),window.crypto.getRandomValues(e),e)||function(){for(var e=new Array(31),t=0;t<31;t++)e[t]=Math.floor(16*Math.random());return e}();var e}(),t=0,n="",r=0;r<36;r++)switch(r){case 8:case 13:case 18:case 23:t+=1,n+="-";break;case 14:t+=1,n+="4";break;case 19:n+=(e[r-t]%4+8).toString(16);break;default:n+=(e[r-t]%16).toString(16)}return n}var pt=function(){return document.createElement("iframe")},vt=function(e,t,n,r){return new Promise((function(i,o){return gt((function s(a){if(a.source===e&&a.origin===t){var u,c=!1;try{u=n(a)}catch(e){return yt(s),void o(e)}try{c=r(u,a)}catch(e){return yt(s),void o(e)}c&&(yt(s),i(u))}}))}))},gt=function(e){return addEventListener("message",e,!1)},yt=function(e){return removeEventListener("message",e,!1)};function _t(e){return function(e){return!!e&&(0===e.toLocaleLowerCase().indexOf("http://")||0===e.toLocaleLowerCase().indexOf("https://"))}(e)?e:"https://".concat(e)}var mt="https://substrate.office.com",St="/search/api/v2/resources",bt={},Tt={},wt={},It="ms-searchux-3s-proxy";function Ct(e,t){return bt[e]?bt[e]:function(e,t,n){if(e){var r=document.getElementById(It),i=function(e,t){return e&&e.src===t&&e.ready&&e.postMessage?{postMessage:e.postMessage,iframe:e,ready:e.ready,initialize:function(){return e}}:null}(r,t);bt[n]=i||Et(!r,t,n)}else bt[n]=Et(!1,t,n);return bt[n]}((n=window).mssearchux&&n.mssearchux.reuseIframe||t,e+St,e);var n}function Et(e,t,n){var r=function(e,t){var n=pt();return{iframe:n,initialize:function(){return document.body.contains(n)||(n.src=e,n.style.display="none",document.body.appendChild(n)),n},postMessage:function(r,i,o){var s=function(e,r){return vt(n.contentWindow,t,e,r)}(i,o);return n.contentWindow&&n.contentWindow.postMessage(r,e),s}}}(t,n),i=r.initialize(),o=vt(i.contentWindow,n,(function(e){return e.data}),(function(e){return"loaded"===e}));return e&&(i.id=It,i.postMessage=r.postMessage,i.ready=o),h(c({},r),{ready:o})}function At(e,t,n){var r=ft(),i=e.action,o=e.token,s=e.prefix,a=e.cvid,u=e.additionalInfo,c=e.headers;return Ct(t,n).postMessage(JSON.stringify({requestId:r,action:i,token:o,prefix:s,cvid:a,additionalInfo:u,headers:c}),Pt,(function(e){return e.requestId===r}))}function Pt(e){try{return JSON.parse(e.data)}catch(e){return function(e,t,n){throw function(e,t,n){var r;return(r=new Error("Failed to parse substrate API JSON response.")).safeToLog=!0,r.logProperties=void 0,Object.freeze(r),r}()}()}}function kt(e,t){var n=-e.getTimezoneOffset(),r=n>=0?"+":"-";return e.getFullYear()+"-"+Rt(e.getMonth()+1)+"-"+Rt(e.getDate())+"T"+Rt(e.getHours())+":"+Rt(e.getMinutes())+":"+Rt(e.getSeconds())+(t?"."+Rt(e.getMilliseconds(),3):"")+r+Rt(n/60)+":"+Rt(n%60)}function Rt(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return(Math.pow(10,t)+Math.abs(e)).toString().substring(1,t+1)}var Ot=function(e){return e.HostApp="HostApp",e.Error="Error",e}({}),Bt=function(e){return e.Error="Error",e.Info="Info",e}({}),Mt=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1/0;(0,v._)(this,e),this.capacity=t,this.hostAppStorage=[],this.errorLogsStorage=[],this.harTraceEntries=[],this.metaData=""}var t=e.prototype;return t.setMetaData=function(t){e.enableDiagnosticLogs&&(this.metaData=t)},t.getMetaData=function(){return this.metaData},t.enqueueHarEntry=function(t){e.enableDiagnosticLogs&&t&&(this.harTraceEntries.length===this.capacity&&this.harTraceEntries.shift(),this.harTraceEntries.push(t))},t.enqueueEvent=function(t){e.enableDiagnosticLogs&&(t.eventType===Ot.HostApp&&(this.hostAppStorage.length===this.capacity&&this.hostAppStorage.shift(),this.hostAppStorage.push(t)),t.eventType===Ot.Error&&(this.errorLogsStorage.length===this.capacity&&this.errorLogsStorage.shift(),this.errorLogsStorage.push(t)))},t.getHostAppEvents=function(){return this.hostAppStorage},t.getHarTraceEntries=function(){return this.harTraceEntries},t.getErrorEvents=function(){return this.errorLogsStorage},e.getDiagnosticLogsEnabled=function(){return e.enableDiagnosticLogs},e.setDiagnosticLogsEnabled=function(t){e.enableDiagnosticLogs=t},e.getInstance=function(){return!e.instance&&e.enableDiagnosticLogs&&(e.instance=new e(1e3)),e.instance},e.deleteInstance=function(){var t=e.getInstance();null!=t&&(t.hostAppStorage=[],t.errorLogsStorage=[],t.harTraceEntries=[],t.setMetaData("")),e.instance=null},e}();function xt(e){var t=[];for(var n in e)t.push({name:n,value:e[n]});return t}Mt.enableDiagnosticLogs=!1;var Dt=function(e){return e[e.TIMEOUT=0]="TIMEOUT",e[e.CANCELLED=1]="CANCELLED",e[e.FAILED=2]="FAILED",e}({}),Nt="xml-http-request-executor";function Wt(e){return{family:Nt,code:e}}var jt=/\?/;function Lt(e,t){return{message:e.status?"":"UndefinedOrAbortedRequestStatus_".concat(t),statusCode:e.status,responseText:"blob"!==e.responseType?e.responseText:void 0,isExpected:!1,shouldRetryAsXhrRequest:!1,headers:Ut(e.getAllResponseHeaders()),midgardError:Wt(Dt.FAILED)}}function Ut(e){if(e){var t={};return e.split("\r\n").forEach((function(e){var n=e.indexOf(": ");t[e.substr(0,n).toLowerCase()]=e.substr(n+2)})),t}return{}}var Ft=function(){function e(t,n,r,i,o,s){var a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:"text";if((0,v._)(this,e),this.url=t,this.verb=n,this.timeout=r,this.body=o,this.withCredentials=s,this.responseType=a,this.isConvertGetPostRequest=!1,this.headers={},i&&"GET"===this.verb){this.isConvertGetPostRequest=!0,this.verb="POST";var u=jt.test(this.url);this.url="".concat(this.url).concat(u?"&":"?").concat("ConvertGetPost","=true")}}var t=e.prototype;return t.setHeader=function(e,t){this.headers[e]=t},t.setTimeout=function(e){this.timeout=e},t.go=function(){var e,t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"REST";return new Promise((function(r,i){var o=new XMLHttpRequest,s={send:-1,wait:-1,block:-1,dns:-1,connect:-1,receive:0},a=performance.now(),u=0;if(o.open(t.verb,t.url),o.timeout=t.timeout,o.withCredentials=t.withCredentials||!1,o.onreadystatechange=function(e){var n,r,i,c;if(Mt.getDiagnosticLogsEnabled())try{if(o.readyState===XMLHttpRequest.OPENED&&(u=performance.now()),o.readyState===XMLHttpRequest.DONE){var l,d=performance.now();s.wait=d-u;var h=o.responseText?new Blob([o.responseText]).size:0,f={startedDateTime:(new Date).toISOString(),time:d-a,request:{cookies:[],httpVersion:"unknown",method:t.verb||"",url:t.url||"",headers:xt(t.headers),queryString:(n=t.url,r=new URL(n),i=new URLSearchParams(r.search),c=[],i.forEach((function(e,t){c.push({name:t,value:e})})),c),postData:t.body?{mimeType:o.getResponseHeader("Content-Type")||"",text:t.body}:{},headersSize:-1,bodySize:t._requestBodySize||0},response:{cookies:[],httpVersion:"unknown",status:o.status,statusText:o.statusText,headers:xt(Ut(o.getAllResponseHeaders())),content:{size:h,mimeType:o.getResponseHeader("Content-Type"),text:o.responseText},redirectURL:"",headersSize:-1,bodySize:h},cache:{},timings:s};f._resourceType=function(e){var t,n=e.request.url,r=e.response.content.mimeType||"",i=(null===(t=n.toString().split(".").pop())||void 0===t?void 0:t.toLowerCase())||"";return-1!==r.indexOf("html")||"html"===i?"document":-1!==r.indexOf("css")||"css"===i?"stylesheet":-1!==r.indexOf("javascript")||"js"===i?"script":r.match(/image\/(jpeg|png|gif|bmp|webp)/)||-1!==["jpg","jpeg","png","gif","bmp","webp"].indexOf(i)?"image":r.match(/font\/(woff|woff2|ttf|otf)/)||-1!==["woff","woff2","ttf","otf"].indexOf(i)?"font":"POST"===e.request.method&&-1!==r.indexOf("application/json")?"xhr":"other"}(f),null===(l=Mt.getInstance())||void 0===l||l.enqueueHarEntry(f)}}catch(e){var p;null===(p=Mt.getInstance())||void 0===p||p.enqueueEvent({eventType:Ot.Error,message:"Failed while processing har entry",timeStamp:new Date,level:Bt.Error}),console.error(e)}},t.responseType&&(o.responseType=t.responseType),t.isConvertGetPostRequest)t.body=JSON.stringify(t.headers);else for(var c in t.headers)t.headers.hasOwnProperty(c)&&o.setRequestHeader(c,t.headers[c]);o.onload=function(s){if(o.status>=200&&o.status<300){var a=function(e,t){if(e&&t&&0!==t)return Math.ceil(t-e)}(e,s.timeStamp);r(t.getResponseObjectFromRequest(o,a))}else i(Lt(o,n))},o.onerror=function(){i(Lt(o,n))},o.ontimeout=function(){i({statusCode:o.status,message:"RequestTimeout",isExpected:!0,shouldRetryAsXhrRequest:!1,headers:void 0,midgardError:Wt(Dt.TIMEOUT)})},o.onabort=function(){i({statusCode:o.status,message:"Cancelled",isExpected:!1,shouldRetryAsXhrRequest:!1,headers:void 0,midgardError:Wt(Dt.CANCELLED)})},e=function(){try{if("function"==typeof CustomEvent)return new CustomEvent("Event").timeStamp;if("undefined"!=typeof document)return document.createEvent("Event").timeStamp}catch(e){}}(),t._requestBodySize=t.body?new Blob([t.body]).size:0,s.send=performance.now()-a,o.send(t.body)}))},t.getResponseObjectFromRequest=function(e,t){return{Body:"blob"===e.responseType?e.response:e.responseText,StatusCode:e.status,Headers:Ut(e.getAllResponseHeaders()),ResponseUrl:e.responseURL,RequestDurationFromEvents:t}},e}(),qt="msgRequestId",Ht="diagnostics",Vt="diagnosticsWithoutId",zt="Authorization",Qt="Accept",Kt="Content-Type",Gt="application/json",Jt=0;function Xt(){return Xt=d((function(e,t){var n,r,i,o,s,a,u,l,d,h,p,v,g,y,_,m,S,b,T,w,I,C,E,A;return f(this,(function(f){switch(f.label){case 0:if(n=ft(),r=e.action,i=e.cvid,o=e.prefix,s=e.token,a=e.headers,u=e.useNoBearerAuth,l=e.additionalInfo,d=function(e,t){var n=function(e){switch(e){case"getsuggestions":return{path:"suggestions",version:"v1",webMethodType:"GET"};case"init":return{path:"init",version:"v1",webMethodType:"GET"};case"initv2":return{path:"init",version:"v2",webMethodType:"GET"};case"logevents":return{path:"events",version:"v1",webMethodType:"POST"};case"query":return{path:"query",version:"v1",webMethodType:"POST"};case"queryv2":return{path:"query",version:"v2",webMethodType:"POST"};case"adaptiveactions":return{path:"adaptiveactions",version:"v1",webMethodType:"POST"};case"resolveEntitiesAsync":return{path:"resolveEntitiesAsync",version:"v1",webMethodType:"POST"};case"spoquery":return{path:"sharepoint/spoquery",version:"v1",webMethodType:"POST"};case"userConfig":return{path:"userConfig",version:"v1",webMethodType:"POST"};case"unfurl":return{path:"unfurl",version:"v1",webMethodType:"POST"};case"getrecommendations":return{path:"recommendations",version:"v1",webMethodType:"POST"};default:return}}(e);if(n){var r=n.path,i=n.version,o=n.webMethodType,s=_t(t);return{url:"".concat(s,"/search/api/").concat(i,"/").concat(r),webMethodType:o}}}(r,t),!d)throw"No base url found for the given action type";h=function(e,t,n){var r={};switch(e){case"init":case"initv2":case"query":case"userConfig":t&&(r={cvid:t});break;case"spoquery":t&&(r={correlationId:t});break;case"getsuggestions":t&&(r={cvid:t}),r=c({},r,{query:n||""});break;default:return r}return r}(r,i,o),h&&(l=c({},l,h)),p=l&&"1"===l[Ht],v=l&&"1"===l[Vt],p&&!v&&(l=c({},l,{msgRequestId:n})),g=d.url,y=d.webMethodType,_=Zt(g,l),m=function(e,t,n,r,i,o){var s=new Ft(e,t,Jt,!1,r,!0);n&&s.setHeader(zt,o?n:"Bearer ".concat(n)),s.setHeader(Qt,Gt);var a=!0;if(i)for(var u in i)if(u){var c=u.toUpperCase();c===Kt.toUpperCase()&&(a=!1),c!==Qt.toUpperCase()&&c!==zt.toUpperCase()&&s.setHeader(u,i[u])}return"POST"===t&&a&&s.setHeader(Kt,Gt),s}(_,y,s,o,a,u),f.label=1;case 1:return f.trys.push([1,3,,4]),[4,m.go()];case 2:return S=f.sent(),[3,4];case 3:if((b=f.sent()).midgardError)throw{prefix:o||"",qfResponse:(T=b).responseText||"",requestId:n,responseHeaders:JSON.stringify(T.headers),statusCode:T.statusCode};throw b;case 4:return w={prefix:o||"",qfResponse:S.Body,requestId:n,responseHeaders:JSON.stringify(S.Headers),statusCode:S.StatusCode},p&&performance&&(v?(C=S.Headers&&parseInt(S.Headers["content-length"],10)||0,I=performance.getEntriesByName?performance.getEntriesByName(_,"resource").filter((function(e){return!e.encodedBodySize||!C||e.encodedBodySize===C})).slice(-1):[]):(E=performance.getEntriesByType("resource"),A="".concat(qt,"=").concat(n),I=E.filter((function(e){return e.name.substr(-A.length)===A}))),I&&(w=c({},w,{diagnostics:JSON.stringify(I)}))),[2,w]}}))})),Xt.apply(this,arguments)}function Zt(e,t){if(t)for(var n in t)if(n&&n!==Ht&&n!==Vt){var r=e.indexOf("?")>-1?"&":"?";e+="".concat(r).concat(encodeURIComponent(n),"=").concat(encodeURIComponent(t[n]))}return e}const $t="1.20250331.2.0";var Yt=function(e){return e[e.current=2]="current",e}({}),en=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:globalThis.fetch;return function(t,n){var r,i,o,s,a=n.headers,u=n.method,l=void 0===u?"GET":u,d=n.body,f=ht(n,["headers","method","body"]),p="string"==typeof(r=t)?new URL(r):r instanceof URL?r:r instanceof Request?new URL(r.url):void 0;return p?-1===["GET","POST"].indexOf(l.toUpperCase())?e(t,n):"POST"!==l.toUpperCase()||(i=function(e){if("string"==typeof e)try{return JSON.parse(e)}catch(e){}}(d),i)?(p.searchParams.set("ConvertSimpleRequest",l),e(String(p),h(c({},f),{method:"POST",body:JSON.stringify(h(c({},i),{__request_headers:(o=a,s={},o&&new Headers(o).forEach((function(e,t){s[t]=e})),s)}))}))):e(t,n):e(t,n)}}(globalThis.fetch);function tn(e){switch(e){case"SPHomeWeb":return"SPHome";case"TeamSiteSearch":case"CommSiteSearch":case"HubSiteSearch":case"MSW":return"ODSP";case"officehome":case"officehomemsa":case"officehomeuwp":return"OfficeDotCom";case"DelveWeb":return"DelveWeb";case"TeamsSearch":case"Teams.RoomsNearby":case"teams":return"MicrosoftTeams";case"BingForBusinessWeb":case"msb.ux.webserp":case"msb.ux.workserp":case"Wsb.MiniSerp":case"Wsb.PreviewPane":return"Bing";case"wsb.ux.workserp":return"WsbBingVertical";case"Owa.react":case"Outlook.ios":case"Outlook.android":case"OutlookOPX":return"OwaMail";default:return}}var nn=s(51);const rn=(0,nn.regs)("searchux.clientId","",{required:!0,tags:["session","deprecated"]});var on="LokiSubstrateProxyQuery";function sn(){return(sn=d((function(e,t,n){var r,i,o,s,a,u,l,d,p,v,g,y,_,m,S;return f(this,(function(f){switch(f.label){case 0:r=dt(),i=e.additionalInfo,o=e.cvid,s=e.prefix,a=e.token,u=e.headers,l=new URL("api/v2/graphql?operationName=".concat(on),n),(d=i&&"1"===i[Ht])&&l.searchParams.append(qt,r),f.label=1;case 1:return f.trys.push([1,4,,5]),[4,en(l,{method:"POST",headers:h(c({},u),{Authorization:"Bearer "+a,"Content-Type":"application/json",Accept:"application/json","X-ClientFeature":"Search","X-ClientType":tn(rn()),"X-RootCorrelationId":t,"X-ClientCorrelationId":o}),body:JSON.stringify(cn(e))})];case 2:return[4,(p=f.sent()).json()];case 3:return y=f.sent(),_=null==y||null===(v=y.data)||void 0===v?void 0:v.proxyThreeS,m=!_&&(null===(g=y.errors)||void 0===g?void 0:g.length),[2,{prefix:s||"",qfResponse:_||"",requestId:r,responseHeaders:JSON.stringify(an(p)),statusCode:m?500:p.status,diagnostics:JSON.stringify(un(d,l.toString(),p))}];case 4:return S=f.sent(),console.log("makeLokiSubstrateRequest failed:",S),[2,{prefix:s||"",qfResponse:JSON.stringify({}),requestId:r,responseHeaders:JSON.stringify(an(p)),statusCode:(null==p?void 0:p.status)||500,diagnostics:JSON.stringify(un(d,l.toString(),p))}];case 5:return[2]}}))}))).apply(this,arguments)}function an(e){var t={};return(null==e?void 0:e.headers)&&e.headers.forEach((function(e,n){return t[n]=e})),t}function un(e,t,n){var r={};if(e){var i=performance.getEntriesByName(t,"resource")[0];i&&(r=h(c({},r,i.toJSON()),{name:""}))}return(null==n?void 0:n.headers)&&(r.lokiOutboundRequestDuration=n.headers.get("x-outboundrequestduration"),r.lokiInboundDuration=n.headers.get("x-inboundduration"),r.lokiCorrelationId=n.headers.get("x-correlationid"),r.lokiDatacenter=n.headers.get("x-datacenter")),[r]}function cn(e){return{operationName:on,query:ln(e),variables:{}}}function ln(e){var t=Zt("",e.additionalInfo);return"query ".concat(on," {\n    proxyThreeS(\n      bodyJson: ").concat(JSON.stringify(e.prefix),",\n      queryParamsString: ").concat(JSON.stringify(t),")\n  }")}var dn=s(567),hn=function(e,t,n){try{return(t||fn)(e)}catch(e){return n}},fn=function(){return{}};function pn(e){try{var t=e.parsedBody;return t.error&&t.error.code}catch(e){return null}}function vn(e,t,n,r,i,o,s,a){var u=dt(),l=c({},t&&{Authorization:s?t:"Bearer ".concat(t)},{"Client-Request-Id":u},r&&{"X-AnchorMailbox":r},r&&{"X-RoutingParameter-SessionKey":r},i&&{"X-Client-Language":i},o&&{"X-Client-Flights":o},_n(),a),d=new XMLHttpRequest,h="POST";return d.open(h,e,n),st(l,(function(e,t){d.setRequestHeader(t,e)})),t||(d.withCredentials=!0),{xhr:d,headers:l,method:h}}function gn(){return(gn=d((function(e){var t,n,r,i,o,s,a,u;return f(this,(function(l){return t=e.url,n=e.token,r=e.body,i=e.anchorMailbox,o=e.locale,s=e.substrateSearchClientFlights,a=e.useNoBearerAuth,u=e.additionalHeaders,[2,new Promise((function(e,l){var d=vn(t,n,!0,i,o,s,a,u),h=d.xhr,f=d.headers,p=d.method;h.onload=function(){var t=h.getResponseHeader("request-id");h.status>=200&&h.status<300?e(c({statusCode:h.status},""===h.responseText?{}:JSON.parse(h.responseText),t?{responseRequestId:t}:{})):l(c({statusCode:h.status,metadata:{headers:f,method:p,body:r}},""===h.responseText?{}:JSON.parse(h.responseText),t?{responseRequestId:t}:{}))},h.onerror=function(){var e="An Unknown Error occurred";0===h.status&&4===h.readyState&&(e+=" - no interaction with the server");var t=h.getResponseHeader("request-id");l(c({statusCode:500,metadata:{headers:f,method:p,body:r},defaultMessage:e},t?{responseRequestId:t}:{}))},h.send(r)}))]}))}))).apply(this,arguments)}function yn(e){var t=e.action,n=e.token,r=e.query,i=e.cvid,o=e.additionalInfo,s=e.substrateHostName,a=e.substrateHostNameWithIFrame,u=e.useNoBearerAuth,l=e.reuseIframe,d=e.lokiProxy,f=e.logicalSearchId,p={action:t,token:n,prefix:r,cvid:i,additionalInfo:o,headers:h(c({},_n(),e.additionalHeaders),{LogicalId:e.logicalSearchId}),useNoBearerAuth:u},v=d?function(e,t,n){return sn.apply(this,arguments)}(p,f,d):s&&!a?function(e,t){return Xt.apply(this,arguments)}(p,s).catch((function(e){return e})):function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:mt,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return t=_t(t),wt[t]?At(e,t,n):function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:mt,t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e=_t(e),void 0===Tt[e]&&(Tt[e]=Ct(e,t).ready),Tt[e].then((function(){return wt[e]=!0,function(n){return At(n,e,t)}}))}(t,n).then((function(t){return t(e)}))}(p,s,l);return v.then((function(e){var t,n=h(c({},e),{qfResponse:(t=e.qfResponse,t?t.replace(Sn,'$1"$2"$3'):t)});try{n.parsedBody=JSON.parse(n.qfResponse)}catch(e){}if(200!==n.statusCode)throw n;return n}))}function _n(){return{Accept:"application/json;odata=verbose","Content-Type":"application/json","X-Client-LocalTime":(e=new Date,kt(e,!0)),"X-Client-Version":$t};var e}function mn(){return{diagnostics:arguments.length>0&&void 0!==arguments[0]&&arguments[0]?"1":"0"}}var Sn=/("DocId"\s*:\s*)(\d+)([,\s}])/gi;const bn=function(e,t){for(var n=-1,r=null==e?0:e.length,i=Array(r);++n<r;)i[n]=t(e[n],n,e);return i},Tn=function(e,t){return e===t||e!=e&&t!=t},wn=function(e,t){for(var n=e.length;n--;)if(Tn(e[n][0],t))return n;return-1};var In=Array.prototype.splice;function Cn(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}Cn.prototype.clear=function(){this.__data__=[],this.size=0},Cn.prototype.delete=function(e){var t=this.__data__,n=wn(t,e);return!(n<0||(n==t.length-1?t.pop():In.call(t,n,1),--this.size,0))},Cn.prototype.get=function(e){var t=this.__data__,n=wn(t,e);return n<0?void 0:t[n][1]},Cn.prototype.has=function(e){return wn(this.__data__,e)>-1},Cn.prototype.set=function(e,t){var n=this.__data__,r=wn(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this};const En=Cn,An=Z(Object,"create");var Pn=Object.prototype.hasOwnProperty,kn=Object.prototype.hasOwnProperty;function Rn(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}Rn.prototype.clear=function(){this.__data__=An?An(null):{},this.size=0},Rn.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},Rn.prototype.get=function(e){var t=this.__data__;if(An){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return Pn.call(t,e)?t[e]:void 0},Rn.prototype.has=function(e){var t=this.__data__;return An?void 0!==t[e]:kn.call(t,e)},Rn.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=An&&void 0===t?"__lodash_hash_undefined__":t,this};const On=Rn,Bn=function(e,t){var n,r,i=e.__data__;return("string"==(r=typeof(n=t))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?i["string"==typeof t?"string":"hash"]:i.map};function Mn(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}Mn.prototype.clear=function(){this.size=0,this.__data__={hash:new On,map:new(Y||En),string:new On}},Mn.prototype.delete=function(e){var t=Bn(this,e).delete(e);return this.size-=t?1:0,t},Mn.prototype.get=function(e){return Bn(this,e).get(e)},Mn.prototype.has=function(e){return Bn(this,e).has(e)},Mn.prototype.set=function(e,t){var n=Bn(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this};const xn=Mn;function Dn(e){var t=this.__data__=new En(e);this.size=t.size}Dn.prototype.clear=function(){this.__data__=new En,this.size=0},Dn.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},Dn.prototype.get=function(e){return this.__data__.get(e)},Dn.prototype.has=function(e){return this.__data__.has(e)},Dn.prototype.set=function(e,t){var n=this.__data__;if(n instanceof En){var r=n.__data__;if(!Y||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new xn(r)}return n.set(e,t),this.size=n.size,this};const Nn=Dn;function Wn(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new xn;++t<n;)this.add(e[t])}Wn.prototype.add=Wn.prototype.push=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this},Wn.prototype.has=function(e){return this.__data__.has(e)};const jn=Wn,Ln=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1},Un=function(e,t,n,r,i,o){var s=1&n,a=e.length,u=t.length;if(a!=u&&!(s&&u>a))return!1;var c=o.get(e),l=o.get(t);if(c&&l)return c==t&&l==e;var d=-1,h=!0,f=2&n?new jn:void 0;for(o.set(e,t),o.set(t,e);++d<a;){var p=e[d],v=t[d];if(r)var g=s?r(v,p,d,t,e,o):r(p,v,d,e,t,o);if(void 0!==g){if(g)continue;h=!1;break}if(f){if(!Ln(t,(function(e,t){if(s=t,!f.has(s)&&(p===e||i(p,e,n,r,o)))return f.push(t);var s}))){h=!1;break}}else if(p!==v&&!i(p,v,n,r,o)){h=!1;break}}return o.delete(e),o.delete(t),h},Fn=k.Uint8Array,qn=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n},Hn=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n};var Vn=R?R.prototype:void 0,zn=Vn?Vn.valueOf:void 0;const Qn=function(e,t){for(var n=-1,r=t.length,i=e.length;++n<r;)e[i+n]=t[n];return e},Kn=function(e,t,n){var r=t(e);return ye(e)?r:Qn(r,n(e))},Gn=function(){return[]};var Jn=Object.prototype.propertyIsEnumerable,Xn=Object.getOwnPropertySymbols;const Zn=Xn?function(e){return null==e?[]:(e=Object(e),function(e,t){for(var n=-1,r=null==e?0:e.length,i=0,o=[];++n<r;){var s=e[n];t(s)&&(o[i++]=s)}return o}(Xn(e),(function(t){return Jn.call(e,t)})))}:Gn,$n=function(e){return Kn(e,rt,Zn)};var Yn=Object.prototype.hasOwnProperty,er="[object Arguments]",tr="[object Array]",nr="[object Object]",rr=Object.prototype.hasOwnProperty;const ir=function e(t,n,r,i,o){return t===n||(null==t||null==n||!_e(t)&&!_e(n)?t!=t&&n!=n:function(e,t,n,r,i,o){var s=ye(e),a=ye(t),u=s?tr:pe(e),c=a?tr:pe(t),l=(u=u==er?nr:u)==nr,d=(c=c==er?nr:c)==nr,h=u==c;if(h&&Ve(e)){if(!Ve(t))return!1;s=!0,l=!1}if(h&&!l)return o||(o=new Nn),s||Ye(e)?Un(e,t,n,r,i,o):function(e,t,n,r,i,o,s){switch(n){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!o(new Fn(e),new Fn(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return Tn(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var a=qn;case"[object Set]":var u=1&r;if(a||(a=Hn),e.size!=t.size&&!u)return!1;var c=s.get(e);if(c)return c==t;r|=2,s.set(e,t);var l=Un(a(e),a(t),r,i,o,s);return s.delete(e),l;case"[object Symbol]":if(zn)return zn.call(e)==zn.call(t)}return!1}(e,t,u,n,r,i,o);if(!(1&n)){var f=l&&rr.call(e,"__wrapped__"),p=d&&rr.call(t,"__wrapped__");if(f||p){var v=f?e.value():e,g=p?t.value():t;return o||(o=new Nn),i(v,g,n,r,o)}}return!!h&&(o||(o=new Nn),function(e,t,n,r,i,o){var s=1&n,a=$n(e),u=a.length;if(u!=$n(t).length&&!s)return!1;for(var c=u;c--;){var l=a[c];if(!(s?l in t:Yn.call(t,l)))return!1}var d=o.get(e),h=o.get(t);if(d&&h)return d==t&&h==e;var f=!0;o.set(e,t),o.set(t,e);for(var p=s;++c<u;){var v=e[l=a[c]],g=t[l];if(r)var y=s?r(g,v,l,t,e,o):r(v,g,l,e,t,o);if(!(void 0===y?v===g||i(v,g,n,r,o):y)){f=!1;break}p||(p="constructor"==l)}if(f&&!p){var _=e.constructor,m=t.constructor;_==m||!("constructor"in e)||!("constructor"in t)||"function"==typeof _&&_ instanceof _&&"function"==typeof m&&m instanceof m||(f=!1)}return o.delete(e),o.delete(t),f}(e,t,n,r,i,o))}(t,n,r,i,e,o))},or=function(e){return e==e&&!j(e)},sr=function(e,t){return function(n){return null!=n&&n[e]===t&&(void 0!==t||e in Object(n))}},ar=function(e){return"symbol"==typeof e||_e(e)&&"[object Symbol]"==W(e)};var ur=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,cr=/^\w*$/;const lr=function(e,t){if(ye(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!ar(e))||cr.test(e)||!ur.test(e)||null!=t&&e in Object(t)};function dr(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var n=function(){var r=arguments,i=t?t.apply(this,r):r[0],o=n.cache;if(o.has(i))return o.get(i);var s=e.apply(this,r);return n.cache=o.set(i,s)||o,s};return n.cache=new(dr.Cache||xn),n}dr.Cache=xn;var hr=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,fr=/\\(\\)?/g;const pr=(vr=dr((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(hr,(function(e,n,r,i){t.push(r?i.replace(fr,"$1"):n||e)})),t}),(function(e){return 500===gr.size&&gr.clear(),e})),gr=vr.cache,vr);var vr,gr,yr=R?R.prototype:void 0,_r=yr?yr.toString:void 0;const mr=function e(t){if("string"==typeof t)return t;if(ye(t))return bn(t,e)+"";if(ar(t))return _r?_r.call(t):"";var n=t+"";return"0"==n&&1/t==-1/0?"-0":n},Sr=function(e){return null==e?"":mr(e)},br=function(e,t){return ye(e)?e:lr(e,t)?[e]:pr(Sr(e))},Tr=function(e){if("string"==typeof e||ar(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t},wr=function(e,t){for(var n=0,r=(t=br(t,e)).length;null!=e&&n<r;)e=e[Tr(t[n++])];return n&&n==r?e:void 0},Ir=function(e,t){return null!=e&&t in Object(e)},Cr=function(e){return"function"==typeof e?e:null==e?ot:"object"==typeof e?ye(e)?function(e,t){return lr(e)&&or(t)?sr(Tr(e),t):function(n){var r=function(e,t,n){var r=null==e?void 0:wr(e,t);return void 0===r?void 0:r}(n,e);return void 0===r&&r===t?function(e,t){return null!=e&&function(e,t,n){for(var r=-1,i=(t=br(t,e)).length,o=!1;++r<i;){var s=Tr(t[r]);if(!(o=null!=e&&n(e,s)))break;e=e[s]}return o||++r!=i?o:!!(i=null==e?0:e.length)&&ve(i)&&Qe(s,i)&&(ye(e)||Ue(e))}(e,t,Ir)}(n,e):ir(t,r,3)}}(e[0],e[1]):function(e){var t=function(e){for(var t=rt(e),n=t.length;n--;){var r=t[n],i=e[r];t[n]=[r,i,or(i)]}return t}(e);return 1==t.length&&t[0][2]?sr(t[0][0],t[0][1]):function(n){return n===e||function(e,t,n,r){var i=n.length,o=i;if(null==e)return!o;for(e=Object(e);i--;){var s=n[i];if(s[2]?s[1]!==e[s[0]]:!(s[0]in e))return!1}for(;++i<o;){var a=(s=n[i])[0],u=e[a],c=s[1];if(s[2]){if(void 0===u&&!(a in e))return!1}else{var l=new Nn;if(!ir(c,u,3,undefined,l))return!1}}return!0}(n,0,t)}}(e):function(e){return lr(e)?me(Tr(e)):function(e){return function(t){return wr(t,e)}}(e)}(e)},Er=function(e,t){var n=-1,r=ge(e)?Array(e.length):[];return it(e,(function(e,i,o){r[++n]=t(e,i,o)})),r},Ar=function(e,t){return(ye(e)?bn:Er)(e,Cr(t))},Pr=function(e,t,n){var r=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(n=n>i?i:n)<0&&(n+=i),i=t>n?0:n-t>>>0,t>>>=0;for(var o=Array(i);++r<i;)o[r]=e[r+t];return o};var kr=/\s/,Rr=/^\s+/,Or=/^[-+]0x[0-9a-f]+$/i,Br=/^0b[01]+$/i,Mr=/^0o[0-7]+$/i,xr=parseInt,Dr=1/0;const Nr=function(e){var t=function(e){return e?(e=function(e){if("number"==typeof e)return e;if(ar(e))return NaN;if(j(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=j(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=function(e){return e?e.slice(0,function(e){for(var t=e.length;t--&&kr.test(e.charAt(t)););return t}(e)+1).replace(Rr,""):e}(e);var n=Br.test(e);return n||Mr.test(e)?xr(e.slice(2),n?2:8):Or.test(e)?NaN:+e}(e))===Dr||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0}(e),n=t%1;return t==t?n?t-n:t:0};var Wr=Math.ceil,jr=Math.max;function Lr(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Ur(e,t){if(e){if("string"==typeof e)return Lr(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Lr(e,t):void 0}}function Fr(e){return function(e){if(Array.isArray(e))return Lr(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Ur(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var qr=Object.prototype.hasOwnProperty;const Hr=function(e){if(null==e)return!0;if(ge(e)&&(ye(e)||"string"==typeof e||"function"==typeof e.splice||Ve(e)||Ye(e)||Ue(e)))return!e.length;var t=pe(e);if("[object Map]"==t||"[object Set]"==t)return!e.size;if(T(e))return!E(e).length;for(var n in e)if(qr.call(e,n))return!1;return!0},Vr=function(){try{var e=Z(Object,"defineProperty");return e({},"",{}),e}catch(e){}}(),zr=function(e,t,n){"__proto__"==t&&Vr?Vr(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n},Qr=function(e,t,n){(void 0!==n&&!Tn(e[t],n)||void 0===n&&!(t in e))&&zr(e,t,n)};var Kr=t&&!t.nodeType&&t,Gr=Kr&&e&&!e.nodeType&&e,Jr=Gr&&Gr.exports===Kr?k.Buffer:void 0;Jr&&Jr.allocUnsafe;var Xr=Object.create;const Zr=function(){function e(){}return function(t){if(!j(t))return{};if(Xr)return Xr(t);e.prototype=t;var n=new e;return e.prototype=void 0,n}}(),$r=w(Object.getPrototypeOf,Object);var Yr=Function.prototype,ei=Object.prototype,ti=Yr.toString,ni=ei.hasOwnProperty,ri=ti.call(Object);const ii=function(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]};var oi=Object.prototype.hasOwnProperty;const si=function(e,t,n){var r=e[t];oi.call(e,t)&&Tn(r,n)&&(void 0!==n||t in e)||zr(e,t,n)};var ai=Object.prototype.hasOwnProperty;const ui=function(e){return ge(e)?nt(e,!0):function(e){if(!j(e))return function(e){var t=[];if(null!=e)for(var n in Object(e))t.push(n);return t}(e);var t=T(e),n=[];for(var r in e)("constructor"!=r||!t&&ai.call(e,r))&&n.push(r);return n}(e)},ci=function e(t,n,r,i,o){t!==n&&De(n,(function(s,a){if(o||(o=new Nn),j(s))!function(e,t,n,r,i,o,s){var a,u=ii(e,n),c=ii(t,n),l=s.get(c);if(l)Qr(e,n,l);else{var d=o?o(u,c,n+"",e,t,s):void 0,h=void 0===d;if(h){var f=ye(c),p=!f&&Ve(c),v=!f&&!p&&Ye(c);d=c,f||p||v?ye(u)?d=u:_e(a=u)&&ge(a)?d=function(e,t){var n=-1,r=e.length;for(t||(t=Array(r));++n<r;)t[n]=e[n];return t}(u):p?(h=!1,d=function(e,t){return e.slice()}(c)):v?(h=!1,d=function(e,t){var n=function(e){var t=new e.constructor(e.byteLength);return new Fn(t).set(new Fn(e)),t}(e.buffer);return new e.constructor(n,e.byteOffset,e.length)}(c)):d=[]:function(e){if(!_e(e)||"[object Object]"!=W(e))return!1;var t=$r(e);if(null===t)return!0;var n=ni.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&ti.call(n)==ri}(c)||Ue(c)?(d=u,Ue(u)?d=function(e){return function(e,t,n,r){var i=!n;n||(n={});for(var o=-1,s=t.length;++o<s;){var a=t[o],u=void 0;void 0===u&&(u=e[a]),i?zr(n,a,u):si(n,a,u)}return n}(e,ui(e))}(u):j(u)&&!L(u)||(d=function(e){return"function"!=typeof e.constructor||T(e)?{}:Zr($r(e))}(c))):h=!1}h&&(s.set(c,d),i(d,c,r,o,s),s.delete(c)),Qr(e,n,d)}}(t,n,a,r,e,i,o);else{var u=i?i(ii(t,a),s,a+"",t,n,o):void 0;void 0===u&&(u=s),Qr(t,a,u)}}),ui)};var li=Math.max;const di=Vr?function(e,t){return Vr(e,"toString",{configurable:!0,enumerable:!1,value:(n=t,function(){return n}),writable:!0});var n}:ot;var hi=Date.now;const fi=function(e){var t=0,n=0;return function(){var r=hi(),i=16-(r-n);if(n=r,i>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}(di),pi=(vi=function(e,t,n){ci(e,t,n)},function(e,t){return fi(function(e,t,n){return t=li(void 0===t?e.length-1:t,0),function(){for(var r=arguments,i=-1,o=li(r.length-t,0),s=Array(o);++i<o;)s[i]=r[t+i];i=-1;for(var a=Array(t+1);++i<t;)a[i]=r[i];return a[t]=n(s),function(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}(e,this,a)}}(e,void 0,ot),e+"")}((function(e,t){var n=-1,r=t.length,i=r>1?t[r-1]:void 0,o=r>2?t[2]:void 0;for(i=vi.length>3&&"function"==typeof i?(r--,i):void 0,o&&function(e,t,n){if(!j(n))return!1;var r=typeof t;return!!("number"==r?ge(n)&&Qe(t,n.length):"string"==r&&t in n)&&Tn(n[t],e)}(t[0],t[1],o)&&(i=r<3?void 0:i,r=1),e=Object(e);++n<r;){var s=t[n];s&&vi(e,s,n)}return e})));var vi;const gi=function(e,t,n,r){if(!j(e))return e;for(var i=-1,o=(t=br(t,e)).length,s=o-1,a=e;null!=a&&++i<o;){var u=Tr(t[i]),c=n;if("__proto__"===u||"constructor"===u||"prototype"===u)return e;if(i!=s){var l=a[u];void 0===(c=r?r(l,u,a):void 0)&&(c=j(l)?l:Qe(t[i+1])?[]:{})}si(a,u,c),a=a[u]}return e},yi=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)Qn(t,Zn(e)),e=$r(e);return t}:Gn,_i=function(e,t){return function(e,t){if(null==e)return{};var n=bn(function(e){return Kn(e,ui,yi)}(e),(function(e){return[e]}));return t=Cr(t),function(e,t,n){for(var r=-1,i=t.length,o={};++r<i;){var s=t[r],a=wr(e,s);n(a,s)&&gi(o,br(s,e),a)}return o}(e,n,(function(e,n){return t(e,n[0])}))}(e,function(e){if("function"!=typeof e)throw new TypeError("Expected a function");return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}(Cr(t)))};var mi=Math.max;const Si=(bi=function(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var i=null==n?0:Nr(n);return i<0&&(i=mi(r+i,0)),function(e,t,n,r){for(var i=e.length,o=n+-1;++o<i;)if(t(e[o],o,e))return o;return-1}(e,Cr(t),i)},function(e,t,n){var r=Object(e);if(!ge(e)){var i=Cr(t);e=rt(e),t=function(e){return i(r[e],e,r)}}var o=bi(e,t,n);return o>-1?r[i?e[o]:o]:void 0});var bi;function Ti(e,t){return!!t&&!!e}var wi=function(){function e(t,n){(0,v._)(this,e),this.getAuthToken=t,this.logError=n}var t=e.prototype;return t.getToken=function(e){if(this._authToken&&this._authToken.tokenExpiry)return e&&0===this._authToken.tokenExpiry||this._authToken.tokenExpiry>(new Date).getTime()?this._authToken.token:void 0},t.refreshToken=function(){var e=this;return d((function(){var t;return f(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),e._authToken=void 0,[4,e.getAuthToken().then((function(t){return e._authToken=t,t.token}))];case 1:return[2,n.sent()];case 2:return t=n.sent(),e.logError("failure getting token",t),[2];case 3:return[2]}}))}))()},e}(),Ii=function(e){function t(e,n,r){var i,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1e3,s=arguments.length>4?arguments[4]:void 0,a=arguments.length>5?arguments[5]:void 0,u=arguments.length>6?arguments[6]:void 0,c=arguments.length>7?arguments[7]:void 0,l=arguments.length>8?arguments[8]:void 0,d=arguments.length>9?arguments[9]:void 0,h=arguments.length>10?arguments[10]:void 0;return(0,v._)(this,t),(i=function(e,t,n){return t=y(t),function(e,t){return!t||"object"!==(0,m._)(t)&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}(e,_()?Reflect.construct(t,n||[],y(e).constructor):t.apply(e,n))}(this,t,[e,"/search/api/v1/events?scenario={0}","Search3sLoggingBeforeUnload",n,r,o,c]))._xAnchorMailbox=s,i.locale=a,i.useNoBearerAuth=u,i.log3sSuccess=l,i.disableMonitorTypeRequest=d,i.enableDiagMetadataOnError=h,i}!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&S(e,t)}(t,e);var n=t.prototype;return n.log3SEvent=function(e,t){this.logEvent(e,t,e.EventKey)},n.sendLogBatch=function(e,t){var n=this,r=t.token,i=t.hostEndpoint.replace("{0}",e.scenario),o=this.getEventsPartitions(e).batches;st(o,(function(t){var o=vn(i,r,!1,n._xAnchorMailbox,n.locale,void 0,n.useNoBearerAuth).xhr;try{var s,a;o.send(JSON.stringify(t)),ct((function(){return"Event Logged to 3s Events API on Flush:\n"}),{scenario:e.scenario,batchToSend:e.ebatch,body:e.body},"\nTime: ",Math.floor(Date.now())),null===(s=(a=n).log3sSuccess)||void 0===s||s.call(a,JSON.stringify(e.body))}catch(t){var u,c;t.message.includes("Synchronous XHR")&&(null===(u=(c=n).log3sSuccess)||void 0===u||u.call(c,JSON.stringify(e.body)))}}))},n.sendLogBatchAsync=function(e,t){var n=this,r=t.token,i=t.hostEndpoint,o=t.host,s=this.getEventsPartitions(e),a=s.batches,u=s.ebatches,c=i.replace("{0}",e.scenario),l=!1;st(a,(function(t,i){return(n.disableMonitorTypeRequest?n.sendEventsWithoutMonitor(c,e,r,t):n.sendEventsWithMonitor(o,e,r,t)).then((function(){var t,r;null===(t=(r=n).log3sSuccess)||void 0===t||t.call(r,JSON.stringify(e.body))})).catch((function(t){n.mergeBack(e.scenario,u[i]),!l&&(l=500===t.statusCode)}))})),l&&this.increaseBatchTimeout()},n.createBatchToSend=function(e,t){return{scenario:t,ebatch:e,body:Ar(e,(function(e,t){return{Key:t,Value:Ar(e.events,(function(e){return e}))}}))}},n.sendEventsWithMonitor=function(e,t,n,r){var i=this;return at((function(){return function(e){var t,n,r,i,o,s,a=e.action,u=e.dataSource,l=e.token,d=e.query,f=e.cvid,p=e.logicalSearchId,v=e.additionalInfo,g=e.additionalHeaders,y=e.anchorMailbox,_=e.locale,S=e.substrateSearchClientFlights,b=e.logDiagnosticsMetrics,T=void 0!==b&&b,w=e.substrateHostName,I=e.substrateHostNameWithIFrame,C=e.useNoBearerAuth,E=e.reuseIframe,A=e.lokiProxy,P=dt(),k=Yt.current,R=function(e){return function(e,t){var n=t.version,r=t.clientRequestId,i=t.logicalSearchId,o=t.conversationId,s=t.providerName,a=function(e,t){var n,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=-1,o="Unknown",s="Unknown",a="Unknown",u="Unknown",c="Unknown",l=null,d=""!==t;try{var h=JSON.parse(e.responseHeaders);i=h["x-end2endlatencyms"]||-2,o=h["x-calculatedbetarget"]||"NotProvided",s=h["request-id"]||"NotProvided",a=h["x-msedge-ref"]||"NotProvided",u=h["x-besku"]||"NotProvided",c=h["x-searchplatform"]||"NotProvided"}catch(e){}try{r&&(l=JSON.stringify(function(e){var t=JSON.parse(e)[0];return t.name,ht(t,["name"])}(e.diagnostics)))}catch(e){}return n=function(e){try{var t=e.parsedBody;return t.Instrumentation&&t.Instrumentation.TraceId}catch(e){return null}}(e),{status:e.statusCode,serverLatency:i,serverName:o,requestId:n||s,msEdgeReference:a,anchorMailboxSet:d,xBeSku:u,searchPlatform:c,error:pn(e),diagnostics:l}}(e,t.anchorMailbox,t.logDiagnosticsMetrics);return h(c({traceId:a.requestId||r,version:n,status:e.statusCode},s&&{providerName:s}),{properties:h(c({},a),{logicalSearchId:i,clientRequestId:r,conversationId:o})})}(e,{anchorMailbox:y,logDiagnosticsMetrics:T,clientRequestId:P,conversationId:f,logicalSearchId:p,version:k})};return(n=yn,r=(t={monitorName:u,factoryErrorPropExtractor:function(e){return{version:k,status:0,properties:{promiseFactoryFailed:!0}}},successPropExtractor:R,failurePropExtractor:R}).monitorName,i=t.factoryErrorPropExtractor,o=t.successPropExtractor,s=t.failurePropExtractor,function(e){var t,a=function(t,n){var i=function(e){if(e&&"object"===(void 0===e?"undefined":(0,m._)(e))&&"searchText"in e&&"string"==typeof e.searchText)return e.searchText.length}(e),o=Math.round(performance.now())-c;l.dispatch({eventType:"QOSSTOP",name:u,result:t,nameDetail:r,startTimestamp:c,totalTime:o,properties:{LogicalId:n.properties&&n.properties.logicalSearchId,ConversationId:n.properties&&n.properties.conversationId,Version:n.version,TraceId:n.traceId,Status:n.status,ProviderName:n.providerName,Latency:o,properties:JSON.stringify(n.properties),characterCount:i}})},u="ResponseReceived",c=Math.round(performance.now()),l=(0,dn.getDispatcher)();l.dispatch({eventType:"QOSSTART",name:u,nameDetail:r,timestamp:c});try{t=n(e)}catch(e){return a("FAILURE",hn(e,i,{status:0,properties:{promiseFactoryFailed:!0}})),Promise.reject(e)}return t.then((function(e){return a("SUCCESS",hn(e,o,{status:0,properties:{successExtractorFailed:!0}})),Promise.resolve(e)}),(function(e){return a("FAILURE",hn(e,s,{status:0,properties:{failureExtractorFailed:!0}})),Promise.reject(e)}))})({action:a,token:l,query:d,cvid:f,logicalSearchId:p,additionalInfo:c({},mn(T),v),additionalHeaders:c({},{"Client-Request-Id":P},y&&{"X-AnchorMailbox":y},_&&{"X-Client-Language":_},S&&{"X-Client-Flights":S},_n(),g),substrateHostName:w,substrateHostNameWithIFrame:I,useNoBearerAuth:C,reuseIframe:E,lokiProxy:A})}({action:"logevents",dataSource:lt,token:n,query:JSON.stringify(r),additionalInfo:u({},"scenario",t.scenario),anchorMailbox:i._xAnchorMailbox,locale:i.locale,logDiagnosticsMetrics:!1,substrateHostName:e,useNoBearerAuth:i.useNoBearerAuth})})).then((function(e){ct((function(){var t;return"3sEventsAPI: batch logged successfully with TraceId:\n"+(null===(t=e.parsedBody)||void 0===t?void 0:t.Instrumentation.TraceId)+"\n"}),{scenario:t.scenario,batchToSend:t.ebatch,body:t.body},"\nTime: ",Math.floor(Date.now()))})).catch((function(e){var t,n,r,o;throw 401===e.statusCode&&i.refreshToken(),i.logError("call failure with TraceId: ".concat((null===(n=e.parsedBody)||void 0===n||null===(t=n.Instrumentation)||void 0===t?void 0:t.TraceId)||"",", Status: ").concat(e.statusCode||""),null===(o=e.parsedBody)||void 0===o||null===(r=o.error)||void 0===r?void 0:r.message,e.statusCode),e}))},n.sendEventsWithoutMonitor=function(e,t,n,r){var i=this;return at((function(){return function(e){return gn.apply(this,arguments)}({url:e,token:n,body:JSON.stringify(r),anchorMailbox:i._xAnchorMailbox,locale:i.locale,substrateSearchClientFlights:void 0,useNoBearerAuth:i.useNoBearerAuth})})).then((function(e){var n,r=null==e||null===(n=e.Instrumentation)||void 0===n?void 0:n.TraceId;ct((function(){return"3sEventsAPI: batch logged successfully with TraceId:\n"+r+"\n"}),{scenario:t.scenario,batchToSend:t.ebatch,body:t.body},"\nTime: ",Math.floor(Date.now()))})).catch((function(t){var n,o,s,a,u,c,l;throw 401===t.statusCode&&i.refreshToken(),s=(null==t||null===(n=t.Instrumentation)||void 0===n?void 0:n.TraceId)||"",a=t.statusCode,u=null!==(l=null==t||null===(o=t.error)||void 0===o?void 0:o.message)&&void 0!==l?l:t.defaultMessage,c=t.metadata?i.createErrorMetadataDetails(t.metadata,e,r):void 0,i.logError("call failure with TraceId: ".concat(s,", Status: ").concat(a),u,a,c?JSON.stringify(c):void 0),t}))},n.getEventsPartitions=function(e){var t=function(e,t,n){t=void 0===t?1:jr(Nr(t),0);var r=null==e?0:e.length;if(!r||t<1)return[];for(var i=0,o=0,s=Array(Wr(r/t));i<r;)s[o++]=Pr(e,i,i+=t);return s}(e.body,10),n=[];return st(t,(function(t){var r={};st(t,(function(t){r[t.Key]=e.ebatch[t.Key]})),n.push(r)})),{batches:t,ebatches:n}},n.createErrorMetadataDetails=function(e,t,n){var r=void 0;try{r=JSON.stringify(c({requestSize:this.getRequestSizeInBytes(e.body),url:t,body:e.body&&e.body.substring(0,1e4)},this.enableDiagMetadataOnError?{diagData:this.createDiagMetadataDetails(n)}:{}))}catch(e){e.message&&this.logError("Error in creating error metadata details",e.message)}return r},n.createDiagMetadataDetails=function(e){var t={};return st(e,(function(e){t[e.Key]=e.Value.length})),{eventsPerKey:t,numKeys:e.length}},n.getRequestSizeInBytes=function(e){return e?(new TextEncoder).encode(e).length:0},t}(function(){function e(t,n,r,i,o){var s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1e3,a=arguments.length>6?arguments[6]:void 0,u=arguments.length>7&&void 0!==arguments[7]?arguments[7]:"3sEventsAPI",c=arguments.length>8?arguments[8]:void 0,l=arguments.length>9&&void 0!==arguments[9]?arguments[9]:4,d=arguments.length>10?arguments[10]:void 0,h=arguments.length>11?arguments[11]:void 0;(0,v._)(this,e),this._defaultScenarioName=t,this._ApiEndpoint=n,this._localStorageKey=r,this.substrateHost=i,this.substrateHostFallback=o,this._batchTimeout=s,this.onError=a,this.apiName=u,this.isV2Schema=c,this.localStorageVersion=l,this.onInfo=d,this._batchProperties=h,this._batch={},this._retriesRemaining=3,this._logBatchAfterTimeout=0,this._timerIsRunning=!1,this._localStorageAvailable=!1,this._localStorageAvailable="undefined"!=typeof localStorage&&!!localStorage,this.getBatchFromLocalStorage(),this._selectedApiHost=this.getEventsDestination()}var t=e.prototype;return t.logEvent=function(e,t,n,r){this.addEvent(r||this._defaultScenarioName,t,e,n),this._timerIsRunning||(this.resetRetryCounter(),this.startTimer())},t.flush=function(){var e=this;return d((function(){return f(this,(function(t){switch(t.label){case 0:return clearTimeout(e._logBatchAfterTimeout),[4,e.logBatch()];case 1:return t.sent(),[2]}}))}))()},t.dispose=function(){clearTimeout(this._logBatchAfterTimeout),this.batchFlush()},t.getBatch=function(){return this._batch},t.testMergeBack=function(){var e=this,t=this.getBatchToSend();st(t,(function(t){e.isV2Schema?e.mergeBack(t.scenario,t.body,!1):e.mergeBack(t.scenario,t.ebatch,!1)}))},t.increaseBatchTimeout=function(){this._batchTimeout<3500&&(this._batchTimeout+=1e3)},t.refreshToken=function(){var e=this;return d((function(){return f(this,(function(t){switch(t.label){case 0:return[4,e._selectedApiHost.tokenProvider.refreshToken()];case 1:return t.sent(),[2]}}))}))()},t.logError=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"An error occurred",n=arguments.length>1?arguments[1]:void 0,r=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0,o="".concat(this.apiName,": ").concat(t+("string"==typeof n?", Error Message: ".concat(n):""));!function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r]}((function(){return o}),n),null===(e=this.onError)||void 0===e||e.call(this,o,r,i)},t.logInfo=function(e){var t;ct((function(){return e})),null===(t=this.onInfo)||void 0===t||t.call(this,e)},t.mergeBack=function(e,t){var n,r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];Ti(this._batch,this.isV2Schema)?(!this._batch[e]&&(this._batch[e]=[]),ye(t)?(n=this._batch[e]).push.apply(n,Fr(t)):(this._batch[e].push(t),t.numRetries<3&&r&&(t.numRetries++,this.startTimer()))):(!this._batch[e]||Hr(this._batch[e])?this._batch[e]=t:this._batch[e]=pi(this._batch[e],t),!this._timerIsRunning&&this._retriesRemaining>0&&r&&(this.decrementRetryCounter(),this.startTimer()))},t.switchBackToMainHost=function(){this._selectedApiHost=this.getEventsDestination()},t.batchFlush=function(){var e=this;if(!Hr(this._batch)){this.getBatchFromLocalStorage(!1);var t,n,r=this._selectedApiHost,i=r.tokenProvider,o=r.host,s=r.hostEndpoint,a=i.getToken(!0);if(a?t=a:n=d((function(){return f(this,(function(e){switch(e.label){case 0:return[4,i.refreshToken()];case 1:return[2,e.sent()]}}))})),!t&&""!==t)return ct((function(){return"".concat(e.apiName,": no auth token. Aborting events API call")})),void(null==n||n().then((function(t){t&&e.batchFlushInner({token:t,host:o,hostEndpoint:s})})));this.batchFlushInner({token:t,host:o,hostEndpoint:s})}},t.batchFlushInner=function(e){var t=this,n=this.getBatchToSend();st(n,(function(n){t.sendLogBatch(n,e)}))},t.logBatch=function(){var e=this;return d((function(){var t,n,r,i,o,s,a,u,c;return f(this,(function(l){switch(l.label){case 0:return e._timerIsRunning=!1,Hr(e._batch)?[2]:((t=e._retriesRemaining<3)&&!e._selectedApiHost.isRetry&&e.substrateHostFallback&&(e._selectedApiHost=e.getEventsDestination(!0)),t&&e.logInfo("Retrying to send events batch to ".concat(e.apiName,". Retry #").concat(3-e._retriesRemaining,".")),n=e._selectedApiHost,r=n.tokenProvider,i=n.host,o=n.hostEndpoint,s=void 0,(a=r.getToken())?(s=a,[3,3]):[3,1]);case 1:return[4,r.refreshToken()];case 2:s=l.sent(),l.label=3;case 3:return s||""===s?(u=e.getBatchToSend(),c=s,st(u,(function(t){e.sendLogBatchAsync(t,{token:c,host:i,hostEndpoint:o})})),[2]):(e.logError("no auth token for host ".concat(i,", endpoint: ").concat(o,". Aborting events API call")),e.substrateHostFallback&&!e._timerIsRunning&&e._retriesRemaining>0&&(e.decrementRetryCounter(),e.startTimer()),[2])}}))}))()},t.addEvent=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:ft(),i=this.formatAPIEvent(n);this.isV2Schema?this.addEventV2(e,t,i):Ti(this._batch,this.isV2Schema)||(!this._batch[e]&&(this._batch[e]={}),this._batch[e][t]||(this._batch[e][t]={events:{}}),this._batch[e][t].events[r]=i,this._batchProperties&&(this._batch[e][t].properties=this._batchProperties))},t.formatAPIEvent=function(e){return{Name:e.Name,Attributes:Ar(_i(e.Attributes,(function(e,t){return!e&&0!==e||!t})),(function(e,t){return{Key:t,Value:null==e?void 0:e.toString()}}))}},t.addEventV2=function(e,t,n){if(Ti(this._batch,this.isV2Schema)){this._batch[e]||(this._batch[e]=[]);var r=Si(this._batch[e],(function(e){return e.numEvents<20}));if(r){var i=Si(r.batch,(function(e){return e.Key===t}));i||(i={Key:t,Value:[]},r.batch.push(i)),i.Value.push(n),r.numEvents++,r.numRetries=0}else r={batch:[{Key:t,Value:[n]}],numEvents:1,numRetries:0},this._batch[e].push(r)}},t.resetRetryCounter=function(){this._retriesRemaining=3},t.decrementRetryCounter=function(){this._retriesRemaining--},t.startTimer=function(){var e=this;this._timerIsRunning=!0,this._logBatchAfterTimeout=setTimeout((function(){return e.logBatch()}),this._batchTimeout)},t.getBatchFromLocalStorage=function(){var e=this,t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(this._localStorageAvailable){var n=localStorage.getItem(this._localStorageKey);if(n&&n.length>0){var r=JSON.parse(n);r.LocalStorageVersion===this.localStorageVersion&&st(r.Batch,(function(t,n){e.isV2Schema,e.mergeBack(n,t,!1)}))}var i="";t||(i=JSON.stringify(this.getBatchToSave())),localStorage.setItem(this._localStorageKey,i)}},t.getBatchToSave=function(){var e=this;return Object.keys(this._batch).forEach((function(t){if(Ti(e._batch,e.isV2Schema)){var n=e._batch[t].length;if(n>5)for(var r=n-5,i=0;i<r;i++)e._batch[t].pop()}else{var o=Object.keys(e._batch[t]);if(o.length>40)for(var s=o.length-40,a=0;a<s;a++)delete e._batch[t][o[a]]}})),{LocalStorageVersion:this.localStorageVersion,Batch:this._batch}},t.getBatchToSend=function(){var e=this,t=this._batch;return this._batch={},Ar(t,(function(t,n){return e.createBatchToSend(t,n)}))},t.getEventsDestination=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=e&&this.substrateHostFallback?this.substrateHostFallback:this.substrateHost,n=t.hostName,r=n?n.toLocaleLowerCase().startsWith("http")?n:"https://".concat(n):"https://substrate.office.com";return{host:r,hostEndpoint:"".concat(r).concat(this._ApiEndpoint),isRetry:e,tokenProvider:new wi(t.getAuthToken,this.logError)}},e}()),Ci=function(){function e(t,n,r,i,o,s,a,u){(0,v._)(this,e),this._scenarioName=t,this._eventMapper=n,this._getAuthTokenAsync=r,this._xAnchorMailbox=i,this._batchTimeout=o,this.locale=s,this.substrateHostName=a,this.useNoBearerAuth=u,this._logManager=new Ii(this._scenarioName,{hostName:a,getAuthToken:this._getAuthTokenAsync},void 0,this._batchTimeout,this._xAnchorMailbox,s,u)}var t=e.prototype;return t.canConsumeEvent=function(e){return!0},t.consume=function(e,t){var n=this._eventMapper(e);if(n){var r=this.getKey(n);this._logManager&&r&&this._logManager.logEvent(n,r,t)}},t.flush=function(){return this._logManager&&this._logManager.flush(),Promise.resolve()},t.destroy=function(){this.flush(),delete this._logManager},t.getLogManager=function(){return this._logManager},t.getKey=function(e){switch(e.Name){case"CachedContentRendered":var t;return null==e||null===(t=e.Attributes.NewLogicalId)||void 0===t?void 0:t.toString();case"SearchEntityActions":case"SearchActions":case"ClientDataSource":case"ClientLayout":case"ResultsRendered":case"SearchFeedback":case"CounterFactualInformation":var n;return null==e||null===(n=e.Attributes.LogicalId)||void 0===n?void 0:n.toString();case"ResponseReceived":var r;return null==e||null===(r=e.Attributes.TraceId)||void 0===r?void 0:r.toString();default:e.Name}},e}(),Ei=[lt,"SubstrateWarmup_RoundTripTime"];function Ai(e){if(function(e){switch(e){case"MicrosoftSearchClickEvent":case"MicrosoftSearchClickEntityEvent":case"MicrosoftSearchClientDataSourceEvent":case"MicrosoftSearchClientLayoutEvent":case"MicrosoftSearchRoundTripTimeEvent":case"MicrosoftSearchResultsRenderedEvent":case"MicrosoftSearchCachedContentRenderedEvent":case"MicrosoftSearchFeedbackEvent":case"MicrosoftSearchCounterFactualInformationEvent":return!0;case"MicrosoftSearchClientDataSourceAriaEvent":case"MicrosoftSearchClickAriaEvent":case"MicrosoftSearchDiagnosticsEvent":case"MicrosoftSearchPerformanceEvent":case"MicrosoftSearchValidateEvent":case"MicrosoftSearchRoundTripTimeBundleEvent":case"MicrosoftSearchQueryEvent":case"MicrosoftSearchRankingEvent":case"MicrosoftSearchImpressionEvent":case"MicrosoftSearchAnswerResultsRenderedEvent":case"MicrosoftSearchSignInEvent":case"MicrosoftSearchSignOutEvent":case"MicrosoftSearchValueEvent":case"MicrosoftSearchQfValueEvent":case"MicrosoftSearchQfRankEvent":case"MicrosoftSearchResourcePerformanceEvent":case"MicrosoftSearchPerformanceTimingsEvent":case"MicrosoftSearchVariantEvent":case"MicrosoftSearchQfVariantEvent":case"MicrosoftSearchPrivacyEvent":case"MicrosoftSearchLatencyEvent":case"MicrosoftSearchMissingInstEvent":return!1;default:return g(0,!1)}}(e.featureName)){var t=e.properties;return function(e,t,n,r){if(e)return{Name:e,Attributes:t}}("ResponseReceived"===e.source?-1===Ei.indexOf(e.eventId)?e.source:void 0:e.source,t)}}var Pi=s(375),ki=s(386),Ri=s(919),Oi=function(e){return e.Qos="qos_ev",e.Usage="usage",e.Perf="perf",e}({});function Bi(e,t){var n,r,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return!("NotSet"!==e||(n="fd103984-5798-4920-987c-c6fe11cbbad1",r=(0,Pi.killSwitchesSetting)(),(0,Pi.isKillSwitchActivatedSetting)()(n)||n in r&&r[n])||!(0,Ri.allowEUPIScrubbingIfNoTelemetryBoundarySet)()||!function(e){return(void 0===e?"undefined":(0,m._)(e))&&-1!==ki.Hz.indexOf(e)}(t)||!function(e){return"officehomemsa"!==e}(i))&&"EUDB"}function Mi(e){var t=(null==e?void 0:e.props.serviceEnvInfo)||{environment:"prod",urls:void 0},n=t.environment,r=t.urls;return function(e,t){var n="https://substrate.office.com";if(null==t?void 0:t.Substrate)return t.Substrate;switch(e){case"GccHigh":return"https://substrate.office365.us";case"DoD":return"https://substrate-dod.office365.us";case"Gallatin":return"https://partner.outlook.cn";case"Df":case"Msit":case"Prod":case"GccModerate":case"Unknown":return n;default:return g(0,n)}}(function(e){switch(e){case"o365test":case"onebox":case"edog":case"spdf":case"test":case"df":return"Df";case"prodbubble":return"Msit";case"":case"prod":return"Prod";case"blackforest":return"Blackforest";case"gallatin":return"Gallatin";case"pathfinder":return"DoD";case"trailblazer":return"GccHigh";case"gcc":return"GccModerate";case"ag08":return"AG08";case"ag09":return"AG09";default:return g(0,"Prod")}}(n),r)}function xi(e){var t=(e.getBufferedLogEvents&&e.getBufferedLogEvents()||e.logEventBuffer||[]).slice(),n=null;function r(){return i.apply(this,arguments)}function i(){return(i=d((function(){var t;return f(this,(function(n){switch(n.label){case 0:return t={},[4,e.tokenProvider({attempt:0}).then((function(e){return e}))];case 1:return t.token=n.sent(),[2,t]}}))}))).apply(this,arguments)}var o,s=e.instrumenter,a=(o=e.logCtx.hostAppInfo,new Ci(o.id,Ai,r,void 0,void 0,void 0,Mi(s)));function u(e){switch(e){case"ResponseReceived":return"MicrosoftSearchRoundTripTimeEvent";case"ClientLayout":return"MicrosoftSearchClientLayoutEvent";case"ResultsRendered":return"MicrosoftSearchResultsRenderedEvent";case"ClientDataSource":return"MicrosoftSearchClientDataSourceEvent";case"SearchFeedback":return"MicrosoftSearchFeedbackEvent";case"SearchActions":return"MicrosoftSearchClickEvent";case"SearchEntityActions":return"MicrosoftSearchClickEntityEvent";case"CachedContentRendered":return"MicrosoftSearchCachedContentRenderedEvent";case"CounterFactualInformation":return"MicrosoftSearchCounterFactualInformationEvent";default:return e}}function l(t){var n=self["fsc-predicate"];n&&"function"==typeof n&&!n("3s-event",t)||a&&function(e){return("QOSSTOP"==e.eventType||"USAGE"==e.eventType)&&void 0!==(t=e.name)&&ki.Dq.indexOf(t)>-1;var t}(t)&&a.consume(function(t){var n=t;return"ClientDataSource"==n.name&&(n=function(t){var n=e.logCtx,r=n.userInfo,i=n.tenantInfo;return{name:"ClientDataSource",eventType:"USAGE",nameDetail:t.nameDetail,properties:h(c({},t.properties),{UserId:r.id,TenantId:i.omsId})}}(n)),h(c({},n),{featureName:u(n.name),eventId:n.nameDetail,source:n.name})}(t))}return s&&p(s,["hostAppInfo"],(function(){a&&(a.destroy(),a=new Ci(s.props.hostAppInfo.id,Ai,r,void 0,void 0,void 0,Mi(s)))})),{enable:function(){t.forEach(l),t=[],null===n&&(n=e.dispatcher.register(l))},disable:function(){n&&(e.dispatcher.unregister(n),n=null)}}}var Di=s(158),Ni=s(663),Wi="0000000000000000",ji=function(e){var t=e;if(void 0!==e.properties){var n=c({},e);n.properties=c({},e.properties,n.properties.LogicalId&&{LogicalId:""},n.properties.ConversationId&&{ConversationId:""},n.properties.TraceId&&{TraceId:""},n.properties.impressionId&&{impressionId:""},n.properties.conversationId&&{conversationId:""},n.properties.NewLogicalId&&{NewLogicalId:""},n.properties.properties&&{properties:""}),t=c({},n)}return t},Li=function e(t){var n=this;(0,v._)(this,e),this.processEvent=function(e){var t=e;return n.middleWares.forEach((function(e){t=e(t)})),t},this.middleWares=t},Ui=s(717);function Fi(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return(Math.pow(10,t)+Math.abs(e)).toString().substring(1,t+1)}function qi(e,t,n){var r=n||{};return t&&(r.EntityClickDetails=t),c({logEventName:"SearchEntityActions"},Gi("EntityClicked",e,r))}function Hi(e,t){return c({logEventName:"SearchEntityActions"},Gi("RelatedEntityClicked",e,t&&{RelatedEntityType:t}))}function Vi(e,t,n){var r={};return t&&(r.EntityRefinerType=t),c({logEventName:"SearchEntityActions"},Gi("EntityRefinerClicked",e,c({},n,r)))}function zi(e,t,n){var r={};return t&&(r.EntityActionTakenType=t),c({logEventName:"SearchEntityActions"},Gi("EntityActionTaken",e,c({},n,r)))}function Qi(e,t){return c({logEventName:"SearchEntityActions"},Gi(t,e))}function Ki(e,t,n){return c({logEventName:"SearchActions"},Gi(e,t,n))}function Gi(e,t,n){return{properties:{Id:t.properties.EntityId||"",nameDetail:t.nameDetail||"",LogicalId:t.properties.LogicalId||"",ConversationId:t.properties.ConversationId||"",EventType:e,Version:Yt.current,TraceId:t.properties.TraceId||"",metadata:n&&JSON.stringify(n)||"",LocalTime:(r=new Date,function(e,t){var n=-e.getTimezoneOffset(),r=n>=0?"+":"-";return e.getFullYear()+"-"+Fi(e.getMonth()+1)+"-"+Fi(e.getDate())+"T"+Fi(e.getHours())+":"+Fi(e.getMinutes())+":"+Fi(e.getSeconds())+"."+Fi(e.getMilliseconds(),3)+r+Fi(n/60)+":"+Fi(n%60)}(r))}};var r}function Ji(e){var t=e&&e.itemKey||"";return c(t.indexOf("_")>0?{VerticalType:"Custom",VerticalId:t}:{VerticalType:t},{Position:null==e?void 0:e.position,FromOverflow:null==e?void 0:e.fromOverflow,TotalVerticalCount:null==e?void 0:e.totalCount,VisibleVerticalCount:null==e?void 0:e.visibleCount,ConnectionId:null==e?void 0:e.connectionIds})}function Xi(e,t){var n,r,i=function(e,t){return("hostAppInfo"in t?t.hostAppInfo:e)||e}(e,t),o=i.id,s=i.userRing,a=i.version,u=i.scopeId,c=i.variant;return[["clientId",o],["HostAppInfo.ScopeId",u],["environment",(null===(n=t.serviceEnvInfo)||void 0===n?void 0:n.environment)||(r=s,r&&Zi[r]||r)],["HostAppInfo.Version",a],["HostAppInfo.Variant",c]]}(new Date).valueOf();var Zi={test:"test",inner:"inner",outer:"prodbubble",insiders:"insiders",generalAudience:"prod"},$i=function(e){return e[e.Unspecified=0]="Unspecified",e[e.String=1]="String",e[e.Int64=2]="Int64",e[e.Double=3]="Double",e[e.Boolean=4]="Boolean",e[e.Date=5]="Date",e}({}),Yi=[];function eo(){var e=(0,nn.getSettingsForLogging)(),t=Object.keys(e).map((function(t){return{name:t,value:e[t]}})).filter((function(e){return!!e.value})).reduce((function(e,t){var n,r=t.name;return e.concat((n=r.replace("searchux.",""))?n.replace(new RegExp("\\.","g"),"_"):"")}),[]);return t.length||Yi.length?{flights:t.concat(Yi).join(";")}:{}}var to=function(){function e(t){var n,r,i,o=this;(0,v._)(this,e),this.props=t,this.listenToken=null,this.initialized=!1,this.propertyMap={},this.middlewares=Bi((0,Ri.default)(),null===(n=this.props.instrumenter)||void 0===n?void 0:n.props.serviceEnvInfo.environment,null===(i=this.props.instrumenter)||void 0===i||null===(r=i.props.hostAppInfo)||void 0===r?void 0:r.id)?new Li([ji]):new Li([]),this.memoryPropertyStorageOverride={setProperty:function(e,t){o.propertyMap[e]=t},getProperty:function(e){return o.propertyMap[e]||""}}}var t=e.prototype;return t.enable=function(){var e=this;this.initAria(),(this.props.getBufferedLogEvents&&this.props.getBufferedLogEvents()||this.props.logEventBuffer||[]).slice().forEach((function(t){return e.logEvent(t)})),null===this.listenToken&&(this.listenToken=this.props.dispatcher.register((function(t){return e.logEvent(t)})))},t.disable=function(){this.listenToken&&(this.props.dispatcher.unregister(this.listenToken),this.listenToken=null)},t.logEvent=function(e){switch((e=this.middlewares.processEvent(e)).eventType){case"QOSSTOP":this.logQosEvent(e);break;case"USAGE":"engagement"===e.name&&this.logSearchActivity(e),this.logUsageEvent(e);break;case"ERROR":this.logErrorEvent(e);break;case"PERF":this.logPerfEvent(e)}},t.logErrorEvent=function(e){var t,n=e.errorType||"generic_error";t="resultsrendered_error"===n?{name:n,priority:Di.AWTEventPriority.Normal,properties:c({nameDetail:e.name},e.properties,eo(),io(e))}:{name:"generic_error",priority:Di.AWTEventPriority.Normal,properties:c({nameDetail:e.nameDetail},eo(),io(e))},this.logManager.logFailure(e.name||"unknown",e.detail||"unknown","error",null,t)},t.logQosEvent=function(e){"FAILURE"===e.result?this.logFailure(e):this.logSuccess(e)},t.logUsageEvent=function(e){this.logManager.logEvent({name:e.name,type:Oi.Usage,priority:Di.AWTEventPriority.Normal,properties:c({"EventInfo.RelativeTime":Math.round((0,Ui.tB)()),nameDetail:e.nameDetail},e.properties,eo(),io(e))})},t.logSearchActivity=function(e){var t=this,n=function(e){var t=function(e){var t,n=e.properties&&e.properties.options,r=function(e){var t=e.properties&&e.properties.impressionId,n=e.properties&&e.properties.traceId,r=e.properties&&e.properties.entityId,i=e.properties&&e.properties.conversationId;return{nameDetail:e.nameDetail,properties:{LogicalId:t,TraceId:n,EntityId:r,ConversationId:i}}}(e),i=(e.nameDetail||"").split("."),o=3===i.length?i.slice(1,3).join("."):e.nameDetail;switch(o){case"TriageSiteSearchResult.onCollapse":case"TriageFileSearchResult.onCollapse":case"TriageNewsSearchResult.onCollapse":case"TriageVideoSearchResult.onCollapse":return Qi(r,"PreviewClose");case"TriageSiteSearchResult.onExpand":case"TriageFileSearchResult.onExpand":case"TriageNewsSearchResult.onExpand":case"TriageVideoSearchResult.onExpand":case"OneUpCookieFilePreviewWrapper.onInlinePreviewInvoked":case"OneUpTokenFilePreviewWrapper.onInlinePreviewInvoked":return Qi(r,"PreviewOpen");case"TriageFileSearchResult.onPersonClickTargetWrapperTrigger":case"TriageNewsSearchResult.onPersonClickTargetWrapperTrigger":case"TriageNewsSearchResult.onPublishedByClick":case"TriageFileSearchResult.onModifiedByClick":case"TriageFileSearchResult.onModifiedByContextMenu":case"VideoSearchResult.onPersonClickTargetWrapperTrigger":case"VideoSearchResult.onModifiedByClick":case"VideoSearchResult.onModifiedByContextMenu":case"TileItem.onModifiedByClick":case"TileItem.onModifiedByContextMenu":case"TileItem.onPersonClickTargetWrapperTrigger":return Hi(r,"Author");case"TriageSiteSearchResult.onLocationClick":case"TriageFileSearchResult.onLocationClick":case"TriageNewsSearchResult.onLocationClick":case"VideoSearchResult.onLocationClick":case"TriageFileSearchResult.onLocationContextMenu":case"TriageNewsSearchResult.onLocationContextMenu":case"VideoSearchResult.onLocationContextMenu":case"TileItem.onItemActionOpenFileLocationClick":case"TriageFileSearchResult.onItemActionOpenFileLocationClick":return Hi(r,"EntityLocationClick");case"TriageSiteSearchResultHeroModule.onActivityContextMenu":case"TriageSiteSearchResultHeroModule.onActivityClick":return Hi(r,"DeepLink");case"TriageFileSearchResult.onBreadcrumbItemClick":case"TriageVideoSearchResult.onBreadcrumbItemClick":case"TriageFileSearchResult.onBreadcrumbItemContextMenu":case"TileItem.onBreadcrumbItemClick":return Hi(r,"BreadcrumbItemClick");case"TriageSiteSearchResult.onLocationContextMenu":return Hi(r);case"PeopleFlashinHeroResult.onPersonPropertyValueClick":case"PeopleFlashinHeroResult.onPersonPropertyValueContextMenu":return Vi(r);case"TriageSiteSearchResult.onScopeToSite":case"TriageSiteSearchResultHeroModule.onScopeToSite":case"TriageSiteSearchResult.onItemActionScopeToSiteClick":return Vi(r,"SiteSearch");case"PeopleFlashinHeroResult.onCopyEmailClick":case"ProfileSearchResult.onCopyEmailClick":case"ContactVertical.onEmailCopy":case"EmbeddedLPC.onEmailCopy":return zi(r,"CopyEmailId");case"PeopleFlashinHeroResult.onCopyWorkPhone":case"ProfileSearchResult.onCopyWorkPhone":case"ContactVertical.onMobilePhoneCopy":case"ContactVertical.onWorkPhoneCopy":case"EmbeddedLPC.onPhoneCopy":return zi(r,"CopyPhone");case"PeopleFlashinHeroResult.onEmailClick":case"ProfileSearchResult.onEmailClick":case"PeopleFlashinHeroResult.onEmailContextMenu":case"ProfileSearchResult.onEmailContextMenu":case"EmbeddedLPC.onEmailHeaderClick":case"EmbeddedLPC.onEmailClick":case"ContactVertical.onEmailClick":return zi(r,"EmailClick");case"PeopleFlashinHeroResult.onWorkPhoneClick":case"ProfileSearchResult.onWorkPhoneClick":case"PeopleFlashinHeroResult.onWorkPhoneContextMenu":case"ProfileSearchResult.onWorkPhoneContextMenu":case"ContactVertical.onMobilePhoneClick":case"ContactVertical.onWorkPhoneClick":case"EmbeddedLPC.onMobilePhoneClick":case"EmbeddedLPC.onWorkPhoneClick":return zi(r,"WorkPhoneClick");case"ContactVertical.onChatClick":case"EmbeddedLPC.onChatHeaderClick":return zi(r,"ChatClick");case"OrganizationChart.onPersonInFocusClicked":var s;return Vi(r,"PeopleCentricSearch",(null==e||null===(s=e.properties)||void 0===s?void 0:s.metadata)||{});case"ContactVertical.onChatCopy":return zi(r,"CopyChatId");case"ContactVertical.onOtherContactDataCopy":case"EmbeddedLPC.onOtherContactDataCopy":return zi(r,"CopyContactData",e.properties.metadata);case"EmbeddedLPC.onContactPanelHeaderClick":case"EmbeddedLPC.onContactPanelFooterClick":case"EmbeddedLPC.onOrganizationPanelHeaderClick":case"EmbeddedLPC.onOrganizationPanelFooterClick":var a;return qi(r,"NavigationalClick",(null==e||null===(a=e.properties)||void 0===a?void 0:a.metadata)||{});case"EmbeddedLPC.onOrganizationManagerClick":case"EmbeddedLPC.onOrganizationDirectClick":var u;return Vi(r,"PeopleCentricSearch",(null==e||null===(u=e.properties)||void 0===u?void 0:u.metadata)||{});case"TriageFileSearchResult.onItemActionMenuOpen":case"TriageSiteSearchResult.onItemActionMenuOpen":case"TriageNewsSearchResult.onItemActionMenuOpen":case"TileItem.onItemActionMenuOpen":return Qi(r,"OptionsMenuOpen");case"TriageFileSearchResult.onItemActionMenuDismissed":case"TriageSiteSearchResult.onItemActionMenuDismissed":case"TriageNewsSearchResult.onItemActionMenuDismissed":case"TileItem.onItemActionMenuDismissed":return function(e){return Qi(e,"OptionsMenuClose")}(r);case"TileItem.onItemActionDownloadClick":case"TriageFileSearchResult.onItemActionDownloadClick":return zi(r,"Download");case"TileItem.onItemActionCopyLinkClick":case"TriageFileSearchResult.onItemActionCopyLinkClick":case"TriageNewsSearchResult.onItemActionCopyLinkClick":return zi(r,"CopyLink");case"TileItem.onItemActionShareClick":case"TriageFileSearchResult.onItemActionShareClick":case"TriageNewsSearchResult.onItemActionShareClick":return zi(r,"Share");case"TriageFileSearchResult.onItemActionOpenInAppClick":return qi(r,"OpenInClient");case"TriageFileSearchResult.onItemActionOpenInBrowserClick":return qi(r,"NewTabOpen");case"TriageNewsSearchResult.onContinueReadingClick":return qi(r,"ContinueReading");case"TriageFileSearchResult.onContextMenu":case"TriageBestBetSearchResult.onContextMenu":case"TriageBookmarkSearchResult.onContextMenu":case"VideoSearchResult.onContextMenu":case"TileItem.onContextMenu":case"TriageFileSearchResult.onOpenNewTabContextMenu":case"TriageNewsSearchResult.onContinueReadingContextMenu":case"TriageNewsSearchResult.onTitleContextMenu":case"TriageSiteSearchResult.onTitleContextMenu":case"TriageSiteSearchResultHeroModule.onOpenNewTabContextMenu":case"TriageFileSearchResult.onPreviewLinkContextMenu":case"TriageNewsSearchResult.onPreviewLinkContextMenu":case"TriageSiteSearchResult.onPreviewLinkContextMenu":case"VideoSearchResult.onPreviewContextMenu":return qi(r,"RightClickOpen");case"RelatedPerson.onClick":case"RelatedSearch.onClick":case"DidYouMean.onClick":return qi(r);case"GroupedSuggestionsWithLayouts.onImpressionItemClicked":case"GroupedSearchSuggestions.onImpressionItemClicked":return e.properties.commandId?zi(r,e.properties.commandId,{FilterTypes:(t=null==e?void 0:e.properties).FilterTypes,EventId:t.eventId,EntityClickDetails:t.entityClickDetails,ClickedRefinerValue:t.queryValue}):qi(r,"NewTabOpen");case"TriageFileSearchResult.onPreviewLinkClick":case"TriageNewsSearchResult.onPreviewLinkClick":case"TriageSiteSearchResult.onPreviewLinkClick":case"VideoSearchResult.onPreviewClick":case"TileItem.onClick":case"ConnectorClusterResult.onAnchorClicked":case"ConnectorClusterResult.onExecuteAction":case"ConnectorResult.onAnchorClicked":case"ConnectorResult.onExecuteAction":case"TriageFileSearchResult.onItemActionOpenFolderClick":case"ConversationSearchPage.onConversationItemClick":case"PeopleFlashinHeroResult.onClick":case"TriageSiteSearchResult.onTitleClick":case"TriageNewsSearchResult.onTitleClick":case"PeopleFlashinHeroResult.onPersonClickTargetWrapperTrigger":case"ProfileSearchResult.onPersonClickTargetWrapperTrigger":case"ProfileSearchResult.onClick":case"TriageFileSearchResult.onClick":case"FlashinPeopleCard.onClick":case"TriageBestBetSearchResult.onClick":case"TriageBookmarkSearchResult.onClick":case"TriageSiteSearchResultHeroModule.onOpenNewTabClick":case"TriageFileSearchResult.onOpenNewTabClick":case"TriagePageSearchResult.onClick":case"TriageSiteSearchResult.onClick":case"TriageVideoSearchResult.onOpenNewTabClick":case"TriageVideoSearchResult.onClick":case"VideoSearchResult.onClick":case"TemplateSearchResult.onClick":case"AppSearchResult.onClick":case"CustomSearchResultBase.onAnchorClicked":case"CustomSearchResultBase.onExecuteAction":return qi(r,"NewTabOpen",function(e){return{ConnectionId:null==e?void 0:e.connectionId,LayoutId:null==e?void 0:e.layoutId,VerticalId:null==e?void 0:e.verticalId}}(null==e?void 0:e.properties));case"PaginationWrapper.paginateAndLog":case"Pagination.onContextMenu":return Ki("PaginationClicked",r,{pageId:n&&n.pageNumber});case"VerticalFilters.changeAndLog":case"VerticalFilters.overflowOptionClick":return Ki("VerticalClicked",r,Ji(n));case"VerticalFilters.clickInPCS":return Ki("PeopleScopeVerticalClicked",r,Ji(n));case"CustomRefinersContainer.applyRefiner":return Ki("FilterModified",r,n);case"ClearRefinersButton.clearRefinersAndLog":case"SelectedRefinerItem.removeRefinerItem":return Ki("ClearFilters",r,n);case"CustomRefinersContainer.onRefinerClick":return Ki("FilterPaneClicked",r,n);case"VerticalFilters.toggleMoreOptions":return Ki("ExpandHiddenItems",r,{ExpansionType:"Vertical"});case"PeopleFlashinHost.onSeeMorePeople":return Ki("ExpandLinkClicked",r,{ExpansionType:"People"});case"CustomVerticalResultCluster.onHeaderClick":case"CustomVerticalResultCluster.onFooterClick":return Ki("ExpandLinkClicked",r,{ExpansionType:"Vertical"});case"SearchWithoutRefinersLink.onClearRefinersClick":return Ki("ClearFilters",r,n&&{Source:n.source});case"UpScopeToAllVertical.onUpScopeSearchClick":return Ki("UpScopeSearch",r,n&&{Source:n.source});case"RefinerButton.onClick":case"RefinerHideButton.onClick":return Ki("FilterPaneClicked",r);case"RefinerChoiceGroup.onChangeAndLog":return Ki("FilterModified",r,{FilterType:n&&n.filterType});case"SearchBoxExitButton.onClick":case"RoundedSearchBoxExitButton.onClick":return Ki("ExitSearch",r);case"SearchBoxMagnifierSubmitButton.onClick":case"SearchBoxHideableMagnifierSubmitButton.onClick":case"RoundedSearchBoxHideableMagnifierSubmitButton.onClick":case"SubmitSearchSuggestion.onClick":case"SearchBoxSubmitButton.onClick":case"SearchBoxMagnifierButton.onMouseDown":case"ScopingControl.onSearchDone":return Ki("SearchDone",r,{SearchTriggerOrigin:"SubmitSearchSuggestion.onClick"===o?"QfPaneLinkAtBottom":"EnterKey"});case"PeoplePill.onRemove":return Ki("PeoplePillRemoved",r);case"ScopingControl.onClick":case"RoundedScopingControl.onClick":return Ki("ScopePaneClicked",r);case"ScopingControl.onDropdownItemClick":case"RoundedScopingControl.onDropdownItemClick":var l;return Ki("ScopeModified",r,(null==e||null===(l=e.properties)||void 0===l?void 0:l.metadata)||{});default:return e.properties&&e.properties.shouldLogToSubstrate?c({logEventName:"MissingSearchActions"},Gi("Unknown",r)):void 0}}(e);if(t)return{name:t.logEventName,eventType:"USAGE",properties:c({},t.properties)}}(e);n&&setTimeout((function(){return t.props.dispatcher.dispatch(n)}))},t.logPerfEvent=function(e){this.logManager.logEvent({name:e.metricType,type:Oi.Perf,priority:Di.AWTEventPriority.Normal,properties:c({},e.metrics,eo(),io(e))})},t.initAria=function(){var e,t,n=this;if(!this.initialized){var r,i,o=this.props.logCtx,s=o.appInfo,a=void 0===s?{}:s,u=o.userInfo,l=o.hostAppInfo,d=o.tenantInfo,h=l&&l.userRing&&"test"===l.userRing||"7d0a4169-73cb-43f5-a096-1e050bf94dd0"===(null==(r=d.omsId)?void 0:r.toLowerCase()),f=this.props.instrumenter;this.initialized=!0,f&&!f.props.correlationId&&f.setProps({correlationId:ft()}),u.flights&&(i=u.flights,Yi=i),a.id&&Ni.default.getSemanticContext().setAppId(a.id),Ni.default.getSemanticContext().setAppVersion("1.20250331.2.0"),no(function(e,t){var n=e.appInfo,r=void 0===n?{}:n,i=e.hostAppInfo,o=e.hostComponentInfo,s=e.tenantInfo,a=e.spInfo,u=e.searchTextCharacteristics;return[["SearchUXCorrelationId",t.correlationId],["AppInfo.Component",r.component]].concat(Fr(Xi(i,t)),Fr([["DeviceInfo.Id"," "]]),Fr(function(e){return e?[["HostComponentInfo.Id",e.id],["HostComponentInfo.Version",e.version],["HostComponentInfo.UserRing",e.userRing]]:[]}(o)),[["TenantInfo.OmsId",s.omsId]],Fr(function(e){return e?[["siteSubscriptionId",e.siteSubscriptionId],["SpInfo.Environment",e.environment],["farmLabel",e.farmLabel],["SpInfo.WebId",e.webId],["SpInfo.SiteId",e.siteId]]:[]}(a)),[["QueryInfo.QueryLength",u&&u.characterCount.toString()]])}(this.props.logCtx,f?f.props:{})),f&&(p(f,["hostAppInfo"],(function(){no(Xi(n.props.logCtx.hostAppInfo,f.props))})),p(f,["searchTextCharacteristics"],(function(){var e;no([["QueryInfo.QueryLength",(e=f.props.searchTextCharacteristics)&&e.characterCount.toString()]])})),p(f,["spInfo"],(function(e){no(function(e){return[["SpInfo.WebId",e.webId],["SpInfo.SiteId",e.siteId]]}(e.spInfo))})));var v=null==f||null===(e=f.props.serviceEnvInfo)||void 0===e?void 0:e.environment,y=null==f||null===(t=f.props.hostAppInfo)||void 0===t?void 0:t.id,_=function(e){if(arguments.length>1&&void 0!==arguments[1]&&arguments[1])return"547a07f2fb17404ca8072723e610e9c6-d15f0ac5-7367-4222-856c-0da02af0cf8f-7217";var t="21d9f63f7fbc4707beb42a3e1e4917be-40627a7f-071b-46d1-9227-3715668b8b52-7162";switch(e){case"blackforest":return"21d9f63f7fbc4707beb42a3e1e4917be-d09c639e-1cb2-4912-860d-ecb147c5b94a-7059";case"trailblazer":return"21d9f63f7fbc4707beb42a3e1e4917be-7acf387d-2e06-4967-b288-4d2e285fd922-7501";case"pathfinder":return"21d9f63f7fbc4707beb42a3e1e4917be-dc0279f8-eb6b-4e78-b2cd-fd75da84b23c-7532";case"ag08":case"ag09":return"21d9f63f7fbc4707beb42a3e1e4917be-7094fd92-69d3-4cde-a92b-a858219ee835-7027";case"edog":case"gallatin":case"gcc":case"o365test":case"spdf":case"df":case"test":case"onebox":case"prodbubble":case"prod":case"":case void 0:return t;default:return g(0,t)}}(v,h),m=function(e,t,n){var r,i="https://browser.pipe.aria.microsoft.com/Collector/3.0/";if(null==e||null===(r=e.urls)||void 0===r?void 0:r.Aria)return e.urls.Aria;switch(null==e?void 0:e.environment){case"trailblazer":return"https://tb.pipe.aria.microsoft.com/Collector/3.0";case"pathfinder":return"https://pf.pipe.aria.microsoft.com/Collector/3.0";case"gallatin":return"https://collector.azure.cn/Collector/3.0/";case"edog":case"gcc":case"o365test":case"spdf":case"df":case"test":case"onebox":case"prodbubble":case"prod":case"":case void 0:return"EU"===t?"https://eu-mobile.events.data.microsoft.com/Collector/3.0/":i;default:return g(0,i)}}(null==f?void 0:f.props.serviceEnvInfo,(0,Ri.default)());this.logManager=Ni.default.initialize(_,c({enableAutoUserSession:!0,canSendStatEvent:function(){return!1},collectorUri:m},this.props.disableCookies&&{disableCookiesUsage:!0,propertyStorageOverride:this.memoryPropertyStorageOverride}));var S,b,T=Bi((0,Ri.default)(),v,y);T?(Ni.default.getSemanticContext().setUserId(Wi),Ni.default.getSemanticContext().setDeviceId("00000000-0000-0000-0000-000000000000"),no([["ReasonForScrubbing",T,$i.String]])):Ni.default.getSemanticContext().setUserId("string"!=typeof(S=(b=u.id)?b.lastIndexOf("|")>-1&&b.indexOf("@")>-1?b.substring(b.lastIndexOf("|")+1,b.indexOf("@")):b:null)||16!==S.length||S.search(/[^0-9A-Fa-f]/)>=0?Wi:S);var w={eventsRejected:function(e,t){var r="rejected_event";e.filter((function(e){return e.name!==r})).forEach((function(e){var i;n.logManager.logFailure(((i=((i=e.name)||"").replace(/\W/g,"")).length<4?i+="0000":i.length>100&&(i=i.substr(0,100)),i),t.toString(),"error",null,{name:r})}))}};Ni.default.addNotificationListener(w),window.addEventListener("beforeunload",ro)}},t.logSuccess=function(e){this.logManager.logEvent({name:e.name,type:Oi.Qos,priority:Di.AWTEventPriority.Normal,properties:c(h(c({nameDetail:e.nameDetail,startTime:e.startTimestamp,totalTime:e.totalTime},e.properties,eo()),{result:e.result}),io(e))})},t.logFailure=function(e){var t={name:e.name,priority:Di.AWTEventPriority.Normal,properties:c({nameDetail:e.nameDetail,startTime:e.startTimestamp,totalTime:e.totalTime},e.properties,eo(),io(e))};e.error?this.logManager.logFailure(e.error.bucketId||"unknown",e.error.detail||"unknown","error",null,t):this.logManager.logFailure("unknown","unknown","error",null,t)},e}();function no(e){e.forEach((function(e){var t,n=function(e){if(Array.isArray(e))return e}(t=e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o=[],s=!0,a=!1;try{for(n=n.call(e);!(s=(r=n.next()).done)&&(o.push(r.value),3!==o.length);s=!0);}catch(e){a=!0,i=e}finally{try{s||null==n.return||n.return()}finally{if(a)throw i}}return o}}(t)||Ur(t,3)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),r=n[0],i=n[1],o=n[2];return Ni.default.setContext(r,i,o||Di.AWTPropertyType.String)}))}function ro(){try{Ni.default.flushAndTeardown()}catch(e){}}function io(e){return{"EventInfo.DispatchTime":e.dispatchTime}}const oo=to;s(74),s(919),s(375);var so,ao=!1;function uo(e){ao||(ao=!0,new oo(e).enable())}function co(e){so||(so=xi(c({},e))).enable()}var lo=nn;return a})()).apply(t,r),void 0===i||(e.exports=i)}}]);