[{"Key": "562306f7-3f74-03c7-6363-acddda42d8fd", "Value": [{"Name": "ResponseReceived", "Attributes": [{"Key": "ConversationId", "Value": "6cb08fc7-9ccd-2e51-d3b7-3048aafa3841"}, {"Key": "Latency", "Value": "1383.5999999996275"}, {"Key": "Status", "Value": "200"}, {"Key": "Version", "Value": "2"}, {"Key": "TraceId", "Value": "562306f7-3f74-03c7-6363-acddda42d8fd"}, {"Key": "ProviderName", "Value": "SubstrateSearch"}, {"Key": "<PERSON><PERSON><PERSON>", "Value": "{\"queryLogicalId\":\"ca1c4799-b61c-1695-1ac5-f99f065bbd02\"}"}, {"Key": "LogicalId", "Value": "ca1c4799-b61c-1695-1ac5-f99f065bbd02"}, {"Key": "properties", "Value": "{\"status\":200,\"serverLatency\":\"1300\",\"serverName\":\"PN3PR01MB9358.INDPRD01.PROD.OUTLOOK.COM\",\"requestId\":\"562306f7-3f74-03c7-6363-acddda42d8fd\",\"msEdgeReference\":\"NotProvided\",\"anchorMailboxSet\":\"true\",\"xBeSku\":\"Unknown\",\"searchPlatform\":\"Substrate\",\"diagnostics\":\"{\\\"entryType\\\":\\\"resource\\\",\\\"startTime\\\":7233.5999999996275,\\\"duration\\\":1361.2000000011176,\\\"initiatorType\\\":\\\"xmlhttprequest\\\",\\\"deliveryType\\\":\\\"\\\",\\\"nextHopProtocol\\\":\\\"\\\",\\\"renderBlockingStatus\\\":\\\"non-blocking\\\",\\\"workerStart\\\":0,\\\"redirectStart\\\":0,\\\"redirectEnd\\\":0,\\\"fetchStart\\\":7233.5999999996275,\\\"domainLookupStart\\\":0,\\\"domainLookupEnd\\\":0,\\\"connectStart\\\":0,\\\"secureConnectionStart\\\":0,\\\"connectEnd\\\":0,\\\"requestStart\\\":0,\\\"responseStart\\\":0,\\\"firstInterimResponseStart\\\":0,\\\"finalResponseHeadersStart\\\":0,\\\"responseEnd\\\":8594.800000000745,\\\"transferSize\\\":0,\\\"encodedBodySize\\\":0,\\\"decodedBodySize\\\":0,\\\"responseStatus\\\":200,\\\"serverTiming\\\":[]}\",\"logicalSearchId\":\"ca1c4799-b61c-1695-1ac5-f99f065bbd02\",\"conversationId\":\"6cb08fc7-9ccd-2e51-d3b7-3048aafa3841\"}"}, {"Key": "SharedSerp", "Value": "true"}]}]}]