<testsuites id="" name="" tests="1" failures="0" skipped="1" errors="0" time="22.734111">
<testsuite name="cucumber-wrapper.spec.ts" timestamp="2025-06-05T06:22:32.155Z" hostname="chromium" tests="1" failures="0" skipped="1" time="18.112" errors="0">
<testcase name="SPO Health Check › TC0101- Login and Check the Default Data Source Order" classname="cucumber-wrapper.spec.ts" time="18.112">
<properties>
<property name="feature" value="SPO Health Check">
</property>
<property name="tags" value="@combined, @spo">
</property>
<property name="file" value="src\features\spo-health-check.feature">
</property>
</properties>
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>