<testsuites id="" name="" tests="1" failures="1" skipped="0" errors="0" time="43.270748999999995">
<testsuite name="cucumber-wrapper.spec.ts" timestamp="2025-06-05T05:52:22.046Z" hostname="chromium" tests="1" failures="1" skipped="0" time="36.893" errors="0">
<testcase name="SPO Health Check › TC0101- Login and Check the Default Data Source Order" classname="cucumber-wrapper.spec.ts" time="36.893">
<properties>
<property name="feature" value="SPO Health Check">
</property>
<property name="tags" value="@combined, @spo">
</property>
<property name="file" value="src\features\spo-health-check.feature">
</property>
</properties>
<failure message="cucumber-wrapper.spec.ts:34:11 TC0101- Login and Check the Default Data Source Order" type="FAILURE">
<![CDATA[  [chromium] › cucumber-wrapper.spec.ts:34:11 › SPO Health Check › TC0101- Login and Check the Default Data Source Order 

    Error: page.screenshot: Target page, context or browser has been closed
    Call log:
      - taking page screenshot
      - waiting for fonts to load...


       at ..\src\core\playwright-cucumber-runner.ts:270

      268 |       // Take failure screenshot
      269 |       const failureScreenshot = path.join(testInfo.outputDir, 'failure-screenshot.png');
    > 270 |       await page.screenshot({ path: failureScreenshot, fullPage: true });
          |                  ^
      271 |       await testInfo.attach('Failure Screenshot', {
      272 |         path: failureScreenshot,
      273 |         contentType: 'image/png'
        at PlaywrightCucumberRunner.executeScenario (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:270:18)
        at C:\workspace\Playwright_Automation_Framework\tests\cucumber-wrapper.spec.ts:45:9

    Error: end of central directory record signature not found

    attachment #1: Step 1 - Before: Given I navigate to URL "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/" (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-1---Before-Given-I-navigate-to-URL-https-spo-global-kpmg-com-sites-GO-oi-bus-kpmgfinddeventerprisesearch--0874230e995514f05230eb09df7e564097deb05a.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: Step 1 - After: Given I navigate to URL "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/" (4305ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-1---After-Given-I-navigate-to-URL-https-spo-global-kpmg-com-sites-GO-oi-bus-kpmgfinddeventerprisesearch-4305ms--76e0bc46f32100246452a29546397d4fd4e61208.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: Step 2 - Before: Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-7b97b6ecd8a021fd44aa7e3cc251f323f5aac40f.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: Step 2 - After: Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000 (56ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-56ms--6d4618fa6469d69ce9894daee70fcbd3beb85ca3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: Step 3 - Before: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>" (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com--a31b3e7c43cb1efea11b3548fc5c4de968169b53.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #6: Step 3 - After: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>" (72ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com-72ms--9aed25838606bab71a24c398a8193d0e5e7144e6.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #7: Step 4 - Before: And I click on "getByRole('button', { name: 'Next' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-4---Before-And-I-click-on-getByRole-button-name-Next--1cb637a407c45e252a89feaa09376d02ecff8032.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #8: Step 4 - After: And I click on "getByRole('button', { name: 'Next' })" (134ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-4---After-And-I-click-on-getByRole-button-name-Next-134ms--39ca31def62bf7693a9303f1f6530a2005473251.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #9: Step 5 - Before: Then I wait for element "getByRole('link', { name: 'OI Development', exact: true })" to be visible with timeout 60000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-5---Before-Then-I-wait-for-element-getByRole-link-name-OI-Development-exact-true-to-be-visible-with-timeout-60000-6f394438ac89195e969aa3c0716a8777148d78c7.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🎬 Starting scenario: TC0101- Login and Check the Default Data Source Order
📁 Feature: SPO Health Check
🏷️ Tags: @combined, @spo
📋 Step 1/34: Given I navigate to URL "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/"
✅ Step completed in 4305ms
📋 Step 2/34: Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000
✅ Step completed in 56ms
📋 Step 3/34: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>"
✅ Step completed in 72ms
📋 Step 4/34: And I click on "getByRole('button', { name: 'Next' })"
✅ Step completed in 134ms
📋 Step 5/34: Then I wait for element "getByRole('link', { name: 'OI Development', exact: true })" to be visible with timeout 60000

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-1---Before-Given-I-navigate-to-URL-https-spo-global-kpmg-com-sites-GO-oi-bus-kpmgfinddeventerprisesearch--0874230e995514f05230eb09df7e564097deb05a.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-1---After-Given-I-navigate-to-URL-https-spo-global-kpmg-com-sites-GO-oi-bus-kpmgfinddeventerprisesearch-4305ms--76e0bc46f32100246452a29546397d4fd4e61208.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-7b97b6ecd8a021fd44aa7e3cc251f323f5aac40f.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-56ms--6d4618fa6469d69ce9894daee70fcbd3beb85ca3.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com--a31b3e7c43cb1efea11b3548fc5c4de968169b53.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com-72ms--9aed25838606bab71a24c398a8193d0e5e7144e6.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-4---Before-And-I-click-on-getByRole-button-name-Next--1cb637a407c45e252a89feaa09376d02ecff8032.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-4---After-And-I-click-on-getByRole-button-name-Next-134ms--39ca31def62bf7693a9303f1f6530a2005473251.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-5---Before-Then-I-wait-for-element-getByRole-link-name-OI-Development-exact-true-to-be-visible-with-timeout-60000-6f394438ac89195e969aa3c0716a8777148d78c7.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\error-context.md]]
]]>
</system-out>
<system-err>
<![CDATA[❌ Scenario failed: TC0101- Login and Check the Default Data Source Order page.screenshot: Timeout 15000ms exceeded.
Call log:
[2m  - taking page screenshot[22m
[2m  - waiting for fonts to load...[22m
[2m  - fonts loaded[22m

    at PlaywrightCucumberRunner.executeScenario [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-cucumber-runner.ts:236:20[90m)[39m
    at [90mC:\workspace\Playwright_Automation_Framework\[39mtests\cucumber-wrapper.spec.ts:45:9 {
  name: [32m'TimeoutError'[39m,
  [[32mSymbol(step)[39m]: {
    stepId: [32m'pw:api@32'[39m,
    location: {
      file: [32m'C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts'[39m,
      line: [33m236[39m,
      column: [33m20[39m,
      function: [32m'PlaywrightCucumberRunner.executeScenario'[39m
    },
    category: [32m'pw:api'[39m,
    title: [32m'page.screenshot'[39m,
    apiName: [32m'page.screenshot'[39m,
    params: {
      path: [32m'C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\step-5-after.png'[39m,
      fullPage: [33mtrue[39m,
      mask: [90mundefined[39m,
      type: [32m'png'[39m
    },
    boxedStack: [90mundefined[39m,
    steps: [],
    attachmentIndices: [],
    info: TestStepInfoImpl {
      annotations: [],
      _testInfo: [36m[TestInfoImpl][39m,
      _stepId: [32m'pw:api@32'[39m
    },
    complete: [36m[Function: complete][39m,
    endWallTime: [33m1749102779136[39m,
    error: {
      message: [32m'TimeoutError: page.screenshot: Timeout 15000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m'\x1B[2m  - taking page screenshot\x1B[22m\n'[39m +
        [32m'\x1B[2m  - waiting for fonts to load...\x1B[22m\n'[39m +
        [32m'\x1B[2m  - fonts loaded\x1B[22m\n'[39m,
      stack: [32m'TimeoutError: page.screenshot: Timeout 15000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m'\x1B[2m  - taking page screenshot\x1B[22m\n'[39m +
        [32m'\x1B[2m  - waiting for fonts to load...\x1B[22m\n'[39m +
        [32m'\x1B[2m  - fonts loaded\x1B[22m\n'[39m +
        [32m'\n'[39m +
        [32m'    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:236:20)\n'[39m +
        [32m'    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9'[39m,
      cause: [90mundefined[39m
    }
  }
}
]]>
</system-err>
</testcase>
</testsuite>
</testsuites>