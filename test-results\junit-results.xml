<testsuites id="" name="" tests="1" failures="1" skipped="0" errors="0" time="48.584548999999996">
<testsuite name="cucumber-wrapper.spec.ts" timestamp="2025-06-05T05:07:27.061Z" hostname="chromium" tests="1" failures="1" skipped="0" time="38.778" errors="0">
<testcase name="SPO Health Check › TC0101- Login and Check the Default Data Source Order" classname="cucumber-wrapper.spec.ts" time="38.778">
<properties>
<property name="feature" value="SPO Health Check">
</property>
<property name="tags" value="@combined, @spo">
</property>
<property name="file" value="src\features\spo-health-check.feature">
</property>
</properties>
<failure message="cucumber-wrapper.spec.ts:34:11 TC0101- Login and Check the Default Data Source Order" type="FAILURE">
<![CDATA[  [chromium] › cucumber-wrapper.spec.ts:34:11 › SPO Health Check › TC0101- Login and Check the Default Data Source Order 

    Error: page.title: Execution context was destroyed, most likely because of a navigation

       at ..\src\core\playwright-world.ts:48

      46 |    */
      47 |   async getTitle(): Promise<string> {
    > 48 |     return await this.page.title();
         |                            ^
      49 |   }
      50 |
      51 |   /**
        at PlaywrightWorld.getTitle (C:\workspace\Playwright_Automation_Framework\src\core\playwright-world.ts:48:28)
        at Object.handler (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:190:37)
        at StepDefinitionRegistry.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:286:9)
        at PlaywrightCucumberRunner.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:196:7)
        at PlaywrightCucumberRunner.executeScenario (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:231:9)
        at C:\workspace\Playwright_Automation_Framework\tests\cucumber-wrapper.spec.ts:45:9

    attachment #1: Step 1 - Before: Given I navigate to URL "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/" (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-1---Before-Given-I-navigate-to-URL-https-spo-global-kpmg-com-sites-GO-oi-bus-kpmgfinddeventerprisesearch--0874230e995514f05230eb09df7e564097deb05a.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: Step 1 - After: Given I navigate to URL "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/" (4614ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-1---After-Given-I-navigate-to-URL-https-spo-global-kpmg-com-sites-GO-oi-bus-kpmgfinddeventerprisesearch-4614ms--76e0bc46f32100246452a29546397d4fd4e61208.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: Step 2 - Before: Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-7b97b6ecd8a021fd44aa7e3cc251f323f5aac40f.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: Step 2 - After: Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000 (76ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-76ms--6d4618fa6469d69ce9894daee70fcbd3beb85ca3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: Step 3 - Before: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>" (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com--a31b3e7c43cb1efea11b3548fc5c4de968169b53.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #6: Step 3 - After: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>" (123ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com-123ms--9aed25838606bab71a24c398a8193d0e5e7144e6.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #7: Step 4 - Before: And I click on "getByRole('button', { name: 'Next' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-4---Before-And-I-click-on-getByRole-button-name-Next--1cb637a407c45e252a89feaa09376d02ecff8032.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #8: Step 4 - After: And I click on "getByRole('button', { name: 'Next' })" (167ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-4---After-And-I-click-on-getByRole-button-name-Next-167ms--39ca31def62bf7693a9303f1f6530a2005473251.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #9: Step 5 - Before: Then I wait for element "getByRole('link', { name: 'OI Development', exact: true })" to be visible with timeout 60000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-5---Before-Then-I-wait-for-element-getByRole-link-name-OI-Development-exact-true-to-be-visible-with-timeout-60000-6f394438ac89195e969aa3c0716a8777148d78c7.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #10: Step 5 - After: Then I wait for element "getByRole('link', { name: 'OI Development', exact: true })" to be visible with timeout 60000 (11063ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-5---After-Then-I-wait-for-element-getByRole-link-name-OI-Development-exact-true-to-be-visible-with-timeout-60000-11063ms--89c6bbe0aca926942991f53ad8cefa84e72da041.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #11: Step 6 - Before: And I wait for page title to contain "OI Development - Home" (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-6---Before-And-I-wait-for-page-title-to-contain-OI-Development---Home--ffa58bd93ab409707aafaddf3ab8cfb725026aab.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #12: Failure Screenshot (image/png) ─────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Failure-Screenshot-cb449921f9855491b5761c09290439149d1aab4f.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #13: screenshot (image/png) ─────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #14: video (video/webm) ─────────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #15: trace (application/zip) ────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\trace.zip
    Usage:

        npx playwright show-trace test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[🎬 Starting scenario: TC0101- Login and Check the Default Data Source Order
📁 Feature: SPO Health Check
🏷️ Tags: @combined, @spo
📋 Step 1/15: Given I navigate to URL "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/"
✅ Step completed in 4614ms
📋 Step 2/15: Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000
✅ Step completed in 76ms
📋 Step 3/15: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>"
✅ Step completed in 123ms
📋 Step 4/15: And I click on "getByRole('button', { name: 'Next' })"
✅ Step completed in 167ms
📋 Step 5/15: Then I wait for element "getByRole('link', { name: 'OI Development', exact: true })" to be visible with timeout 60000
✅ Step completed in 11063ms
📋 Step 6/15: And I wait for page title to contain "OI Development - Home"
[[31merror[39m]: ❌ Step failed: And I wait for page title to contain "OI Development - Home"

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-1---Before-Given-I-navigate-to-URL-https-spo-global-kpmg-com-sites-GO-oi-bus-kpmgfinddeventerprisesearch--0874230e995514f05230eb09df7e564097deb05a.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-1---After-Given-I-navigate-to-URL-https-spo-global-kpmg-com-sites-GO-oi-bus-kpmgfinddeventerprisesearch-4614ms--76e0bc46f32100246452a29546397d4fd4e61208.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-7b97b6ecd8a021fd44aa7e3cc251f323f5aac40f.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-76ms--6d4618fa6469d69ce9894daee70fcbd3beb85ca3.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com--a31b3e7c43cb1efea11b3548fc5c4de968169b53.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com-123ms--9aed25838606bab71a24c398a8193d0e5e7144e6.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-4---Before-And-I-click-on-getByRole-button-name-Next--1cb637a407c45e252a89feaa09376d02ecff8032.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-4---After-And-I-click-on-getByRole-button-name-Next-167ms--39ca31def62bf7693a9303f1f6530a2005473251.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-5---Before-Then-I-wait-for-element-getByRole-link-name-OI-Development-exact-true-to-be-visible-with-timeout-60000-6f394438ac89195e969aa3c0716a8777148d78c7.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-5---After-Then-I-wait-for-element-getByRole-link-name-OI-Development-exact-true-to-be-visible-with-timeout-60000-11063ms--89c6bbe0aca926942991f53ad8cefa84e72da041.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-6---Before-And-I-wait-for-page-title-to-contain-OI-Development---Home--ffa58bd93ab409707aafaddf3ab8cfb725026aab.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Failure-Screenshot-cb449921f9855491b5761c09290439149d1aab4f.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\test-failed-1.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\video.webm]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[❌ Scenario failed: TC0101- Login and Check the Default Data Source Order page.title: Execution context was destroyed, most likely because of a navigation
    at PlaywrightWorld.getTitle [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-world.ts:48:28[90m)[39m
    at Object.handler [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\step-definition-registry.ts:190:37[90m)[39m
    at StepDefinitionRegistry.executeStep [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\step-definition-registry.ts:286:9[90m)[39m
    at PlaywrightCucumberRunner.executeStep [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-cucumber-runner.ts:196:7[90m)[39m
    at PlaywrightCucumberRunner.executeScenario [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-cucumber-runner.ts:231:9[90m)[39m
    at [90mC:\workspace\Playwright_Automation_Framework\[39mtests\cucumber-wrapper.spec.ts:45:9 {
  name: [32m'Error'[39m,
  [[32mSymbol(step)[39m]: {
    stepId: [32m'pw:api@38'[39m,
    location: {
      file: [32m'C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts'[39m,
      line: [33m48[39m,
      column: [33m28[39m,
      function: [32m'PlaywrightWorld.getTitle'[39m
    },
    category: [32m'pw:api'[39m,
    title: [32m'page.title'[39m,
    apiName: [32m'page.title'[39m,
    params: [90mundefined[39m,
    boxedStack: [90mundefined[39m,
    steps: [],
    attachmentIndices: [],
    info: TestStepInfoImpl {
      annotations: [],
      _testInfo: [36m[TestInfoImpl][39m,
      _stepId: [32m'pw:api@38'[39m
    },
    complete: [36m[Function: complete][39m,
    endWallTime: [33m1749100077896[39m,
    error: {
      message: [32m'Error: page.title: Execution context was destroyed, most likely because of a navigation'[39m,
      stack: [32m'Error: page.title: Execution context was destroyed, most likely because of a navigation\n'[39m +
        [32m'    at PlaywrightWorld.getTitle (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts:48:28)\n'[39m +
        [32m'    at Object.handler (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:190:37)\n'[39m +
        [32m'    at StepDefinitionRegistry.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:286:9)\n'[39m +
        [32m'    at PlaywrightCucumberRunner.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:196:7)\n'[39m +
        [32m'    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:231:9)\n'[39m +
        [32m'    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9'[39m,
      cause: [90mundefined[39m
    }
  }
}
]]>
</system-err>
</testcase>
</testsuite>
</testsuites>