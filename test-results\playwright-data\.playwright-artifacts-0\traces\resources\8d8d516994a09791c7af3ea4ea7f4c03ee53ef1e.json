[{"time": "2025-06-05T06:26:19.117Z", "iKey": "0b456e85-ed56-4af9-9d42-c8421de22c78", "name": "Microsoft.ApplicationInsights.0b456e85ed564af99d42c8421de22c78.Exception", "tags": {"ai.user.id": "MtaOO5q16Yb1YL9/ySlfMT", "ai.session.id": "IjZaK//Q7xUrWPaQK0qk5J", "ai.device.id": "browser", "ai.device.type": "Browser", "ai.operation.name": "/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx", "ai.operation.id": "591fd9cebefb46c6ab4793870a49f556", "ai.internal.sdkVersion": "javascript:3.3.8"}, "data": {"baseType": "ExceptionData", "baseData": {"ver": 2, "exceptions": [{"typeName": "OI-Error", "message": "OI-Error: <PERSON><PERSON> request previously failed", "hasFullStack": true, "stack": "OI-Error: Token request previously failed\n    at new t (https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js:179:182)\n    at e._getTokenDataInternal (https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js:179:2739)\n    at e._getTokenInternal (https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js:179:1450)\n    at e.getToken (https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js:179:1047)\n    at e.getToken (https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js:179:9414)\n    at e.<anonymous> (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:6981)\n    at https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:1303\n    at Object.next (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:1408)\n    at o (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:149)", "parsedStack": [{"level": 0, "method": "<no_method>", "assembly": "at new t (https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js:179:182)", "fileName": "https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js", "line": 179}, {"level": 1, "method": "e._getTokenDataInternal", "assembly": "at e._getTokenDataInternal (https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js:179:2739)", "fileName": "https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js", "line": 179}, {"level": 2, "method": "e._getTokenInternal", "assembly": "at e._getTokenInternal (https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js:179:1450)", "fileName": "https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js", "line": 179}, {"level": 3, "method": "e.getToken", "assembly": "at e.getToken (https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js:179:1047)", "fileName": "https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js", "line": 179}, {"level": 4, "method": "e.getToken", "assembly": "at e.getToken (https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js:179:9414)", "fileName": "https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js", "line": 179}, {"level": 5, "method": "e.<anonymous>", "assembly": "at e.<anonymous> (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:6981)", "fileName": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js", "line": 74}, {"level": 6, "method": "<no_method>", "assembly": "at https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:1303", "fileName": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js", "line": 74}, {"level": 7, "method": "Object.next", "assembly": "at Object.next (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:1408)", "fileName": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js", "line": 74}, {"level": 8, "method": "o", "assembly": "at o (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:149)", "fileName": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js", "line": 74}]}], "severityLevel": 3, "properties": {"OneIntranet Message": "Error getting onprem token", "OneIntranet Exception Message": "Token request previously failed", "typeName": "OI-Error", "KPMG Source": "OneIntranet", "KPMG HubSiteId": "36072d22-c34a-430f-96a5-6f4e3e097513", "OriginSiteUrl": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch", "OriginFullSiteUrl": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch"}}}}, {"time": "2025-06-05T06:26:19.224Z", "iKey": "0b456e85-ed56-4af9-9d42-c8421de22c78", "name": "Microsoft.ApplicationInsights.0b456e85ed564af99d42c8421de22c78.Exception", "tags": {"ai.user.id": "MtaOO5q16Yb1YL9/ySlfMT", "ai.session.id": "IjZaK//Q7xUrWPaQK0qk5J", "ai.device.id": "browser", "ai.device.type": "Browser", "ai.operation.name": "/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx", "ai.operation.id": "686f4f4a0cf6498ba0e1db8574d6a5b3", "ai.internal.sdkVersion": "javascript:3.3.8"}, "data": {"baseType": "ExceptionData", "baseData": {"ver": 2, "exceptions": [{"typeName": "OI-Error", "message": "OI-Error: Authentication: Cannot renewOnPremToken token", "hasFullStack": true, "stack": "OI-Error: Authentication: Cannot renewOnPremToken token\n    at https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:502:76820\n    at https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:502:76174\n    at Object.next (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:502:76279)\n    at i (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:502:77828)", "parsedStack": [{"level": 0, "method": "<no_method>", "assembly": "at https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:502:76820", "fileName": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js", "line": 502}, {"level": 1, "method": "<no_method>", "assembly": "at https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:502:76174", "fileName": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js", "line": 502}, {"level": 2, "method": "Object.next", "assembly": "at Object.next (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:502:76279)", "fileName": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js", "line": 502}, {"level": 3, "method": "i", "assembly": "at i (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:502:77828)", "fileName": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js", "line": 502}]}], "severityLevel": 3, "properties": {"OneIntranet Message": "Error in fetching search tips action", "OneIntranet Exception Message": "Authentication: Cannot renewOnPremToken token", "typeName": "OI-Error", "KPMG Source": "OneIntranet", "KPMG HubSiteId": "36072d22-c34a-430f-96a5-6f4e3e097513", "OriginSiteUrl": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch", "OriginFullSiteUrl": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch"}}}}, {"time": "2025-06-05T06:26:19.226Z", "iKey": "0b456e85-ed56-4af9-9d42-c8421de22c78", "name": "Microsoft.ApplicationInsights.0b456e85ed564af99d42c8421de22c78.Exception", "tags": {"ai.user.id": "MtaOO5q16Yb1YL9/ySlfMT", "ai.session.id": "IjZaK//Q7xUrWPaQK0qk5J", "ai.device.id": "browser", "ai.device.type": "Browser", "ai.operation.name": "/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx", "ai.operation.id": "686f4f4a0cf6498ba0e1db8574d6a5b3", "ai.internal.sdkVersion": "javascript:3.3.8"}, "data": {"baseType": "ExceptionData", "baseData": {"ver": 2, "exceptions": [{"typeName": "OI-Error", "message": "OI-Error: <PERSON><PERSON> request previously failed", "hasFullStack": true, "stack": "OI-Error: Token request previously failed\n    at new t (https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js:179:182)\n    at e._getTokenDataInternal (https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js:179:2739)\n    at e._getTokenInternal (https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js:179:1450)\n    at e.getToken (https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js:179:1047)\n    at e.getToken (https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js:179:9414)\n    at e.<anonymous> (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:6981)\n    at https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:1303\n    at Object.next (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:1408)\n    at o (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:149)", "parsedStack": [{"level": 0, "method": "<no_method>", "assembly": "at new t (https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js:179:182)", "fileName": "https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js", "line": 179}, {"level": 1, "method": "e._getTokenDataInternal", "assembly": "at e._getTokenDataInternal (https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js:179:2739)", "fileName": "https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js", "line": 179}, {"level": 2, "method": "e._getTokenInternal", "assembly": "at e._getTokenInternal (https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js:179:1450)", "fileName": "https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js", "line": 179}, {"level": 3, "method": "e.getToken", "assembly": "at e.getToken (https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js:179:1047)", "fileName": "https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js", "line": 179}, {"level": 4, "method": "e.getToken", "assembly": "at e.getToken (https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js:179:9414)", "fileName": "https://res-1.public.onecdn.static.microsoft/files/sp-client/sp-pages-assembly_en-us_0c67568e128fa63e799e92b758e4aad0.js", "line": 179}, {"level": 5, "method": "e.<anonymous>", "assembly": "at e.<anonymous> (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:6981)", "fileName": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js", "line": 74}, {"level": 6, "method": "<no_method>", "assembly": "at https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:1303", "fileName": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js", "line": 74}, {"level": 7, "method": "Object.next", "assembly": "at Object.next (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:1408)", "fileName": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js", "line": 74}, {"level": 8, "method": "o", "assembly": "at o (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:149)", "fileName": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js", "line": 74}]}], "severityLevel": 3, "properties": {"OneIntranet Message": "Error getting onprem token", "OneIntranet Exception Message": "Token request previously failed", "typeName": "OI-Error", "KPMG Source": "OneIntranet", "KPMG HubSiteId": "36072d22-c34a-430f-96a5-6f4e3e097513", "OriginSiteUrl": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch", "OriginFullSiteUrl": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch"}}}}, {"time": "2025-06-05T06:26:19.228Z", "iKey": "0b456e85-ed56-4af9-9d42-c8421de22c78", "name": "Microsoft.ApplicationInsights.0b456e85ed564af99d42c8421de22c78.Exception", "tags": {"ai.user.id": "MtaOO5q16Yb1YL9/ySlfMT", "ai.session.id": "IjZaK//Q7xUrWPaQK0qk5J", "ai.device.id": "browser", "ai.device.type": "Browser", "ai.operation.name": "/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx", "ai.operation.id": "686f4f4a0cf6498ba0e1db8574d6a5b3", "ai.internal.sdkVersion": "javascript:3.3.8"}, "data": {"baseType": "ExceptionData", "baseData": {"ver": 2, "exceptions": [{"typeName": "OI-Error", "message": "OI-Error: Authentication: Cannot renewOnPremToken token", "hasFullStack": true, "stack": "OI-Error: Authentication: Cannot renewOnPremToken token\n    at Ka.<anonymous> (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:591033)\n    at https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:584638\n    at Object.next (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:584743)\n    at o (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:583483)", "parsedStack": [{"level": 0, "method": "<PERSON>.<anonymous>", "assembly": "at Ka.<anonymous> (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:591033)", "fileName": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js", "line": 74}, {"level": 1, "method": "<no_method>", "assembly": "at https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:584638", "fileName": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js", "line": 74}, {"level": 2, "method": "Object.next", "assembly": "at Object.next (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:584743)", "fileName": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js", "line": 74}, {"level": 3, "method": "o", "assembly": "at o (https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js:74:583483)", "fileName": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/ClientSideAssets/739af9cb-34f0-4125-a986-4edb7947a090/one-intranet-search-results-web-part_61631235140e23a4f8bf.js", "line": 74}]}], "severityLevel": 3, "properties": {"OneIntranet Message": "Error in fetching version translations action", "OneIntranet Exception Message": "Authentication: Cannot renewOnPremToken token", "typeName": "OI-Error", "KPMG Source": "OneIntranet", "KPMG HubSiteId": "36072d22-c34a-430f-96a5-6f4e3e097513", "OriginSiteUrl": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch", "OriginFullSiteUrl": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch"}}}}, {"time": "2025-06-05T06:26:08.814Z", "iKey": "0b456e85-ed56-4af9-9d42-c8421de22c78", "name": "Microsoft.ApplicationInsights.0b456e85ed564af99d42c8421de22c78.Pageview", "tags": {"ai.user.id": "MtaOO5q16Yb1YL9/ySlfMT", "ai.session.id": "IjZaK//Q7xUrWPaQK0qk5J", "ai.device.id": "browser", "ai.device.type": "Browser", "ai.operation.name": "/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx", "ai.operation.id": "d44606986f9c4951963faacc6dec2fa9", "ai.internal.sdkVersion": "javascript:3.3.8", "ai.internal.snippet": "-"}, "data": {"baseType": "PageviewData", "baseData": {"ver": 2, "name": "Results", "url": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx?q=kpmg&datasource=intranet&origin=portal&caller=https%3A%2F%2Fspo-global.kpmg.com%2Fsites%2FGO-oi-bus-kpmgfinddeventerprisesearch&premium=false", "duration": "00:00:00.000", "properties": {"refUri": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx?q=kpmg&k=kpmg", "KPMG Source": "OneIntranet", "KPMG HubSiteId": "36072d22-c34a-430f-96a5-6f4e3e097513", "OriginSiteUrl": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch", "OriginFullSiteUrl": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch"}, "measurements": {"duration": 0}, "id": "d44606986f9c4951963faacc6dec2fa9"}}}]