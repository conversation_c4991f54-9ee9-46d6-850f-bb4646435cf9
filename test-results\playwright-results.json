{"config": {"configFile": "C:\\workspace\\Playwright_Automation_Framework\\playwright.config.ts", "rootDir": "C:/workspace/Playwright_Automation_Framework/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report", "open": "always", "host": "localhost", "port": 9323}], ["line", null], ["json", {"outputFile": "test-results/playwright-results.json"}], ["junit", {"outputFile": "test-results/junit-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/workspace/Playwright_Automation_Framework/test-results/playwright-data", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "C:/workspace/Playwright_Automation_Framework/tests", "testIgnore": [], "testMatch": ["**/*.spec.ts", "**/*.spec.js"], "timeout": 300000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 6, "webServer": null}, "suites": [{"title": "cucumber-wrapper.spec.ts", "file": "cucumber-wrapper.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "SPO Health Check", "file": "cucumber-wrapper.spec.ts", "line": 26, "column": 8, "specs": [{"title": "TC0101- <PERSON><PERSON> and Check the Default Data Source Order", "ok": false, "tags": [], "tests": [{"timeout": 300000, "annotations": [{"type": "feature", "description": "SPO Health Check"}, {"type": "tags", "description": "@combined, @spo"}, {"type": "file", "description": "src\\features\\spo-health-check.feature"}], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 36893, "error": {"message": "Error: page.screenshot: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - taking page screenshot\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n", "stack": "Error: page.screenshot: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - taking page screenshot\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\n    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:270:18)\n    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9", "location": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts", "column": 18, "line": 270}, "snippet": "\u001b[90m   at \u001b[39m..\\src\\core\\playwright-cucumber-runner.ts:270\n\n\u001b[0m \u001b[90m 268 |\u001b[39m       \u001b[90m// Take failure screenshot\u001b[39m\n \u001b[90m 269 |\u001b[39m       \u001b[36mconst\u001b[39m failureScreenshot \u001b[33m=\u001b[39m path\u001b[33m.\u001b[39mjoin(testInfo\u001b[33m.\u001b[39moutputDir\u001b[33m,\u001b[39m \u001b[32m'failure-screenshot.png'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 270 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mscreenshot({ path\u001b[33m:\u001b[39m failureScreenshot\u001b[33m,\u001b[39m fullPage\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 271 |\u001b[39m       \u001b[36mawait\u001b[39m testInfo\u001b[33m.\u001b[39mattach(\u001b[32m'Failure Screenshot'\u001b[39m\u001b[33m,\u001b[39m {\n \u001b[90m 272 |\u001b[39m         path\u001b[33m:\u001b[39m failureScreenshot\u001b[33m,\u001b[39m\n \u001b[90m 273 |\u001b[39m         contentType\u001b[33m:\u001b[39m \u001b[32m'image/png'\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts", "column": 18, "line": 270}, "message": "Error: page.screenshot: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - taking page screenshot\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\n\n\u001b[90m   at \u001b[39m..\\src\\core\\playwright-cucumber-runner.ts:270\n\n\u001b[0m \u001b[90m 268 |\u001b[39m       \u001b[90m// Take failure screenshot\u001b[39m\n \u001b[90m 269 |\u001b[39m       \u001b[36mconst\u001b[39m failureScreenshot \u001b[33m=\u001b[39m path\u001b[33m.\u001b[39mjoin(testInfo\u001b[33m.\u001b[39moutputDir\u001b[33m,\u001b[39m \u001b[32m'failure-screenshot.png'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 270 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mscreenshot({ path\u001b[33m:\u001b[39m failureScreenshot\u001b[33m,\u001b[39m fullPage\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 271 |\u001b[39m       \u001b[36mawait\u001b[39m testInfo\u001b[33m.\u001b[39mattach(\u001b[32m'Failure Screenshot'\u001b[39m\u001b[33m,\u001b[39m {\n \u001b[90m 272 |\u001b[39m         path\u001b[33m:\u001b[39m failureScreenshot\u001b[33m,\u001b[39m\n \u001b[90m 273 |\u001b[39m         contentType\u001b[33m:\u001b[39m \u001b[32m'image/png'\u001b[39m\u001b[0m\n\u001b[2m    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:270:18)\u001b[22m\n\u001b[2m    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9\u001b[22m"}, {"message": "Error: end of central directory record signature not found"}], "stdout": [{"text": "🎬 Starting scenario: TC0101- <PERSON><PERSON> and Check the Default Data Source Order\n"}, {"text": "📁 Feature: SPO Health Check\n"}, {"text": "🏷️ Tags: @combined, @spo\n"}, {"text": "📋 Step 1/34: Given I navigate to URL \"https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/\"\n"}, {"text": "✅ Step completed in 4305ms\n"}, {"text": "📋 Step 2/34: Then I wait for \"getByRole('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 56ms\n"}, {"text": "📋 Step 3/34: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"\n"}, {"text": "✅ Step completed in 72ms\n"}, {"text": "📋 Step 4/34: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\"\n"}, {"text": "✅ Step completed in 134ms\n"}, {"text": "📋 Step 5/34: Then I wait for element \"getByRole('link', { name: 'OI Development', exact: true })\" to be visible with timeout 60000\n"}], "stderr": [{"text": "❌ Scenario failed: TC0101- Lo<PERSON> and Check the Default Data Source Order page.screenshot: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - taking page screenshot\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\n    at PlaywrightCucumberRunner.executeScenario \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\playwright-cucumber-runner.ts:236:20\u001b[90m)\u001b[39m\n    at \u001b[90mC:\\workspace\\Playwright_Automation_Framework\\\u001b[39mtests\\cucumber-wrapper.spec.ts:45:9 {\n  name: \u001b[32m'TimeoutError'\u001b[39m,\n  [\u001b[32mSymbol(step)\u001b[39m]: {\n    stepId: \u001b[32m'pw:api@32'\u001b[39m,\n    location: {\n      file: \u001b[32m'C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-cucumber-runner.ts'\u001b[39m,\n      line: \u001b[33m236\u001b[39m,\n      column: \u001b[33m20\u001b[39m,\n      function: \u001b[32m'PlaywrightCucumberRunner.executeScenario'\u001b[39m\n    },\n    category: \u001b[32m'pw:api'\u001b[39m,\n    title: \u001b[32m'page.screenshot'\u001b[39m,\n    apiName: \u001b[32m'page.screenshot'\u001b[39m,\n    params: {\n      path: \u001b[32m'C:\\\\workspace\\\\Playwright_Automation_Framework\\\\test-results\\\\playwright-data\\\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\\\step-5-after.png'\u001b[39m,\n      fullPage: \u001b[33mtrue\u001b[39m,\n      mask: \u001b[90mundefined\u001b[39m,\n      type: \u001b[32m'png'\u001b[39m\n    },\n    boxedStack: \u001b[90mundefined\u001b[39m,\n    steps: [],\n    attachmentIndices: [],\n    info: TestStepInfoImpl {\n      annotations: [],\n      _testInfo: \u001b[36m[TestInfoImpl]\u001b[39m,\n      _stepId: \u001b[32m'pw:api@32'\u001b[39m\n    },\n    complete: \u001b[36m[Function: complete]\u001b[39m,\n    endWallTime: \u001b[33m1749102779136\u001b[39m,\n    error: {\n      message: \u001b[32m'TimeoutError: page.screenshot: Timeout 15000ms exceeded.\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - taking page screenshot\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - waiting for fonts to load...\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - fonts loaded\\x1B[22m\\n'\u001b[39m,\n      stack: \u001b[32m'TimeoutError: page.screenshot: Timeout 15000ms exceeded.\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - taking page screenshot\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - waiting for fonts to load...\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - fonts loaded\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'    at PlaywrightCucumberRunner.executeScenario (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-cucumber-runner.ts:236:20)\\n'\u001b[39m +\n        \u001b[32m'    at C:\\\\workspace\\\\Playwright_Automation_Framework\\\\tests\\\\cucumber-wrapper.spec.ts:45:9'\u001b[39m,\n      cause: \u001b[90mundefined\u001b[39m\n    }\n  }\n}\n"}], "retry": 0, "startTime": "2025-06-05T05:52:24.747Z", "annotations": [{"type": "feature", "description": "SPO Health Check"}, {"type": "tags", "description": "@combined, @spo"}, {"type": "file", "description": "src\\features\\spo-health-check.feature"}], "attachments": [{"name": "Step 1 - Before: Given I navigate to URL \"https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-1---Before-Given-I-navigate-to-URL-https-spo-global-kpmg-com-sites-GO-oi-bus-kpmgfinddeventerprisesearch--0874230e995514f05230eb09df7e564097deb05a.png"}, {"name": "Step 1 - After: Given I navigate to URL \"https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/\" (4305ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-1---After-Given-I-navigate-to-URL-https-spo-global-kpmg-com-sites-GO-oi-bus-kpmgfinddeventerprisesearch-4305ms--76e0bc46f32100246452a29546397d4fd4e61208.png"}, {"name": "Step 2 - Before: Then I wait for \"getByRole('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-7b97b6ecd8a021fd44aa7e3cc251f323f5aac40f.png"}, {"name": "Step 2 - After: Then I wait for \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000 (56ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-56ms--6d4618fa6469d69ce9894daee70fcbd3beb85ca3.png"}, {"name": "Step 3 - Before: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-j<PERSON><PERSON><PERSON>-kpmg-com--a31b3e7c43cb1efea11b3548fc5c4de968169b53.png"}, {"name": "Step 3 - After: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\" (72ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-j<PERSON><PERSON><PERSON>-kpmg-com-72ms--9aed25838606bab71a24c398a8193d0e5e7144e6.png"}, {"name": "Step 4 - Before: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-4---Before-And-I-click-on-getByRole-button-name-Next--1cb637a407c45e252a89feaa09376d02ecff8032.png"}, {"name": "Step 4 - After: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\" (134ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-4---After-And-I-click-on-getByRole-button-name-Next-134ms--39ca31def62bf7693a9303f1f6530a2005473251.png"}, {"name": "Step 5 - Before: Then I wait for element \"getByRole('link', { name: 'OI Development', exact: true })\" to be visible with timeout 60000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-5---Before-Then-I-wait-for-element-getByRole-link-name-OI-Development-exact-true-to-be-visible-with-timeout-60000-6f394438ac89195e969aa3c0716a8777148d78c7.png"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts", "column": 18, "line": 270}}], "status": "unexpected"}], "id": "f0a7916b485de33344dc-9278188bd534212a88a0", "file": "cucumber-wrapper.spec.ts", "line": 34, "column": 11}]}]}], "errors": [], "stats": {"startTime": "2025-06-05T05:52:21.461Z", "duration": 43270.748999999996, "expected": 0, "skipped": 0, "unexpected": 1, "flaky": 0}}