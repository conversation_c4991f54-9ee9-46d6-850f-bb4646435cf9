{"config": {"configFile": "C:\\workspace\\Playwright_Automation_Framework\\playwright.config.ts", "rootDir": "C:/workspace/Playwright_Automation_Framework/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report", "open": "always", "host": "localhost", "port": 9323}], ["line", null], ["json", {"outputFile": "test-results/playwright-results.json"}], ["junit", {"outputFile": "test-results/junit-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/workspace/Playwright_Automation_Framework/test-results/playwright-data", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "C:/workspace/Playwright_Automation_Framework/tests", "testIgnore": [], "testMatch": ["**/*.spec.ts", "**/*.spec.js"], "timeout": 300000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 6, "webServer": null}, "suites": [{"title": "cucumber-wrapper.spec.ts", "file": "cucumber-wrapper.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "SPO Health Check", "file": "cucumber-wrapper.spec.ts", "line": 26, "column": 8, "specs": [{"title": "TC0101- <PERSON><PERSON> and Check the Default Data Source Order", "ok": false, "tags": [], "tests": [{"timeout": 300000, "annotations": [{"type": "feature", "description": "SPO Health Check"}, {"type": "tags", "description": "@combined, @spo"}, {"type": "file", "description": "src\\features\\spo-health-check.feature"}], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 96922, "error": {"message": "TimeoutError: locator.waitFor: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getByRole('button', { name: 'All', exact: true }) to be visible\u001b[22m\n", "stack": "TimeoutError: locator.waitFor: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getBy<PERSON><PERSON>('button', { name: 'All', exact: true }) to be visible\u001b[22m\n\n    at PlaywrightWorld.waitForElement (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts:250:19)\n    at Object.handler (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:170:21)\n    at StepDefinitionRegistry.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:286:23)\n    at PlaywrightCucumberRunner.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:196:31)\n    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:231:20)\n    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9", "location": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts", "column": 19, "line": 250}, "snippet": "\u001b[90m   at \u001b[39m..\\src\\core\\playwright-world.ts:250\n\n\u001b[0m \u001b[90m 248 |\u001b[39m     \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mlogger\u001b[33m.\u001b[39minfo(\u001b[32m`⏳ Waiting for element: ${selector}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 249 |\u001b[39m     \u001b[36mconst\u001b[39m element \u001b[33m=\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mparse<PERSON>ocator(selector)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 250 |\u001b[39m     \u001b[36mawait\u001b[39m element\u001b[33m.\u001b[39mwaitFor({ state\u001b[33m:\u001b[39m \u001b[32m'visible'\u001b[39m\u001b[33m,\u001b[39m timeout })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 251 |\u001b[39m   }\n \u001b[90m 252 |\u001b[39m\n \u001b[90m 253 |\u001b[39m   \u001b[90m/**\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts", "column": 19, "line": 250}, "message": "TimeoutError: locator.waitFor: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getB<PERSON><PERSON><PERSON>('button', { name: 'All', exact: true }) to be visible\u001b[22m\n\n\n\u001b[90m   at \u001b[39m..\\src\\core\\playwright-world.ts:250\n\n\u001b[0m \u001b[90m 248 |\u001b[39m     \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mlogger\u001b[33m.\u001b[39minfo(\u001b[32m`⏳ Waiting for element: ${selector}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 249 |\u001b[39m     \u001b[36mconst\u001b[39m element \u001b[33m=\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mparseLocator(selector)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 250 |\u001b[39m     \u001b[36mawait\u001b[39m element\u001b[33m.\u001b[39mwaitFor({ state\u001b[33m:\u001b[39m \u001b[32m'visible'\u001b[39m\u001b[33m,\u001b[39m timeout })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 251 |\u001b[39m   }\n \u001b[90m 252 |\u001b[39m\n \u001b[90m 253 |\u001b[39m   \u001b[90m/**\u001b[39m\u001b[0m\n\u001b[2m    at PlaywrightWorld.waitForElement (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts:250:19)\u001b[22m\n\u001b[2m    at Object.handler (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:170:21)\u001b[22m\n\u001b[2m    at StepDefinitionRegistry.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:286:23)\u001b[22m\n\u001b[2m    at PlaywrightCucumberRunner.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:196:31)\u001b[22m\n\u001b[2m    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:231:20)\u001b[22m\n\u001b[2m    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9\u001b[22m"}], "stdout": [{"text": "🎬 Starting scenario: TC0101- <PERSON><PERSON> and Check the Default Data Source Order\n"}, {"text": "📁 Feature: SPO Health Check\n"}, {"text": "🏷️ Tags: @combined, @spo\n"}, {"text": "📋 Step 1/15: Given I navigate to URL \"https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/\"\n"}, {"text": "✅ Step completed in 24446ms\n"}, {"text": "📋 Step 2/15: Then I wait for \"getByRole('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 56ms\n"}, {"text": "📋 Step 3/15: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"\n"}, {"text": "✅ Step completed in 38ms\n"}, {"text": "📋 Step 4/15: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\"\n"}, {"text": "✅ Step completed in 97ms\n"}, {"text": "📋 Step 5/15: Then I wait for element \"getByRole('link', { name: 'OI Development', exact: true })\" to be visible with timeout 60000\n"}, {"text": "✅ Step completed in 9822ms\n"}, {"text": "📋 Step 6/15: And I wait for page title to contain \"OI Development - Home\"\n"}, {"text": "✅ Step completed in 3305ms\n"}, {"text": "📋 Step 7/15: Then I wait for element \"getByRole('combobox', { name: 'Search box. Suggestions' })\" to be visible with timeout 60000\n"}, {"text": "✅ Step completed in 6625ms\n"}, {"text": "📋 Step 8/15: When I fill \"getBy<PERSON><PERSON>('combobox', { name: 'Search box. Suggestions' })\" with \"kpmg\"\n"}, {"text": "✅ Step completed in 131ms\n"}, {"text": "📋 Step 9/15: And I press \"Enter\"\n"}, {"text": "✅ Step completed in 282ms\n"}, {"text": "📋 Step 10/15: And I wait for page title to contain \"Results\"\n"}, {"text": "✅ Step completed in 1076ms\n"}, {"text": "📋 Step 11/15: Then I wait for element \"getByRole('link', { name: 'Organizational Logo' })\" to be visible with timeout 60000\n"}, {"text": "✅ Step completed in 2775ms\n"}, {"text": "📋 Step 12/15: Then I wait for \"getBy<PERSON><PERSON>('button', { name: 'All', exact: true })\" to be visible with timeout 30000\n"}, {"text": "[\u001b[31merror\u001b[39m]: ❌ Step failed: Then I wait for \"getBy<PERSON><PERSON>('button', { name: 'All', exact: true })\" to be visible with timeout 30000\r\n"}], "stderr": [{"text": "❌ Scenario failed: TC0101- Login and Check the Default Data Source Order locator.waitFor: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getBy<PERSON><PERSON>('button', { name: 'All', exact: true }) to be visible\u001b[22m\n\n    at PlaywrightWorld.waitForElement \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\playwright-world.ts:250:19\u001b[90m)\u001b[39m\n    at Object.handler \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\step-definition-registry.ts:170:21\u001b[90m)\u001b[39m\n    at StepDefinitionRegistry.executeStep \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\step-definition-registry.ts:286:23\u001b[90m)\u001b[39m\n    at PlaywrightCucumberRunner.executeStep \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\playwright-cucumber-runner.ts:196:31\u001b[90m)\u001b[39m\n    at PlaywrightCucumberRunner.executeScenario \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\playwright-cucumber-runner.ts:231:20\u001b[90m)\u001b[39m\n    at \u001b[90mC:\\workspace\\Playwright_Automation_Framework\\\u001b[39mtests\\cucumber-wrapper.spec.ts:45:9 {\n  name: \u001b[32m'TimeoutError'\u001b[39m,\n  [\u001b[32mSymbol(step)\u001b[39m]: {\n    stepId: \u001b[32m'pw:api@72'\u001b[39m,\n    location: {\n      file: \u001b[32m'C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-world.ts'\u001b[39m,\n      line: \u001b[33m250\u001b[39m,\n      column: \u001b[33m19\u001b[39m,\n      function: \u001b[32m'PlaywrightWorld.waitForElement'\u001b[39m\n    },\n    category: \u001b[32m'pw:api'\u001b[39m,\n    title: \u001b[32m\"locator.getByRole('button', { name: 'All', exact: true }).waitFor\"\u001b[39m,\n    apiName: \u001b[32m'locator.waitFor'\u001b[39m,\n    params: {\n      selector: \u001b[32m'internal:role=button[name=\"All\"s]'\u001b[39m,\n      strict: \u001b[33mtrue\u001b[39m,\n      omitReturnValue: \u001b[33mtrue\u001b[39m,\n      state: \u001b[32m'visible'\u001b[39m,\n      timeout: \u001b[33m30000\u001b[39m\n    },\n    boxedStack: \u001b[90mundefined\u001b[39m,\n    steps: [],\n    attachmentIndices: [],\n    info: TestStepInfoImpl {\n      annotations: [],\n      _testInfo: \u001b[36m[TestInfoImpl]\u001b[39m,\n      _stepId: \u001b[32m'pw:api@72'\u001b[39m\n    },\n    complete: \u001b[36m[Function: complete]\u001b[39m,\n    endWallTime: \u001b[33m1749098838221\u001b[39m,\n    error: {\n      message: \u001b[32m'TimeoutError: locator.waitFor: Timeout 30000ms exceeded.\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m\"\\x1B[2m  - waiting for getByRole('button', { name: 'All', exact: true }) to be visible\\x1B[22m\\n\"\u001b[39m,\n      stack: \u001b[32m'TimeoutError: locator.waitFor: Timeout 30000ms exceeded.\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m\"\\x1B[2m  - waiting for getByRole('button', { name: 'All', exact: true }) to be visible\\x1B[22m\\n\"\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'    at PlaywrightWorld.waitForElement (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-world.ts:250:19)\\n'\u001b[39m +\n        \u001b[32m'    at Object.handler (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\step-definition-registry.ts:170:21)\\n'\u001b[39m +\n        \u001b[32m'    at StepDefinitionRegistry.executeStep (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\step-definition-registry.ts:286:23)\\n'\u001b[39m +\n        \u001b[32m'    at PlaywrightCucumberRunner.executeStep (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-cucumber-runner.ts:196:31)\\n'\u001b[39m +\n        \u001b[32m'    at PlaywrightCucumberRunner.executeScenario (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-cucumber-runner.ts:231:20)\\n'\u001b[39m +\n        \u001b[32m'    at C:\\\\workspace\\\\Playwright_Automation_Framework\\\\tests\\\\cucumber-wrapper.spec.ts:45:9'\u001b[39m,\n      cause: \u001b[90mundefined\u001b[39m\n    }\n  }\n}\n"}], "retry": 0, "startTime": "2025-06-05T04:45:41.356Z", "annotations": [{"type": "feature", "description": "SPO Health Check"}, {"type": "tags", "description": "@combined, @spo"}, {"type": "file", "description": "src\\features\\spo-health-check.feature"}], "attachments": [{"name": "Step 1 - Before: Given I navigate to URL \"https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-1---Before-Given-I-navigate-to-URL-https-spo-global-kpmg-com-sites-GO-oi-bus-kpmgfinddeventerprisesearch--0874230e995514f05230eb09df7e564097deb05a.png"}, {"name": "Step 1 - After: Given I navigate to URL \"https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/\" (24446ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-1---After-Given-I-navigate-to-URL-https-spo-global-kpmg-com-sites-GO-oi-bus-kpmgfinddeventerprisesearch-24446ms--76e0bc46f32100246452a29546397d4fd4e61208.png"}, {"name": "Step 2 - Before: Then I wait for \"getByRole('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-7b97b6ecd8a021fd44aa7e3cc251f323f5aac40f.png"}, {"name": "Step 2 - After: Then I wait for \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000 (56ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-56ms--6d4618fa6469d69ce9894daee70fcbd3beb85ca3.png"}, {"name": "Step 3 - Before: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-j<PERSON><PERSON><PERSON>-kpmg-com--a31b3e7c43cb1efea11b3548fc5c4de968169b53.png"}, {"name": "Step 3 - After: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\" (38ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-j<PERSON><PERSON><PERSON>-kpmg-com-38ms--9aed25838606bab71a24c398a8193d0e5e7144e6.png"}, {"name": "Step 4 - Before: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-4---Before-And-I-click-on-getByRole-button-name-Next--1cb637a407c45e252a89feaa09376d02ecff8032.png"}, {"name": "Step 4 - After: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\" (97ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-4---After-And-I-click-on-getByRole-button-name-Next-97ms--39ca31def62bf7693a9303f1f6530a2005473251.png"}, {"name": "Step 5 - Before: Then I wait for element \"getByRole('link', { name: 'OI Development', exact: true })\" to be visible with timeout 60000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-5---Before-Then-I-wait-for-element-getByRole-link-name-OI-Development-exact-true-to-be-visible-with-timeout-60000-6f394438ac89195e969aa3c0716a8777148d78c7.png"}, {"name": "Step 5 - After: Then I wait for element \"getByRole('link', { name: 'OI Development', exact: true })\" to be visible with timeout 60000 (9822ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-5---After-Then-I-wait-for-element-getByRole-link-name-OI-Development-exact-true-to-be-visible-with-timeout-60000-9822ms--89c6bbe0aca926942991f53ad8cefa84e72da041.png"}, {"name": "Step 6 - Before: And I wait for page title to contain \"OI Development - Home\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-6---Before-And-I-wait-for-page-title-to-contain-OI-Development---Home--ffa58bd93ab409707aafaddf3ab8cfb725026aab.png"}, {"name": "Step 6 - After: And I wait for page title to contain \"OI Development - Home\" (3305ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-6---After-And-I-wait-for-page-title-to-contain-OI-Development---Home-3305ms--f08e39671a2111ca88989554e63b3ac94e863b55.png"}, {"name": "Step 7 - Before: Then I wait for element \"getByRole('combobox', { name: 'Search box. Suggestions' })\" to be visible with timeout 60000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-7---Before-Then-I-wait-for-element-getByRole-combobox-name-Search-box-Suggestions-to-be-visible-with-timeout-60000-58373359d7360eb289842f5267d21ae12f42a7f6.png"}, {"name": "Step 7 - After: Then I wait for element \"getByRole('combobox', { name: 'Search box. Suggestions' })\" to be visible with timeout 60000 (6625ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-7---After-Then-I-wait-for-element-getByRole-combobox-name-Search-box-Suggestions-to-be-visible-with-timeout-60000-6625ms--2c045a4d3cc1bf2a4b686e49aa666c611bdf3a3d.png"}, {"name": "Step 8 - Before: When I fill \"getBy<PERSON><PERSON>('combobox', { name: 'Search box. Suggestions' })\" with \"kpmg\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-8---Before-When-I-fill-getByRole-combobox-name-Search-box-Suggestions-with-kpmg--f89b4c5e80f021f86ed31cd87734e9ea74982f46.png"}, {"name": "Step 8 - After: When I fill \"getBy<PERSON><PERSON>('combobox', { name: 'Search box. Suggestions' })\" with \"kpmg\" (131ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-8---After-When-I-fill-getByRole-combobox-name-Search-box-Suggestions-with-kpmg-131ms--4f7c18327be60247a70633fcb5287d48efafc641.png"}, {"name": "Step 9 - Before: And I press \"Enter\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-9---Before-And-I-press-Enter--ef98a150dc01e4192950ccacfae2e300eef6e242.png"}, {"name": "Step 9 - After: And I press \"Enter\" (282ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-9---After-And-I-press-Enter-282ms--8751f3c02cb1a72fe804962f6dc7fb0accc8e6c2.png"}, {"name": "Step 10 - Before: And I wait for page title to contain \"Results\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-10---Before-And-I-wait-for-page-title-to-contain-Results--fd1995806bcdcafdff5d0d5abd0a06cc44142d58.png"}, {"name": "Step 10 - After: And I wait for page title to contain \"Results\" (1076ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-10---After-And-I-wait-for-page-title-to-contain-Results-1076ms--1d46c4ddc7ef01f814fc388ec39bf0fca64bf19e.png"}, {"name": "Step 11 - Before: Then I wait for element \"getBy<PERSON>ole('link', { name: 'Organizational Logo' })\" to be visible with timeout 60000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-11---Before-Then-I-wait-for-element-getByRole-link-name-Organizational-Logo-to-be-visible-with-timeout-60000-034b8f7842b82a022974e73de932847d9621310b.png"}, {"name": "Step 11 - After: Then I wait for element \"getBy<PERSON><PERSON>('link', { name: 'Organizational Logo' })\" to be visible with timeout 60000 (2775ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-11---After-Then-I-wait-for-element-getByRole-link-name-Organizational-Logo-to-be-visible-with-timeout-60000-2775ms--67b6c4834cba3b6deb36776b7d7831423262fa00.png"}, {"name": "Step 12 - Before: Then I wait for \"getBy<PERSON><PERSON>('button', { name: 'All', exact: true })\" to be visible with timeout 30000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Step-12---Before-Then-I-wait-for-getByRole-button-name-All-exact-true-to-be-visible-with-timeout-30000-6898384945fd0dac2626220256b2cdf64eb6b973.png"}, {"name": "Failure Screenshot", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\attachments\\Failure-Screenshot-cb449921f9855491b5761c09290439149d1aab4f.png"}, {"name": "screenshot", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\test-failed-1.png"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts", "column": 19, "line": 250}}], "status": "unexpected"}], "id": "f0a7916b485de33344dc-9278188bd534212a88a0", "file": "cucumber-wrapper.spec.ts", "line": 34, "column": 11}]}]}], "errors": [], "stats": {"startTime": "2025-06-05T04:45:38.621Z", "duration": 101203.7, "expected": 0, "skipped": 0, "unexpected": 1, "flaky": 0}}