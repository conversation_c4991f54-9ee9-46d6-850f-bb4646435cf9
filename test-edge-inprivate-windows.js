const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

async function testEdgeInPrivateWindows() {
  console.log('Testing Edge InPrivate on Windows...');
  
  // Common Edge executable paths on Windows
  const edgePaths = [
    'C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe',
    'C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe',
    process.env.PROGRAMFILES + '\\Microsoft\\Edge\\Application\\msedge.exe',
    process.env['PROGRAMFILES(X86)'] + '\\Microsoft\\Edge\\Application\\msedge.exe'
  ];
  
  let edgeExePath = null;
  
  // Find Edge executable
  for (const edgePath of edgePaths) {
    if (fs.existsSync(edgePath)) {
      edgeExePath = edgePath;
      console.log(`✅ Found Edge at: ${edgePath}`);
      break;
    }
  }
  
  if (!edgeExePath) {
    console.error('❌ Edge executable not found in common locations');
    return;
  }
  
  try {
    console.log('Launching Edge in InPrivate mode...');
    
    // Launch Edge with InPrivate flag
    const edgeProcess = spawn(edgeExePath, [
      '--inprivate',
      '--new-window',
      'https://www.google.com'
    ], {
      detached: false,
      stdio: 'pipe'
    });
    
    edgeProcess.stdout.on('data', (data) => {
      console.log(`Edge stdout: ${data}`);
    });
    
    edgeProcess.stderr.on('data', (data) => {
      console.log(`Edge stderr: ${data}`);
    });
    
    edgeProcess.on('close', (code) => {
      console.log(`Edge process exited with code ${code}`);
    });
    
    console.log('✅ Edge launched with --inprivate flag');
    console.log('🔍 Check if Edge opened in InPrivate mode (should show "InPrivate" in the window)');
    
    // Keep the process running for a bit
    setTimeout(() => {
      console.log('Test completed. Edge should be running in InPrivate mode.');
    }, 3000);
    
  } catch (error) {
    console.error('❌ Failed to launch Edge:', error.message);
  }
}

testEdgeInPrivateWindows();
