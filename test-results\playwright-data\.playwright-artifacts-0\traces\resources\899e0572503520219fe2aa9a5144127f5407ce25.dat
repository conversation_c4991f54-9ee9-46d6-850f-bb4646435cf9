<html><head><script type='text/javascript'>var AutoSuggest;(function(n){var t;(function(t){xhr=function(){return new XMLHttpRequest};getDiagnostics=function(){return performance.getEntriesByType("resource")};var i=function(){function t(n){this.addAdditionalInfoUrlParameters=!0;this.config=n}t.prototype.fetchUrl=function(t,i,r){r===void 0&&(r="GET");var o=!1;if(t.additionalInfo){t.additionalInfo[n.AdditionalInfoKeys.diagnostics]=="1"&&(o=!0);delete t.additionalInfo[n.AdditionalInfoKeys.diagnostics]}var f=this.getUrlParams(t.prefix,t.cvid,t.additionalInfo);var h=this.getFlights(t.additionalInfo);h&&(f[n.UrlParameters.setFlight]=h);if(this.addAdditionalInfoUrlParameters&&t.additionalInfo)for(var a in t.additionalInfo)f[encodeURIComponent(a)]=encodeURIComponent(t.additionalInfo[a]);o&&(f[n.UrlParameters.messageRequestId]=t.requestId);var l=this.getFinalUrl(t.action,f);if(l){var u=xhr();u.open(r,l,!0);u.setRequestHeader(n.HeaderNames.authorization,n.HeaderValues.bearer+" "+t.token);u.setRequestHeader(n.HeaderNames.accept,n.HeaderValues.applicationJson);var c=!0;if(t.headers)for(var e in t.headers){var s=e.toUpperCase();s==n.HeaderNames.contentType.toUpperCase()&&(c=!1);s!==n.HeaderNames.accept.toUpperCase()&&s!==n.HeaderNames.authorization.toUpperCase()&&u.setRequestHeader(e,t.headers[e])}r=="POST"&&c&&u.setRequestHeader(n.HeaderNames.contentType,n.HeaderValues.applicationJson);u.onreadystatechange=function(){if(u.readyState===4){u.onreadystatechange=function(){};var c=u.responseText;if(i){var r=u.getAllResponseHeaders();var h={};if(r&&r.trim()){var l=r.trim().split(/[\r\n]+/);l.forEach(function(n){var t=n.split(": ");var r=t.shift();var i=t.join(": ");h[r]=i})}var f=void 0;var e=n.UrlParameters.messageRequestId+"="+t.requestId;var s=o&&performance?getDiagnostics():undefined;s&&(f=s.filter(function(n){return n.name.substr(-e.length)===e}));i(u.status,c,h,f)}}};u.withCredentials!=undefined&&(u.withCredentials=!0);r==="POST"?u.send(t.prefix):u.send()}};t.prototype.getUrlParams=function(n,t,i){return{}};t.prototype.getFlights=function(n){var t=[];if(n){for(var i in n)i.indexOf("flight")>-1&&t.push(n[i]);if(t.length>0)return t.join(",")}return null};t.prototype.getFinalUrl=function(t,i){var r=this.getBaseUrl(t);if(i)for(var u in i){var f=r.indexOf(n.UrlBuildingComponents.questionMark)>-1?n.UrlBuildingComponents.ampersand:n.UrlBuildingComponents.questionMark;r+=f+u+n.UrlBuildingComponents.equal+i[u]}return r};return t}();t.DataProvider=i})(t=n.Providers||(n.Providers={}))})(AutoSuggest||(AutoSuggest={}))
var __extends=this&&this.__extends||function(){var n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i])};return function(t,i){n(t,i);function r(){this.constructor=t}t.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}();var AutoSuggest;(function(n){var t;(function(t){var i=function(t){__extends(i,t);function i(n){return t.call(this,n)||this}i.prototype.getBaseUrl=function(t){return t==n.ActionTypes.initV2Action?this.config.initV2Route:this.config.initRoute};i.prototype.getUrlParams=function(t,i,r){var u={};i&&(u[n.UrlParameters.conversationId]=i);return u};return i}(t.DataProvider);t.InitDataProvider=i})(t=n.Providers||(n.Providers={}))})(AutoSuggest||(AutoSuggest={}))
var __extends=this&&this.__extends||function(){var n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i])};return function(t,i){n(t,i);function r(){this.constructor=t}t.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}();var AutoSuggest;(function(n){var t;(function(t){var i=function(t){__extends(i,t);function i(n){return t.call(this,n)||this}i.prototype.getBaseUrl=function(){return this.config.suggestionRoute};i.prototype.getUrlParams=function(t,i,r){var u={};u[n.UrlParameters.query]=encodeURIComponent(t);u[n.UrlParameters.conversationId]=i;return u};return i}(t.DataProvider);t.SuggestionDataProvider=i})(t=n.Providers||(n.Providers={}))})(AutoSuggest||(AutoSuggest={}))
var __extends=this&&this.__extends||function(){var n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i])};return function(t,i){n(t,i);function r(){this.constructor=t}t.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}();var AutoSuggest;(function(n){var t;(function(t){var i=function(t){__extends(i,t);function i(n){return t.call(this,n)||this}i.prototype.getBaseUrl=function(){return this.config.spoQueryRoute};i.prototype.getUrlParams=function(t,i,r){var u={};u[n.UrlParameters.correlationId]=i;return u};return i}(t.DataProvider);t.SpoQueryDataProvider=i})(t=n.Providers||(n.Providers={}))})(AutoSuggest||(AutoSuggest={}))
var __extends=this&&this.__extends||function(){var n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i])};return function(t,i){n(t,i);function r(){this.constructor=t}t.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}();var AutoSuggest;(function(n){var t;(function(n){var t=function(n){__extends(t,n);function t(t){return n.call(this,t)||this}t.prototype.getBaseUrl=function(){return this.config.recommendationsRoute};return t}(n.DataProvider);n.RecommendationsDataProvider=t})(t=n.Providers||(n.Providers={}))})(AutoSuggest||(AutoSuggest={}))
var __extends=this&&this.__extends||function(){var n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i])};return function(t,i){n(t,i);function r(){this.constructor=t}t.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}();var AutoSuggest;(function(n){var t;(function(t){var i=function(t){__extends(i,t);function i(n){return t.call(this,n)||this}i.prototype.getBaseUrl=function(t){return t===n.ActionTypes.getQueryV2Action?this.config.queryV2Route:this.config.queryRoute};return i}(t.DataProvider);t.QueryDataProvider=i})(t=n.Providers||(n.Providers={}))})(AutoSuggest||(AutoSuggest={}))
var __extends=this&&this.__extends||function(){var n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i])};return function(t,i){n(t,i);function r(){this.constructor=t}t.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}();var AutoSuggest;(function(n){var t;(function(n){var t=function(n){__extends(t,n);function t(t){return n.call(this,t)||this}t.prototype.getBaseUrl=function(){return this.config.eventsLoggingRoute};return t}(n.DataProvider);n.LogEventsDataProvider=t})(t=n.Providers||(n.Providers={}))})(AutoSuggest||(AutoSuggest={}))
var __extends=this&&this.__extends||function(){var n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i])};return function(t,i){n(t,i);function r(){this.constructor=t}t.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}();var AutoSuggest;(function(n){var t;(function(n){var t=function(n){__extends(t,n);function t(t){return n.call(this,t)||this}t.prototype.getBaseUrl=function(){return this.config.suggestionRoute};return t}(n.DataProvider);n.PostSuggestionsDataProvider=t})(t=n.Providers||(n.Providers={}))})(AutoSuggest||(AutoSuggest={}))
var __extends=this&&this.__extends||function(){var n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i])};return function(t,i){n(t,i);function r(){this.constructor=t}t.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}();var AutoSuggest;(function(n){var t;(function(n){var t=function(n){__extends(t,n);function t(t){return n.call(this,t)||this}t.prototype.getBaseUrl=function(){return this.config.asyncResolutionRoute};return t}(n.DataProvider);n.AsyncResolutionDataProvider=t})(t=n.Providers||(n.Providers={}))})(AutoSuggest||(AutoSuggest={}))
var __extends=this&&this.__extends||function(){var n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i])};return function(t,i){n(t,i);function r(){this.constructor=t}t.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}();var AutoSuggest;(function(n){var t;(function(n){var t=function(n){__extends(t,n);function t(t){return n.call(this,t)||this}t.prototype.getBaseUrl=function(){return this.config.adaptiveActionsRoute};return t}(n.DataProvider);n.AdaptiveActionsProvider=t})(t=n.Providers||(n.Providers={}))})(AutoSuggest||(AutoSuggest={}))
var __extends=this&&this.__extends||function(){var n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i])};return function(t,i){n(t,i);function r(){this.constructor=t}t.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}();var AutoSuggest;(function(n){var t;(function(n){var t=function(n){__extends(t,n);function t(t){return n.call(this,t)||this}t.prototype.getBaseUrl=function(){return this.config.unfurlRoute};return t}(n.DataProvider);n.UnfurlProvider=t})(t=n.Providers||(n.Providers={}))})(AutoSuggest||(AutoSuggest={}))
var __extends=this&&this.__extends||function(){var n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i])};return function(t,i){n(t,i);function r(){this.constructor=t}t.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}();var AutoSuggest;(function(n){var t;(function(n){var t=function(n){__extends(t,n);function t(t){return n.call(this,t)||this}t.prototype.getBaseUrl=function(){return this.config.userConfigRoute};return t}(n.DataProvider);n.UserConfigProvider=t})(t=n.Providers||(n.Providers={}))})(AutoSuggest||(AutoSuggest={}))
var __extends=this&&this.__extends||function(){var n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i])};return function(t,i){n(t,i);function r(){this.constructor=t}t.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}();var AutoSuggest;(function(n){var t;(function(n){var t=function(n){__extends(t,n);function t(t){return n.call(this,t)||this}t.prototype.getBaseUrl=function(){return this.config.botInvokeRoute};return t}(n.DataProvider);n.BotInvokeProvider=t})(t=n.Providers||(n.Providers={}))})(AutoSuggest||(AutoSuggest={}))
var __extends=this&&this.__extends||function(){var n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i])};return function(t,i){n(t,i);function r(){this.constructor=t}t.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}();var AutoSuggest;(function(n){var t;(function(n){var t=function(n){__extends(t,n);function t(t){return n.call(this,t)||this}t.prototype.getBaseUrl=function(){return this.config.microserviceQueryV2Route};return t}(n.DataProvider);n.MicroserviceQueryProvider=t})(t=n.Providers||(n.Providers={}))})(AutoSuggest||(AutoSuggest={}))
var __extends=this&&this.__extends||function(){var n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i])};return function(t,i){n(t,i);function r(){this.constructor=t}t.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}();var AutoSuggest;(function(n){var t;(function(n){var t=function(n){__extends(t,n);function t(t){return n.call(this,t)||this}t.prototype.getBaseUrl=function(){return this.config.microserviceQueryV3Route};return t}(n.DataProvider);n.MicroserviceQueryProviderV3=t})(t=n.Providers||(n.Providers={}))})(AutoSuggest||(AutoSuggest={}))
var AutoSuggest;(function(n){var t;(function(n){var t;(function(n){parse=function(n){return i(n)};buildMessage=function(n,i,r,u,f,e){return t(n,i,r,u,f,e)};function i(n){return n?JSON.parse(n):null}n.parseMessage=i;function t(n,t,i,r,u,f){var e={requestId:n,prefix:t,statusCode:i,qfResponse:r,responseHeaders:JSON.stringify(u)};f&&(e.diagnostics=JSON.stringify(f));return JSON.stringify(e)}n.buildResponseMessage=t})(t=n.V2||(n.V2={}))})(t=n.Handlers||(n.Handlers={}))})(AutoSuggest||(AutoSuggest={}))
var AutoSuggest;(function(n){var t;(function(t){var i=function(){function t(t){this.initProvider=new n.Providers.InitDataProvider(t);this.suggestionProvider=new n.Providers.SuggestionDataProvider(t);this.eventsProvider=new n.Providers.LogEventsDataProvider(t);this.spoQueryProvider=new n.Providers.SpoQueryDataProvider(t);this.recommendationsProvider=new n.Providers.RecommendationsDataProvider(t);this.queryProvider=new n.Providers.QueryDataProvider(t);this.postSuggestionsProvider=new n.Providers.PostSuggestionsDataProvider(t);this.asyncResolutionProvider=new n.Providers.AsyncResolutionDataProvider(t);this.adaptiveActionsProvider=new n.Providers.AdaptiveActionsProvider(t);this.unfurlProvider=new n.Providers.UnfurlProvider(t);this.userConfigProvider=new n.Providers.UserConfigProvider(t);this.botInvokeProvider=new n.Providers.BotInvokeProvider(t);this.microserviceQueryProvider=new n.Providers.MicroserviceQueryProvider(t);this.microserviceQueryProviderV3=new n.Providers.MicroserviceQueryProviderV3(t)}t.prototype.handle=function(t,i){var r=parse(t);if(!r)return!1;var u=function(n,t,u,f){var e=buildMessage(r.requestId,r.prefix,n,t,u,f);i(e)};r.action===n.ActionTypes.getSuggestionsAction?this.suggestionProvider.fetchUrl(r,u):r.action===n.ActionTypes.initAction||r.action===n.ActionTypes.initV2Action?this.initProvider.fetchUrl(r,u):r.action===n.ActionTypes.logEventsAction?this.eventsProvider.fetchUrl(r,u,"POST"):r.action===n.ActionTypes.getSpoQueryAction?this.spoQueryProvider.fetchUrl(r,u,"POST"):r.action===n.ActionTypes.getRecommendationsAction?this.recommendationsProvider.fetchUrl(r,u,"POST"):r.action===n.ActionTypes.getQueryAction||r.action==n.ActionTypes.getQueryV2Action?this.queryProvider.fetchUrl(r,u,"POST"):r.action===n.ActionTypes.postSuggestionsAction?this.postSuggestionsProvider.fetchUrl(r,u,"POST"):r.action===n.ActionTypes.getAsyncResolutionAction?this.asyncResolutionProvider.fetchUrl(r,u,"POST"):r.action===n.ActionTypes.adaptiveActions?this.adaptiveActionsProvider.fetchUrl(r,u,"POST"):r.action===n.ActionTypes.unfurl?this.unfurlProvider.fetchUrl(r,u,"POST"):r.action===n.ActionTypes.userConfig?this.userConfigProvider.fetchUrl(r,u,"POST"):r.action===n.ActionTypes.botInvoke?this.botInvokeProvider.fetchUrl(r,u,"POST"):r.action===n.ActionTypes.getMicroserviceQueryV2Action?this.microserviceQueryProvider.fetchUrl(r,u,"POST"):r.action===n.ActionTypes.getMicroserviceQueryV3Action&&this.microserviceQueryProviderV3.fetchUrl(r,u,"POST");return!0};return t}();t.MessageHandler=i})(t=n.Handlers||(n.Handlers={}))})(AutoSuggest||(AutoSuggest={}));var config={initRoute:"https://outlook.office365.com/search/api/v1/init",initV2Route:"https://outlook.office365.com/search/api/v2/init",suggestionRoute:"https://outlook.office365.com/search/api/v1/suggestions",eventsLoggingRoute:"https://outlook.office365.com/search/api/v1/events",spoQueryRoute:"https://outlook.office365.com/search/api/v1/sharepoint/spoquery",recommendationsRoute:"https://outlook.office365.com/search/api/v1/recommendations",queryRoute:"https://outlook.office365.com/search/api/v1/query",queryV2Route:"https://outlook.office365.com/search/api/v2/query",asyncResolutionRoute:"https://outlook.office365.com/search/api/v1/resolveEntitiesAsync",adaptiveActionsRoute:"https://outlook.office365.com/search/api/v1/adaptiveactions",unfurlRoute:"https://outlook.office365.com/search/api/v1/unfurl",userConfigRoute:"https://outlook.office365.com/search/api/v1/userConfig",botInvokeRoute:"https://outlook.office365.com/search/api/v1/botInvoke",microserviceQueryV2Route:"https://outlook.office365.com/searchservice/api/v2/query",microserviceQueryV3Route:"https://outlook.office365.com/searchservice/api/v3/query"};var messageHandler=new AutoSuggest.Handlers.MessageHandler(config);window.addEventListener("message",function(n){return messageHandler.handle(n.data,function(t){return n.source.postMessage(t,"*")})},!1);parent&&parent.postMessage("loaded","*")
var AutoSuggest;(function(n){var u=function(){function n(){}n.hitHighlight="textdecorations";n.query="query";n.conversationId="cvid";n.correlationId="correlationId";n.setFlight="setflight";n.messageRequestId="msgRequestId";return n}();n.UrlParameters=u;var f=function(){function n(){}n.diagnostics="diagnostics";return n}();n.AdditionalInfoKeys=f;var e=function(){function n(){}n.equal="=";n.questionMark="?";n.ampersand="&";return n}();n.UrlBuildingComponents=e;var t=function(){function n(){}n.initAction="init";n.initV2Action="initv2";n.getSuggestionsAction="getsuggestions";n.postSuggestionsAction="postsuggestions";n.logEventsAction="logevents";n.getSpoQueryAction="spoquery";n.getRecommendationsAction="getrecommendations";n.getQueryAction="query";n.getQueryV2Action="queryv2";n.getAsyncResolutionAction="resolveEntitiesAsync";n.adaptiveActions="adaptiveactions";n.unfurl="unfurl";n.userConfig="userConfig";n.botInvoke="botInvoke";n.getMicroserviceQueryV2Action="microservicequeryv2";n.getMicroserviceQueryV3Action="microservicequeryv3";return n}();n.ActionTypes=t;var i=function(){function n(){}n.authorization="Authorization";n.accept="Accept";n.contentType="Content-Type";n.xTargetServer="X-TargetServer";n.xCalculatedBeTarget="X-CalculatedBETarget";return n}();n.HeaderNames=i;var r=function(){function n(){}n.bearer="Bearer";n.applicationJson="application/json";return n}();n.HeaderValues=r})(AutoSuggest||(AutoSuggest={}))
</script></head><body></body></html>