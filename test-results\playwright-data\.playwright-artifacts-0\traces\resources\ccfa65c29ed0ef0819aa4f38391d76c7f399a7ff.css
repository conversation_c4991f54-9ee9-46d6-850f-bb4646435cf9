* {
	margin:0px;
	padding:0px;
}
html, body
{
    height:100%;
    width:100%;
    background-color:#ffffff;
    color:#000000;
    font-weight:normal;
    font-family:"Segoe UI" , "Segoe" , "SegoeUI-Regular-final", Tahoma, Helvetica, Arial, sans-serif;
    min-width:500px;
    -ms-overflow-style:-ms-autohiding-scrollbar;
}

body
{
    font-size:0.9em;
}

#noScript { margin:16px; color:Black; }

:lang(en-GB){quotes:'\2018' '\2019' '\201C' '\201D';}
:lang(zh){font-family:微软雅黑;}

@-ms-viewport { width: device-width; }
@-moz-viewport { width: device-width; }
@-o-viewport { width: device-width; }
@-webkit-viewport { width: device-width;  }
@viewport { width: device-width; }

/* Theme layout styles */

#fullPage, #brandingWrapper
{
    width:100%;
    height:100%;
    background-color:inherit;
}
#brandingWrapper
{
    background-color: white;
}
#branding
{       
    /* A background image will be added to the #branding element at run-time once the illustration image is configured in the theme. 
       Recommended image dimensions: 1420x1200 pixels, JPG or PNG, 200 kB average, 500 kB maximum. */
    min-height:120px;
    margin-right:500px; margin-left:0px;
    background-color:inherit;
    background-repeat: no-repeat;
    background-size:cover;
    -webkit-background-size:cover;
    -moz-background-size:cover;
    -o-background-size:cover;
}
#contentWrapper
{
    position:relative;
    width:500px;    
    height:100%;
    overflow:auto;
    background-color:#ffffff; /* for IE7 */
    margin-left:-500px; margin-right:0px; 
}
#content
{
    height: auto !important;
    margin:0 auto -55px auto;
    padding:0px 150px 0px 50px;
}
#header
{
    color:rgb(0, 51, 141);
    font-size:2em;
    font-weight:bold;
    font-family:Arial,"Segoe UI Light" , "Segoe" , "SegoeUI-Light-final", Tahoma, Helvetica, sans-serif;
    padding-top: 90px;
    margin-bottom:60px;
    min-height:100px;
    overflow:hidden;
}
#header img
{
    /* Logo image recommended dimension: 60x60 (square) or 350X35 (elongated), 4 kB average, 10 kB maximum. Transparent PNG strongly recommended. */
    width:auto;
    height:auto;
    max-width:100%;
}
#workArea, #header
{
    word-wrap:break-word;
    width:350px;
}
#workArea
{
    margin-bottom:90px;
}
#footerPlaceholder
{
    height:40px;
}
#footer
{
    margin: 0;
    bottom: 8px;
    left: 16px;
    right: 16px;
    background-color: white;
    font-family:Arial;
    height:auto;
    padding:0;
    position:fixed;
    color:#B2B2B2;
    font-size:0.78em;

}
#footerLinks
{
    float:none;
    padding-top:10px;
    color:#B2B2B2;
    font-family:arial;
}
#footerLinks a.hover
{
    text-decoration: underline;
}

#copyright {color:#696969; display:none;}
.pageLink { color:#000000; padding-left:16px; }

/* Common content styles */

.clear {clear:both;}
.float { float:left; } 
.floatReverse { float:right; } 
.indent { margin-left:16px; } 
.indentNonCollapsible { padding-left:16px; }
.hidden {display:none;}
.notHidden {display:inherit;}
.error { color:#c85305; }
.actionLink { margin-bottom:8px; display:block; }
a
{
    color:#2672ec;
    text-decoration:none;
    background-color:transparent;
}
ul { list-style-type: disc; }
ul,ol,dd { padding: 0 0 0 16px; }
h1,h2,h3,h4,h5,label { margin-bottom: 8px; }
.submitMargin { margin-top:38px; margin-bottom:30px; }
.topFieldMargin { margin-top:8px; }
.fieldMargin { margin-bottom:8px; }
.groupMargin { margin-bottom:30px; } 
.sectionMargin { margin-bottom:64px; }
.block { display: block; }
.autoWidth { width:auto; }
.fullWidth { width:342px; }
.fullWidthIndent { width:326px; }
.smallTopSpacing { margin-top:15px; }
.mediumTopSpacing { margin-top:25px; }
.largeTopSpacing { margin-top:35px; }
.smallBottomSpacing { margin-bottom:5px; }
.mediumBottomSpacing { margin-bottom:15px; }
.largeBottomSpacing  { margin-bottom:25px; }
input
{
    max-width:100%; 
    font-family:inherit;
    margin-bottom:8px;
}
input[type="radio"], input[type="checkbox"] {
    vertical-align:middle;
    margin-bottom: 0px;
}
span.submit, input[type="submit"]
{
    border: solid 1px #1647a3;
    background-color: #1647a3;
    min-width:80px;
    width:auto;
    height:30px;
    padding:4px 20px 6px 20px;
    transition:background 0s;
    color:rgb(255, 255, 255);
    cursor:pointer;
    margin-bottom:8px;
    
    -ms-user-select:none;
    -moz-transition:background 0s;
    -webkit-transition:background 0s;
    -o-transition:background 0s;
    -webkit-touch-callout:none;
    -webkit-user-select:none;
    -khtml-user-select:none;
    -moz-user-select: none;
    -o-user-select: none;
    user-select:none;
}
input[type="submit"]:hover,span.submit:hover
{
    Background-color: rgb(194, 224, 251);
    color: #1647a3;
    border:solid 1px #1647a3;
}
input.text{
    height:28px;
    padding:0px 3px 0px 3px ;
    border:solid 1px #BABABA;
    font-size:1.0em;
}
input.text:focus
{
  border: 1px solid #6B6B6B;
}
select
{
    height:28px;
    min-width:60px;
    max-width:100%; 
    margin-bottom:8px;

    white-space:nowrap;
    overflow:hidden;
    box-shadow:none;
    padding:2px;
    font-family:inherit;
}
h1, .giantText
{
   font-size:2.0em; 
   font-weight:lighter;
}          
h2, .bigText
{
   font-size:1.33em;
   font-weight:lighter;
}
h3, .normalText
{
    font-size:1.0em;
    font-weight:normal;
}
h4, .smallText
{
    font-size:0.9em;
    font-weight:normal;
}
h5, .tinyText
{
    font-size:0.8em;
    font-weight:normal;
}
.hint
{
    color:#999999;
}
.emphasis
{
    font-weight:700;
    color:#2F2F2F;
} 
.smallIcon
{
    height:20px;
    padding-right:12px;
    vertical-align:middle;
}
.largeIcon
{
    height:48px;
    /* width:48px; */
    vertical-align:middle;
}
.largeTextNoWrap
{
    height:48px;
    display:table-cell; /* needed when in float*/
    vertical-align:middle;
    white-space:nowrap;
    font-size:1.2em;
}
.idp
{
    height:48px;
    clear:both;
    padding:8px;
    overflow:hidden;
}
.idp:hover 
{
    background-color:#cccccc;
}
.idpDescription
{
    width:80%;
}

/* Form factor: intermediate height layout. Reduce space of the header. */
@media only screen and (max-height: 820px) {
    #header {
        padding-top: 40px;
        min-height:0px;
        overflow: hidden;
    }

    #workArea
    {
        margin-bottom:60px; 
    }
}

/* Form factor: intermediate height layout. Reduce space of the header. */
@media only screen and (max-height: 500px) {
    #header {
        padding-top: 30px;
        margin-bottom: 30px;
    }
    #workArea{
        margin-bottom:40px; 
    }
}

/* Form factor: intermediate layout (WAB in non-snapped view falls in here) */
@media only screen and (max-width: 600px) {
    html, body {
        min-width: 260px;
    }

    #brandingWrapper {
        display: none;
    }

    #contentWrapper {
        float: none;
        width: 100%;
        margin: 0px auto;
    }

    #content, #header {
        width: 400px;
        padding-left: 0px;
        padding-right: 0px;
        margin-left: auto;
        margin-right: auto;
    }

    #footer {
        position: relative;
        width: 400px;
        padding-left: 0px;
        padding-right: 0px;
        margin-left: auto;
        margin-right: auto;
    }

    #footerLinks {
        width:auto;
        margin-right:16px;
    }


    #workArea {
        width: 100%;
    }

    .fullWidth {
        width: 392px;
    }

    .fullWidthIndent {
        width: 376px;
    }
}

@media only screen and (max-width: 450px) {
    body {
        font-size: 0.8em;
    }

    #content {
        width:auto;
        margin-right:33px;
        margin-left:25px;
    }

    #header {
        width: auto;
    }

    span.submit, input[type="submit"] {
        font-size: 0.9em;
    }

    .fullWidth
    {
        width:100%;
        margin-left:auto;
        margin-right:auto;
    }

    .fullWidthIndent {
        width: 85%;
    }

    .idpDescription
    {
        width:70%;
    }
}

/* Form factor: snapped WAB (for WAB to work in snapped view, the content wrapper width has to be set to 260px) */
@media only screen and (max-width:280px)
{
    #contentWrapper
    {
        width:260px;
    }
    .idpDescription
    {
        max-width:160px;
        min-width:100px;
    }
}