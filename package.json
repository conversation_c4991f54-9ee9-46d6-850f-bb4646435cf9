{"name": "playwright_automation_framework", "version": "4.0.0", "description": "World-class Playwright test automation framework with BDD/Cucumber - The most advanced, intelligent, and user-friendly testing solution for any web application", "main": "dist/main.js", "keywords": ["playwright", "automation", "testing", "bdd", "cucumber", "framework", "typescript", "e2e"], "author": "Playwright Automation Framework Team", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "scripts": {"pretest": "npm run build", "build": "tsc", "build:watch": "tsc --watch", "test": "node dist/core/test-runner.js", "test:dynamic": "node dist/core/test-runner.js", "test:tags": "npm run test -- --tags", "test:feature": "npm run test -- --feature", "test:scenario": "npm run test -- --scenario", "test:data-driven": "npm run test -- --tags @data-driven", "test:accessibility": "npm run test -- --tags @accessibility", "test:visual": "cross-env VISUAL_TESTING_ENABLED=true npm run test -- --tags @visual", "test:smoke": "npm run test -- --tags @smoke", "test:regression": "npm run test -- --tags @regression", "test:e2e": "npm run test -- --tags @e2e", "test:api": "npm run test -- --tags @api", "test:debug": "cross-env DEBUG_MODE=true HEADLESS=false npm run test", "test:headful": "cross-env HEADLESS=false npm run test", "test:headed": "cross-env HEADLESS=false npm run test", "test:chrome": "cross-env BROWSER=chrome npm run test", "test:firefox": "cross-env BROWSER=firefox npm run test", "test:edge": "cross-env BROWSER=edge npm run test", "test:webkit": "cross-env BROWSER=webkit npm run test", "test:playwright": "npx playwright test", "test:with-playwright-report": "npm run test:playwright", "test:spo-with-report": "cross-env TEST_TAGS=@spo npm run test:playwright", "test:spo-headed-with-report": "cross-env TEST_TAGS=@spo HEADLESS=false npm run test:playwright", "test:spo-with-trace-video": "cross-env TEST_TAGS=@spo HEADLESS=false VIDEO_RECORDING=true TRACE_ENABLED=true npm run test:playwright", "test:spo-private": "cross-env TEST_TAGS=@spo HEADLESS=false PRIVATE_SESSION=true npm run test:playwright", "test:spo-private-with-trace": "cross-env TEST_TAGS=@spo HEADLESS=false PRIVATE_SESSION=true VIDEO_RECORDING=true TRACE_ENABLED=true npm run test:playwright", "test:todomvc-with-report": "cross-env TEST_TAGS=@todomvc npm run test:playwright", "test:todomvc-with-trace-video": "cross-env TEST_TAGS=@todomvc HEADLESS=false VIDEO_RECORDING=true TRACE_ENABLED=true npm run test:playwright", "test:todomvc-private": "cross-env TEST_TAGS=@todomvc HEADLESS=false PRIVATE_SESSION=true npm run test:playwright", "test:all-browsers": "npm run test:chrome && npm run test:firefox && npm run test:edge", "test:parallel": "npm run test -- --parallel 4", "test:ci": "cross-env CI=true npm run test -- --parallel 4 --format json", "report:generate": "node dist/reports/report-generator.js", "report:open": "npm run report:generate && node dist/utils/open-report.js", "report:serve": "node dist/reports/report-server.js", "report:playwright": "npx playwright show-report", "report:playwright-open": "npx playwright show-report --host 0.0.0.0 --port 9323", "trace:show": "npx playwright show-trace", "health:check": "node dist/utils/health-check.js", "locator:pick": "node dist/utils/locator-picker.js", "locator:generate": "node dist/utils/locator-generator.js", "auth:setup": "node dist/auth/auth-setup.js", "auth:clear": "node dist/auth/auth-clear.js", "baseline:update": "cross-env UPDATE_BASELINES=true npm run test:visual", "clean": "rimraf dist test-results logs .nyc_output coverage", "clean:all": "npm run clean && rimraf node_modules package-lock.json", "lint": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "validate": "npm run lint && npm run build && npm run health:check", "install:browsers": "npx playwright install", "install:all": "npm install && npm run install:browsers", "docs:serve": "node dist/docs/docs-server.js", "docs:generate": "node dist/docs/docs-generator.js", "framework:info": "node dist/utils/framework-info.js", "framework:validate": "node dist/utils/framework-validator.js", "demo:todomvc": "npm run test -- --tags @todomvc", "demo:todomvc:quick": "npm run test -- --tags @todomvc --headless", "demo:todomvc:headed": "npm run test:headed -- --tags @todomvc", "demo:todomvc:accessibility": "npm run test -- --tags @todomvc and @accessibility", "demo:todomvc:visual": "cross-env VIDEO_RECORDING=true npm run test -- --tags @todomvc", "demo:todomvc:performance": "npm run test -- --tags @todomvc and @performance", "demo:todomvc:comprehensive": "cross-env VIDEO_RECORDING=true npm run test -- --tags @todomvc", "test:todomvc": "npm run test -- --tags @todomvc", "test:todomvc:smoke": "npm run test -- --tags @todomvc and @smoke", "test:todomvc:forms": "npm run test -- --tags @todomvc and @forms", "test:spo": "node run-dynamic-tests.js spo", "test:spo:headed": "node run-dynamic-tests.js spo --headed", "test:spo:video": "node run-dynamic-tests.js spo --video", "demo:comprehensive": "npm run demo:todomvc", "demo:showcase": "npm run demo:todomvc", "demo:simple": "npm run test -- --tags @demo"}, "repository": {"type": "git", "url": "https://github.com/your-org/playwright-automation-framework.git"}, "bugs": {"url": "https://github.com/your-org/playwright-automation-framework/issues"}, "homepage": "https://github.com/your-org/playwright-automation-framework#readme", "dependencies": {"@axe-core/playwright": "^4.10.1", "@cucumber/cucumber": "^11.2.0", "@cucumber/pretty-formatter": "^1.0.1", "@faker-js/faker": "^9.7.0", "@playwright/test": "^1.52.0", "@types/node": "^22.15.17", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "express": "^4.21.2", "fs-extra": "^11.2.0", "lodash": "^4.17.21", "moment": "^2.30.1", "open": "^10.1.0", "pixelmatch": "^5.3.0", "pngjs": "^7.0.0", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "typescript": "^5.8.3", "uuid": "^11.0.4", "winston": "^3.17.0", "yargs": "^17.7.2"}, "devDependencies": {"@types/express": "^5.0.0", "@types/fs-extra": "^11.0.4", "@types/lodash": "^4.17.13", "@types/pixelmatch": "^5.2.7", "@types/pngjs": "^6.0.4", "@types/uuid": "^10.0.0", "@types/yargs": "^17.0.33", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "eslint": "^9.18.0", "prettier": "^3.4.2"}}