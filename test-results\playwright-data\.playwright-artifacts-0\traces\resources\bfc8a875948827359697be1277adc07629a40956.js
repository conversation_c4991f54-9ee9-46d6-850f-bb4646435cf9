"use strict";(self.webpackJsonp_21e471d3_ec7b_4ee6_b15d_659db7eb969c=self.webpackJsonp_21e471d3_ec7b_4ee6_b15d_659db7eb969c||[]).push([["search-instrumentation"],{CVAo:function(e,t){t.A=function(){var e=function(){return Math.floor(65536*(1+Math.random())).toString(16).substring(1)};return e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()}},B6uc:function(e,t,r){r.r(t),r.d(t,{default:function(){return c}});var a=r("CVAo"),n=function(){return n=Object.assign||function(e){for(var t,r=1,a=arguments.length;r<a;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},n.apply(this,arguments)},o=["markAppBoot","markHeaderBoot","markSbInteractive","markSbReady","markZeroQueryReady","markSearchLayoutMount","markSearchLayoutRenderStart"],i={markAppBoot:[],markHeaderBoot:["markAppBoot"],markSbInteractive:["markHeaderBoot"],markSbReady:["markSbInteractive"],markZeroQueryReady:["markSbReady"],SbQuerySubmitted:["markSbReady"],SerpRouteChange:[],SerpStartQuery:["SbQuerySubmitted"],SerpFetchStart:["SbQuerySubmitted","SerpStartQuery"],SerpAnswerFetchDone:["SerpFetchStart"],SerpFetchDone:["SerpFetchStart"],SerpPaintStart:["SerpDataReady"],SerpAnswersRenderDone:["markAppBoot","SbQuerySubmitted","SerpRouteChange","SerpFetchStart","SerpAnswerFetchDone"],SerpDataReady:["SerpFetchStart","SerpFetchDone"],SerpRenderDone:["markAppBoot","SbQuerySubmitted","markSearchLayoutMount","markSearchLayoutRenderStart","SerpPrefetchStart","SerpPrefetchDone","SerpConfigStart","SerpConfigDone","SerpRouteChange","SerpStartQuery","SerpFetchStart","SerpDataReady","SerpPaintStart","SerpFetchDone","SerpFullPageLoad","SerpInPlaceNavigation","RouterHistoryChange"],ProcessVerticalsConfigStart:[],FetchVerticalsConfigStart:[],FetchVerticalsConfigDone:[],VerticalsRegisterDone:[],VerticalsRenderDone:["markAppBoot","SbQuerySubmitted","SerpRouteChange","RouterHistoryChange","SerpPrefetchStart","SerpPrefetchDone","SerpConfigStart","SerpConfigDone","ProcessVerticalsConfigStart","FetchVerticalsConfigStart","FetchVerticalsConfigDone","VerticalsRegisterDone"],markSearchLayoutMount:["SbQuerySubmitted"],markSearchLayoutRenderStart:["SbQuerySubmitted","markSearchLayoutMount"],SerpPrefetchStart:[],SerpPrefetchDone:["SerpPrefetchStart"],SerpConfigStart:[],SerpConfigDone:["SerpConfigStart"],SerpFullPageLoad:[],SerpInPlaceNavigation:[],RouterHistoryChange:[],SetRefinersFromDeepLinkQueryStart:[],SetRefinersFromDeepLinkQueryDone:[],CreateCustomAggregationsStart:[],CreateCustomAggregationsDone:[],UpdateAvailableRefinersStart:[],UpdateAvailableRefinersDone:[],UpdateRefinersStart:[],ClearRefinersStart:[],SerpOldRefinerButtonClick:[],SerpRefinersCheck:["UpdateAvailableRefinersStart"],SerpRefinersRenderDone:["SerpConfigStart","SerpConfigDone","SerpFetchStart","SerpRouteChange","SerpDataReady","SerpFetchDone","UpdateRefinersStart","ClearRefinersStart","SetRefinersFromDeepLinkQueryStart","SetRefinersFromDeepLinkQueryDone","CreateCustomAggregationsStart","CreateCustomAggregationsDone","UpdateAvailableRefinersStart","UpdateAvailableRefinersDone","SerpOldRefinerButtonClick"],ContactDataFetchStart:[],ContactDataFetchEnd:["ContactDataFetchStart"],OrganizationChartDataFetchStart:[],OrganizationChartDataFetchEnd:["OrganizationChartDataFetchStart"],AnswersControlRequested:["markAppBoot","SbQuerySubmitted","SerpRouteChange","RouterHistoryChange"],OverviewContactFetchStart:[],OverviewContactFetchEnd:["OverviewContactFetchStart"],OverviewContentFetchStart:[],OverviewContentFetchEnd:["OverviewContentFetchStart"],OverviewMessageFetchStart:[],OverviewMessageFetchEnd:["OverviewMessageFetchStart"],OverviewTopicFetchStart:[],OverviewTopicFetchEnd:["OverviewTopicFetchStart"],OverviewOrgFetchStart:[],OverviewOrgFetchEnd:["OverviewOrgFetchStart"]},S=function(e){var t,r,n=function(e){var t={},r=void 0,n=!1,o=function(e){var n;return"serp"===e&&r?(n=r,r=void 0):n=(0,a.A)(),t[e]=n,"early-serp"===e&&(r=n,t.serp=n),n};return{conversationId:e||(0,a.A)(),creationTime:Date.now(),getSubmittedState:function(){return n},submit:function(){n=!0},createLogicalSearchId:o,getLogicalSearchId:function(e){return t[e]||o(e)},clearLogicalSearchId:function(e){delete t[e],"early-serp"===e&&(r=void 0)}}};(null==e?void 0:e.lastSubmittedConversationId)&&(r=n(e.lastSubmittedConversationId)).submit();var o=function(e){t&&t.getSubmittedState()&&(r=t);var a=n(e);return t=a,a};return{startConversation:o,getActiveConversation:function(){return!t||t.getSubmittedState()?o():t},getLastSubmittedConversation:function(){if(t&&t.getSubmittedState())return t;if(!r){var e=n();e.submit(),r=e}return r}}};function c(e,t){var r,a=e,c={},u=o.reduce((function(e,t,r){return e[t]=function(){if(c[t]||(c[t]=!0,a.perf.mark(t)),r)try{a.perf.measure("".concat(o[r-1],"-").concat(t),o[r-1],t)}catch(e){}},e}),{}),p={};return n(n(n({updateStartTimeTracker:function(e,t){p[t]=e.startTime}},(r=[],{register:function(e){return"".concat(r.push(e)-1)},unregister:function(e){r[parseInt(e,10)]=0},dispatch:function(e){r.forEach((function(t){return t&&t(e)}))}})),u),{conversationManager:S(t),props:a,setProps:function(e){return this.props=a=n(n({},this.props),e),this.dispatch(e),a},mark:function(e){var t=[];a.perf.mark(e);for(var r=0,n=i[e]||[];r<n.length;r++){var o=n[r];try{var S="".concat(o,"-").concat(e);a.perf.measure(S,o,e);var c=a.perf.getEntriesByName(S).pop();c&&c.startTime>(p[e]||0)&&t.push(c)}catch(e){}}if(t.length>0){var u=Math.max.apply(Math,t.map((function(e){return e.startTime})));p[e]=u}return t}})}}}]);