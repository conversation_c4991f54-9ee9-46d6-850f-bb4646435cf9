self._perfMarks = {};
self._markPerfStage=function(key) {if(self.performance && typeof self.performance.now === 'function'){self._perfMarks[key]=self.performance.now();} else{self._perfMarks[key]=Date.now();} if (self.performance && typeof self.performance.mark === 'function') {self.performance.mark(key);}};
(typeof self._markPerfStage === 'function' && self._markPerfStage('importScriptsStart'));
self._cdnBaseUrl = 'https://res-1.public.onecdn.static.microsoft/files/odsp-web-prod_2025-05-23.004/';
importScripts('https://res-1.public.onecdn.static.microsoft/files/odsp-web-prod_2025-05-23.004/spwebworker.js');
self._wwKillSwitches = {};
(typeof self._markPerfStage === 'function' && self._markPerfStage('importScriptsEnd'));
