[{"time": "2025-06-05T06:26:19.739Z", "iKey": "0b456e85-ed56-4af9-9d42-c8421de22c78", "name": "Microsoft.ApplicationInsights.0b456e85ed564af99d42c8421de22c78.Pageview", "tags": {"ai.user.id": "MtaOO5q16Yb1YL9/ySlfMT", "ai.session.id": "IjZaK//Q7xUrWPaQK0qk5J", "ai.device.id": "browser", "ai.device.type": "Browser", "ai.operation.name": "/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx", "ai.operation.id": "d44606986f9c4951963faacc6dec2fa9", "ai.internal.sdkVersion": "javascript:3.3.8", "ai.internal.snippet": "-"}, "data": {"baseType": "PageviewData", "baseData": {"ver": 2, "name": "Results", "url": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx?q=kpmg&datasource=intranet&origin=portal&caller=https%3A%2F%2Fspo-global.kpmg.com%2Fsites%2FGO-oi-bus-kpmgfinddeventerprisesearch&premium=false", "duration": "00:00:00.000", "properties": {"refUri": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx?q=kpmg&datasource=intranet&origin=portal&caller=https%3A%2F%2Fspo-global.kpmg.com%2Fsites%2FGO-oi-bus-kpmgfinddeventerprisesearch", "KPMG Source": "OneIntranet", "KPMG HubSiteId": "36072d22-c34a-430f-96a5-6f4e3e097513", "OriginSiteUrl": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch", "OriginFullSiteUrl": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch"}, "measurements": {"duration": 0}, "id": "d44606986f9c4951963faacc6dec2fa9"}}}]