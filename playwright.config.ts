import { defineConfig, devices } from '@playwright/test';

/**
 * <PERSON>wright Configuration for Cucumber Integration
 *
 * This configuration enables <PERSON><PERSON>'s native HTML reporter while running Cucumber tests.
 * It provides the best of both worlds: Cucumber's BDD syntax with <PERSON><PERSON>'s powerful reporting.
 */

// Helper function to get browser device configuration
function getBrowserDevice(): string {
  const browser = process.env.BROWSER || 'chromium';
  switch (browser.toLowerCase()) {
    case 'firefox':
      return 'Desktop Firefox';
    case 'webkit':
    case 'safari':
      return 'Desktop Safari';
    case 'edge':
    case 'chrome':
    case 'chromium':
    default:
      return 'Desktop Chrome';
  }
}

// Helper function to map browser names to Playwright browser names
function getPlaywrightBrowserName(): 'chromium' | 'firefox' | 'webkit' {
  const browser = process.env.BROWSER || 'chromium';
  switch (browser.toLowerCase()) {
    case 'firefox':
      return 'firefox';
    case 'webkit':
    case 'safari':
      return 'webkit';
    case 'edge':
    case 'chrome':
    case 'chromium':
    default:
      return 'chromium';
  }
}

/**
 * Get browser-specific private session arguments
 */
function getPrivateSessionArgs(browserName: string): string[] {
  const baseArgs = [
    '--no-first-run',
    '--no-default-browser-check',
    '--disable-background-timer-throttling',
    '--disable-backgrounding-occluded-windows',
    '--disable-renderer-backgrounding',
    '--disable-web-security',
    '--disable-features=VizDisplayCompositor'
  ];

  switch (browserName.toLowerCase()) {
    case 'chromium':
      // Edge uses chromium engine but needs different args
      if (process.env.BROWSER?.toLowerCase() === 'edge') {
        return [
          '--inprivate',              // Edge InPrivate mode - primary flag
          '--new-window',             // Force new window
          '--disable-extensions',     // Disable extensions in private mode
          '--disable-web-security',   // Allow cross-origin requests
          '--no-first-run',
          '--no-default-browser-check',
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding'
        ];
      }
      // Chrome/Chromium
      return [
        '--incognito',              // Chrome incognito mode
        '--disable-extensions',     // Disable extensions in incognito
        ...baseArgs
      ];

    case 'firefox':
      return [
        '--private-window',         // Firefox private browsing
        '--new-instance',           // New Firefox instance
        ...baseArgs
      ];

    case 'webkit':
      return [
        '--private-browsing',       // WebKit private browsing
        ...baseArgs
      ];

    default:
      // Fallback to Chrome-style arguments
      return [
        '--incognito',
        ...baseArgs
      ];
  }
}

export default defineConfig({
  // Test directory
  testDir: './tests',

  // Run tests in files matching this pattern
  testMatch: ['**/*.spec.ts', '**/*.spec.js'],

  // Timeout for each test
  timeout: 5 * 60 * 1000, // 5 minutes for Cucumber test execution

  // Expect timeout
  expect: {
    timeout: 30 * 1000, // 30 seconds
  },

  // Fail the build on CI if you accidentally left test.only in the source code
  forbidOnly: !!process.env.CI,

  // Retry on CI only
  retries: process.env.CI ? 2 : 0,

  // Opt out of parallel tests on CI
  workers: process.env.CI ? 1 : undefined,

  // Reporter configuration - this is where the magic happens!
  reporter: [
    // HTML reporter with embedded screenshots, videos, and traces
    ['html', {
      outputFolder: 'playwright-report',
      open: process.env.CI ? 'never' : 'always',
      host: 'localhost',
      port: 9323
    }],

    // Line reporter for console output
    ['line'],

    // JSON reporter for CI integration
    ['json', { outputFile: 'test-results/playwright-results.json' }],

    // JUnit reporter for CI systems
    ['junit', { outputFile: 'test-results/junit-results.xml' }]
  ],

  // Global test configuration
  use: {
    // Browser to use
    browserName: getPlaywrightBrowserName(),

    // Browser channel (for Edge)
    channel: process.env.BROWSER_CHANNEL || undefined,

    // Headless mode
    headless: process.env.HEADLESS !== 'false',

    // Viewport size
    viewport: {
      width: parseInt(process.env.VIEWPORT_WIDTH || '1280'),
      height: parseInt(process.env.VIEWPORT_HEIGHT || '720')
    },

    // Screenshots
    screenshot: {
      mode: 'only-on-failure',
      fullPage: true
    },

    // Videos
    video: {
      mode: process.env.VIDEO_RECORDING === 'true' ? (process.env.VIDEO_MODE as 'on' | 'off' | 'retain-on-failure' || 'retain-on-failure') : 'off',
      size: {
        width: parseInt(process.env.VIDEO_WIDTH || '1280'),
        height: parseInt(process.env.VIDEO_HEIGHT || '720')
      }
    },

    // Traces - this provides the amazing trace viewer!
    trace: {
      mode: process.env.TRACE_ENABLED === 'true' ? (process.env.TRACE_MODE as 'on' | 'off' | 'retain-on-failure' || 'retain-on-failure') : 'off',
      screenshots: process.env.TRACE_SCREENSHOTS === 'true',
      snapshots: process.env.TRACE_SNAPSHOTS === 'true',
      sources: process.env.TRACE_SOURCES === 'true'
    },

    // Base URL for tests
    baseURL: process.env.BASE_URL || 'https://www.google.com',

    // Ignore HTTPS errors
    ignoreHTTPSErrors: true,

    // Private/Incognito session
    contextOptions: {
      // Clear all browser data for each test
      ...(process.env.PRIVATE_SESSION === 'true' && {
        // Disable storage state to ensure clean session
        storageState: undefined,
        // Clear cookies and local storage
        clearCookies: true,
        clearLocalStorage: true,
      })
    },

    // Action timeout
    actionTimeout: parseInt(process.env.DEFAULT_TIMEOUT || '30000'),

    // Navigation timeout
    navigationTimeout: parseInt(process.env.NAVIGATION_TIMEOUT || '60000'),
  },

  // Output directory for test artifacts
  outputDir: 'test-results/playwright-data',

  // Projects for different browsers - use only the specified browser
  projects: [
    {
      name: getPlaywrightBrowserName(),
      use: {
        ...devices[getBrowserDevice()],
        browserName: getPlaywrightBrowserName(),
        // Browser channel (for Edge)
        channel: process.env.BROWSER_CHANNEL || undefined,
        // Private session configuration
        ...(process.env.PRIVATE_SESSION === 'true' && {
          // Launch browser in private mode with browser-specific arguments
          launchOptions: {
            args: getPrivateSessionArgs(getPlaywrightBrowserName()),
            channel: process.env.BROWSER_CHANNEL || undefined
          },
          // Ensure no storage state is used
          storageState: undefined
        })
      },
    }
  ],

  // Web server configuration (if needed)
  webServer: process.env.START_SERVER === 'true' ? {
    command: 'npm run start',
    port: 3000,
    reuseExistingServer: !process.env.CI,
  } : undefined,
});
