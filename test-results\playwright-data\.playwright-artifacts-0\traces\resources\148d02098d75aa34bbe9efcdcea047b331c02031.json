{"Architecture": 1, "Audience": 1, "Resources": {"Version": "1.20250602.1.0", "CatalogXml": "<ResourceCatalog>\r\n  <Resources>\r\n    <Resource Key=\"_store_mecontrol-fluent-web_3_28_4-preview_4-f5f950b330f93b6554ae_node_modules_mecontrol_flue-5be2d0\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell._store_mecontrol-fluent-web_3_28_4-preview_4-f5f950b330f93b6554ae_node_modules_mecontrol_flue-5be2d0.d0c4aa1da5a1123021cd.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell._store_mecontrol-fluent-web_3_28_4-preview_4-f5f950b330f93b6554ae_node_modules_mecontrol_flue-5be2d0.d0c4aa1da5a1123021cd.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"_store_mecontrol-fluent-web_3_28_4-preview_4-f5f950b330f93b6554ae_node_modules_mecontrol_flue-d6cd69\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell._store_mecontrol-fluent-web_3_28_4-preview_4-f5f950b330f93b6554ae_node_modules_mecontrol_flue-d6cd69.de9a5b48fc986472d210.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell._store_mecontrol-fluent-web_3_28_4-preview_4-f5f950b330f93b6554ae_node_modules_mecontrol_flue-d6cd69.de9a5b48fc986472d210.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"abt\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.abt.848ba8651591158e6d0e.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.abt.848ba8651591158e6d0e.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"abtprompt\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.abtprompt.84c0bbd18f614ae46e5e.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.abtprompt.84c0bbd18f614ae46e5e.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"appicons\" Type=\"Path\">\r\n      <Path>suiteux.shell.appicons.fc60e906294b4bf53fd1.js</Path>\r\n    </Resource>\r\n    <Resource Key=\"applauncher\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.applauncher.afc4fbfbd17f598085b4.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.applauncher.ade26c6d76faee9b2843.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"applauncherreactcontrol\" Type=\"Path\">\r\n      <Path>suiteux.shell.applauncherreactcontrol.12e2389d119632940a05.js</Path>\r\n    </Resource>\r\n    <Resource Key=\"bootstrapper\" Type=\"Path\">\r\n      <Path>suiteux.shell.bootstrapper.ba3986750f7bc35fdbe6.js</Path>\r\n    </Resource>\r\n    <Resource Key=\"changephoto\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.changephoto.318f5d8ba3eff65fcec9.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.changephoto.318f5d8ba3eff65fcec9.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"chat\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.chat.5649a14fd312ee8c3a64.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.chat.5649a14fd312ee8c3a64.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"chatbase\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.chatbase.454e039460a1c32b2ac7.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.chatbase.a0a4ec50a28041660e31.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"chaterror\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.chaterror.db0b978c5406030373e9.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.chaterror.db0b978c5406030373e9.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"consappdata\" Type=\"Path\">\r\n      <Path>suiteux.shell.consappdata.e81cac96def4e7a3c392.js</Path>\r\n    </Resource>\r\n    <Resource Key=\"core\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.core.f1a10b4c7acd7de56b67.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.core.fd30d3a2f50e8918d6e9.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"crossdomainproxyiframe\" Type=\"Path\">\r\n      <Path>suiteux.shell.crossdomainproxyiframe.b54578ce35c816ee3a99.js</Path>\r\n    </Resource>\r\n    <Resource Key=\"diagnostics\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.diagnostics.04d9a547b8edd37067ca.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.diagnostics.04d9a547b8edd37067ca.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"docsmodule\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.docsmodule.50d1f1e118d4f063ec9b.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.docsmodule.e69e9ab16e3798226088.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"dtt\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.dtt.044dcf55fcbb647bbc6f.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.dtt.044dcf55fcbb647bbc6f.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"es6polyfills\" Type=\"Path\">\r\n      <Path>suiteux.shell.es6polyfills.5f886de2aa30f0b75b0a.js</Path>\r\n    </Resource>\r\n    <Resource Key=\"es6promise\" Type=\"Path\">\r\n      <Path>suiteux.shell.es6promise.c909d66762eeffe0d0d1.js</Path>\r\n    </Resource>\r\n    <Resource Key=\"exchangedata\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.exchangedata.64078601151c80ef4e59.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.exchangedata.64078601151c80ef4e59.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"extraflexpane\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.extraflexpane.8212f725c1470b18e98a.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.extraflexpane.ff84f902c888c15f5800.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"featureflags\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.featureflags.b7f97c3c7d0bd081c6a2.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.featureflags.8bafd1ad1ec61b8a0bab.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"legacyheader\" Type=\"Path\">\r\n      <Path>suiteux.shell.legacyheader.a19940d3bb7390e71293.js</Path>\r\n    </Resource>\r\n    <Resource Key=\"m365start\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.m365start.2cfa78d5b2f0c894007a.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.m365start.061548b5ca60e04e0ff8.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"mast\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.mast.46d5694d49f3940e18e3.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.mast.46d5694d49f3940e18e3.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"mastprompt\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.mastprompt.2f88c1a660aabb6e0e0e.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.mastprompt.2f88c1a660aabb6e0e0e.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"msaltokenfactoryiframe\" Type=\"Path\">\r\n      <Path>suiteux.shell.msaltokenfactoryiframe.f11d0cf8b9d8aeb5d88d.js</Path>\r\n    </Resource>\r\n    <Resource Key=\"msrcrypto\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.msrcrypto.ef97e77ffce8e3a34924.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.msrcrypto.ef97e77ffce8e3a34924.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"myday\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.myday.b49bf98ef65307413c30.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.myday.6f2f15685f3ca0f37f66.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"notificationflex\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.notificationflex.44c894460f99db003066.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.notificationflex.eed4d325b5647f80a93a.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"notifications\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.notifications.3aba501053955bdf8e5b.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.notifications.4bd28f3cd0a53663e67b.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"npm_mecontrol\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.npm_mecontrol.1bb730e750dc053be5b2.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.npm_mecontrol.1bb730e750dc053be5b2.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"oneds-analytics-js\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.oneds-analytics-js.6971ebd362faab0d2a97.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.oneds-analytics-js.6971ebd362faab0d2a97.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"otellogging\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.otellogging.b91940d23fed76ac77e2.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.otellogging.b91940d23fed76ac77e2.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"plus\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.plus.60cd08fcd3fc6cf796f5.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.plus.954836c3e52e1bd302ae.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"responsive\" Type=\"Path\">\r\n      <Path>suiteux.shell.responsive.6ced760cbeb7a4f72c0a.js</Path>\r\n    </Resource>\r\n    <Resource Key=\"sb-strings\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.sb-strings.a69e0add68a9942dda6e.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.sb-strings.a69e0add68a9942dda6e.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"search-box-container-plugins_dist_ondemand_js\" Type=\"Path\">\r\n      <Path>suiteux.shell.search-box-container-plugins_dist_ondemand_js.46d4ad6c7dcb37d62146.js</Path>\r\n    </Resource>\r\n    <Resource Key=\"search\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.search.72374ded6a4436e6d5a3.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.search.72374ded6a4436e6d5a3.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"searchbox\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.searchbox.c84eef9175fa9bb4d12f.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.searchbox.fe346ce1b88a18fc7ace.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"searchux\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.searchux.08724e88cadcc0da679b.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.searchux.08724e88cadcc0da679b.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"shellreactcontrol\" Type=\"Path\">\r\n      <Path>suiteux.shell.shellreactcontrol.867a53511058200a0724.js</Path>\r\n    </Resource>\r\n    <Resource Key=\"staticshell\" Type=\"Path\">\r\n      <Path>suiteux.shell.staticshell.ecffebdf0b7bc998e27b.js</Path>\r\n    </Resource>\r\n    <Resource Key=\"suiteux-shared_out_suiteux-shared_suiteux-header_view_enhanced-sb_sx_scss\" Type=\"Path\">\r\n      <Path>suiteux.shell.suiteux-shared_out_suiteux-shared_suiteux-header_view_enhanced-sb_sx_scss.66be75d65b559738843c.js</Path>\r\n    </Resource>\r\n    <Resource Key=\"supportedlanguages\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.supportedlanguages.8568c34ba9daf7b843bd.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.supportedlanguages.8568c34ba9daf7b843bd.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"themeplus\" Type=\"Path\">\r\n      <Path>suiteux.shell.themeplus.f43620619108ace403e1.js</Path>\r\n    </Resource>\r\n    <Resource Key=\"umc_mecontrol\" Type=\"LTRRTLPath\">\r\n      <RTLPath>suiteux.shell.umc_mecontrol.bfe4c8bfd637c952f0f3.rtl.js</RTLPath>\r\n      <LTRPath>suiteux.shell.umc_mecontrol.bfe4c8bfd637c952f0f3.js</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"vendor\" Type=\"Path\">\r\n      <Path>suiteux.shell.vendor.ca89887fd40e28d265e8cb42c6d7c73b.js</Path>\r\n    </Resource>\r\n    <Resource Key=\"allthemes\" Type=\"Path\">\r\n      <Path>allthemes.f44d6be8e52ee17eaf666a1fbe1b6647.json</Path>\r\n    </Resource>\r\n    <Resource Key=\"shared\" Type=\"Path\">\r\n      <Path>suiteux.shell.shared.384aace5f98a8622f421cf599357b68d.css</Path>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_alpineglow\" Type=\"UserTheme\">\r\n      <Json>alpineglow.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_angular\" Type=\"UserTheme\">\r\n      <Json>angular.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_arcticsolitude\" Type=\"UserTheme\">\r\n      <Json>arcticsolitude.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_backpack\" Type=\"UserTheme\">\r\n      <Json>backpack.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_balloons\" Type=\"UserTheme\">\r\n      <Json>balloons.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_beach\" Type=\"UserTheme\">\r\n      <Json>beach.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_black\" Type=\"UserTheme\">\r\n      <Json>black.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_blueberry\" Type=\"UserTheme\">\r\n      <Json>blueberry.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_blueprint\" Type=\"UserTheme\">\r\n      <Json>blueprint.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_bricks\" Type=\"UserTheme\">\r\n      <Json>bricks.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_cats\" Type=\"UserTheme\">\r\n      <Json>cats.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_chevron\" Type=\"UserTheme\">\r\n      <Json>chevron.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_circuit\" Type=\"UserTheme\">\r\n      <Json>circuit.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_comic\" Type=\"UserTheme\">\r\n      <Json>comic.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_contrast\" Type=\"UserTheme\">\r\n      <Json>contrast.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_cordovan\" Type=\"UserTheme\">\r\n      <Json>cordovan.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_crayon\" Type=\"UserTheme\">\r\n      <Json>crayon.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_cubes\" Type=\"UserTheme\">\r\n      <Json>cubes.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_cubism\" Type=\"UserTheme\">\r\n      <Json>cubism.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_darkcordovan\" Type=\"UserTheme\">\r\n      <Json>darkcordovan.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_darkorange\" Type=\"UserTheme\">\r\n      <Json>darkorange.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_diamonds\" Type=\"UserTheme\">\r\n      <Json>diamonds.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_far\" Type=\"UserTheme\">\r\n      <Json>far.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_futureplus\" Type=\"UserTheme\">\r\n      <Json>futureplus.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_grape\" Type=\"UserTheme\">\r\n      <Json>grape.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_green\" Type=\"UserTheme\">\r\n      <Json>green.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_jelly\" Type=\"UserTheme\">\r\n      <Json>jelly.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_lightblue\" Type=\"UserTheme\">\r\n      <Json>lightblue.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_lightgreen\" Type=\"UserTheme\">\r\n      <Json>lightgreen.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_lite\" Type=\"UserTheme\">\r\n      <Json>lite.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_magneticmood\" Type=\"UserTheme\">\r\n      <Json>magneticmood.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_marigoldhills\" Type=\"UserTheme\">\r\n      <Json>marigoldhills.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_mediumdarkblue\" Type=\"UserTheme\">\r\n      <Json>mediumdarkblue.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_minimal\" Type=\"UserTheme\">\r\n      <Json>minimal.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_modern\" Type=\"UserTheme\">\r\n      <Json>modern.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_mountain\" Type=\"UserTheme\">\r\n      <Json>mountain.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_natureundefined\" Type=\"UserTheme\">\r\n      <Json>natureundefined.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_neworange\" Type=\"UserTheme\">\r\n      <Json>neworange.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_newpink\" Type=\"UserTheme\">\r\n      <Json>newpink.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_orange\" Type=\"UserTheme\">\r\n      <Json>orange.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_paint\" Type=\"UserTheme\">\r\n      <Json>paint.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_pink\" Type=\"UserTheme\">\r\n      <Json>pink.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_pixel\" Type=\"UserTheme\">\r\n      <Json>pixel.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_polka\" Type=\"UserTheme\">\r\n      <Json>polka.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_pomegranate\" Type=\"UserTheme\">\r\n      <Json>pomegranate.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_primary\" Type=\"UserTheme\">\r\n      <Json>primary.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_purple\" Type=\"UserTheme\">\r\n      <Json>purple.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_rainbow\" Type=\"UserTheme\">\r\n      <Json>rainbow.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_raspberry\" Type=\"UserTheme\">\r\n      <Json>raspberry.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_readyconfetti\" Type=\"UserTheme\">\r\n      <Json>readyconfetti.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_red\" Type=\"UserTheme\">\r\n      <Json>red.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_ribbon\" Type=\"UserTheme\">\r\n      <Json>ribbon.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_ribbonrelease\" Type=\"UserTheme\">\r\n      <Json>ribbonrelease.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_robot\" Type=\"UserTheme\">\r\n      <Json>robot.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_rubyhills\" Type=\"UserTheme\">\r\n      <Json>rubyhills.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_simple\" Type=\"UserTheme\">\r\n      <Json>simple.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_spectrum\" Type=\"UserTheme\">\r\n      <Json>spectrum.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_strawberry\" Type=\"UserTheme\">\r\n      <Json>strawberry.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_summersummit\" Type=\"UserTheme\">\r\n      <Json>summersummit.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_super\" Type=\"UserTheme\">\r\n      <Json>super.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_supplies\" Type=\"UserTheme\">\r\n      <Json>supplies.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_teagarden\" Type=\"UserTheme\">\r\n      <Json>teagarden.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_teal\" Type=\"UserTheme\">\r\n      <Json>teal.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_unicorn\" Type=\"UserTheme\">\r\n      <Json>unicorn.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_watermelon\" Type=\"UserTheme\">\r\n      <Json>watermelon.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_whale\" Type=\"UserTheme\">\r\n      <Json>whale.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_whimsical\" Type=\"UserTheme\">\r\n      <Json>whimsical.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_wntrlnd\" Type=\"UserTheme\">\r\n      <Json>wntrlnd.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"usertheme_wrld\" Type=\"UserTheme\">\r\n      <Json>wrld.json</Json>\r\n    </Resource>\r\n    <Resource Key=\"calendarnotificationaudio\" Type=\"Path\">\r\n      <Path>calendarnotificationaudio.mp3</Path>\r\n    </Resource>\r\n    <Resource Key=\"callnotificationaudio\" Type=\"Path\">\r\n      <Path>callnotificationaudio.mp3</Path>\r\n    </Resource>\r\n    <Resource Key=\"chatnotificationaudio\" Type=\"Path\">\r\n      <Path>chatnotificationaudio.mp3</Path>\r\n    </Resource>\r\n    <Resource Key=\"mailnotificationaudio\" Type=\"Path\">\r\n      <Path>mailnotificationaudio.mp3</Path>\r\n    </Resource>\r\n    <Resource Key=\"systemnotificationaudio\" Type=\"Path\">\r\n      <Path>systemnotificationaudio.mp3</Path>\r\n    </Resource>\r\n    <Resource Key=\"icon_notifications_progress_gif\" Type=\"Path\">\r\n      <Path>icon_notifications_progress.gif</Path>\r\n    </Resource>\r\n    <Resource Key=\"o365icons_eot\" Type=\"Path\">\r\n      <Path>o365icons.eot</Path>\r\n    </Resource>\r\n    <Resource Key=\"o365icons_svg\" Type=\"Path\">\r\n      <Path>o365icons.svg</Path>\r\n    </Resource>\r\n    <Resource Key=\"o365icons_ttf\" Type=\"Path\">\r\n      <Path>o365icons.ttf</Path>\r\n    </Resource>\r\n    <Resource Key=\"o365icons_woff\" Type=\"Path\">\r\n      <Path>o365icons.woff</Path>\r\n    </Resource>\r\n    <Resource Key=\"o365shared_css\" Type=\"LTRRTLPath\">\r\n      <RTLPath>o365shared.rtl.css</RTLPath>\r\n      <LTRPath>o365shared.css</LTRPath>\r\n    </Resource>\r\n    <Resource Key=\"o365shared_js\" Type=\"Path\">\r\n      <Path>o365shared.js</Path>\r\n    </Resource>\r\n    <Resource Key=\"o365sharedclusteredimage_png\" Type=\"Path\">\r\n      <Path>o365sharedclusteredimage.png</Path>\r\n    </Resource>\r\n    <Resource Key=\"transparent_gif\" Type=\"Path\">\r\n      <Path>transparent.gif</Path>\r\n    </Resource>\r\n    <Resource Key=\"shellstrings\" Type=\"LangPath\">\r\n      <LangPath Language=\"af\">af/shellstrings.3b7e4b4afd48fc6010f1e451e7ec9ca4.json</LangPath>\r\n      <LangPath Language=\"am\">am/shellstrings.488c06347b4b3f0251c9c9fd5c0287a3.json</LangPath>\r\n      <LangPath Language=\"ar\">ar/shellstrings.2953532c72c2c7de79bf5369e7794351.json</LangPath>\r\n      <LangPath Language=\"as-in\">as-in/shellstrings.3fad620351f81347707002709cc6377e.json</LangPath>\r\n      <LangPath Language=\"az-latn-az\">az-latn-az/shellstrings.abfac6779abb869e2b1ad2ef1d87ca3b.json</LangPath>\r\n      <LangPath Language=\"be-by\">be-by/shellstrings.e39b5cf1b4afebc0cfe7846941cce113.json</LangPath>\r\n      <LangPath Language=\"bg\">bg/shellstrings.588e60aa777aff98ef65d5c9445d3323.json</LangPath>\r\n      <LangPath Language=\"bn-bd\">bn-bd/shellstrings.8013da90f217faaf50c7e7b4329ba20b.json</LangPath>\r\n      <LangPath Language=\"bn-in\">bn-in/shellstrings.774f7981d98f4169de14bd92d9cca522.json</LangPath>\r\n      <LangPath Language=\"bs-latn-ba\">bs-latn-ba/shellstrings.b13adbe4095f98a9e288c74743aa1d65.json</LangPath>\r\n      <LangPath Language=\"ca-es-valencia\">ca-es-valencia/shellstrings.6bcbbd5b4cc2b4f89b0a242382bb58ec.json</LangPath>\r\n      <LangPath Language=\"ca\">ca/shellstrings.d34c27bf84c3427a9c4851d0e84adf5c.json</LangPath>\r\n      <LangPath Language=\"cs\">cs/shellstrings.14949f4e98f4060eca60e5c31d7f7443.json</LangPath>\r\n      <LangPath Language=\"cy\">cy/shellstrings.cb99bef61e91dde9869c8482b8b48782.json</LangPath>\r\n      <LangPath Language=\"da\">da/shellstrings.06390cd7fac22fa972a3d9847e98a073.json</LangPath>\r\n      <LangPath Language=\"de\">de/shellstrings.ca7e2079157f31fb91f623a9ae3022ed.json</LangPath>\r\n      <LangPath Language=\"el\">el/shellstrings.dbb1e8dc884f239eb550a7da08cc37eb.json</LangPath>\r\n      <LangPath Language=\"en-gb\">en-gb/shellstrings.03fb70cb12e0e1cb5adbfe50b9f99b5c.json</LangPath>\r\n      <LangPath Language=\"en\">en/shellstrings.d021b25c84e7615bc3cab4d4b7c31652.json</LangPath>\r\n      <LangPath Language=\"es-mx\">es-mx/shellstrings.334e67cead8331aca275c7c3bcac3c3b.json</LangPath>\r\n      <LangPath Language=\"es\">es/shellstrings.58cd6d3f5e5ec5c42b8c2706dc715083.json</LangPath>\r\n      <LangPath Language=\"et\">et/shellstrings.573ae7da9656e0be9da100823b275272.json</LangPath>\r\n      <LangPath Language=\"eu\">eu/shellstrings.4e8bf2b0f501d9f4b4fc9bcd48e5420c.json</LangPath>\r\n      <LangPath Language=\"fa\">fa/shellstrings.87173e576d8786b47a129b5e3bf3d0b3.json</LangPath>\r\n      <LangPath Language=\"fi\">fi/shellstrings.e8b243866dc00d8932347ae9f731902e.json</LangPath>\r\n      <LangPath Language=\"fil\">fil/shellstrings.19bbebab901b613cd23083ce7a6eaac1.json</LangPath>\r\n      <LangPath Language=\"fr-ca\">fr-ca/shellstrings.5d86e39c0b13f2c2ceeeb57b408513b7.json</LangPath>\r\n      <LangPath Language=\"fr\">fr/shellstrings.a6e55dcb8b1bc73bca3a4fc5ae85664a.json</LangPath>\r\n      <LangPath Language=\"ga-ie\">ga-ie/shellstrings.b1608458ee55cbfa9ab79d5d574a6d11.json</LangPath>\r\n      <LangPath Language=\"gd-gb\">gd-gb/shellstrings.00cbc7b2dcdedc9bce377388451f5f94.json</LangPath>\r\n      <LangPath Language=\"gl\">gl/shellstrings.ee83b66e8615a40719f1a2d271a73552.json</LangPath>\r\n      <LangPath Language=\"gu\">gu/shellstrings.62f60a6d0c0a7d9e2e8d56d8a1351497.json</LangPath>\r\n      <LangPath Language=\"ha-latn-ng\">ha-latn-ng/shellstrings.af7f384b4684ae5a683f417140de0ea2.json</LangPath>\r\n      <LangPath Language=\"he\">he/shellstrings.7731cca8590de55f3a80c34ff5b5c03d.json</LangPath>\r\n      <LangPath Language=\"hi\">hi/shellstrings.13e658b8da4ffae9978dbfac81dc91d8.json</LangPath>\r\n      <LangPath Language=\"hr\">hr/shellstrings.22c2c5f63533fa7995deaf8c4519a7a7.json</LangPath>\r\n      <LangPath Language=\"hu\">hu/shellstrings.ee073324eda92f8470b6fdd8659cccbf.json</LangPath>\r\n      <LangPath Language=\"hy\">hy/shellstrings.dd050d37fbcfbb12526a76a23e678ade.json</LangPath>\r\n      <LangPath Language=\"id\">id/shellstrings.b6ba24d17274c0f9cdf40dd699ff8a0e.json</LangPath>\r\n      <LangPath Language=\"is\">is/shellstrings.306bb410479290ebe658990b8cd96ef2.json</LangPath>\r\n      <LangPath Language=\"it\">it/shellstrings.6e5eac0f221beec215a352afeec41656.json</LangPath>\r\n      <LangPath Language=\"ja\">ja/shellstrings.9b9964be58305c2fe072c7252551b3c4.json</LangPath>\r\n      <LangPath Language=\"ka\">ka/shellstrings.6988993357fb1085068aec719b2af1d7.json</LangPath>\r\n      <LangPath Language=\"kk\">kk/shellstrings.b4db6a007e0e62f915933af817813de7.json</LangPath>\r\n      <LangPath Language=\"km-kh\">km-kh/shellstrings.72c3abf8f801bd421bf25d6147c27751.json</LangPath>\r\n      <LangPath Language=\"kn\">kn/shellstrings.95f2ae88d22a6469b5ebacbe1575f04e.json</LangPath>\r\n      <LangPath Language=\"ko\">ko/shellstrings.ded057afa704d497f8b71e66ad41040b.json</LangPath>\r\n      <LangPath Language=\"kok\">kok/shellstrings.3a9e7c8e694ab6e7b7d6d0014780d85e.json</LangPath>\r\n      <LangPath Language=\"ky\">ky/shellstrings.06cfd492743a0142a1529e0b936a7f5e.json</LangPath>\r\n      <LangPath Language=\"lb-lu\">lb-lu/shellstrings.c7d4a35c798aa22acb9d5238dd6b37e8.json</LangPath>\r\n      <LangPath Language=\"lo-la\">lo-la/shellstrings.c4cd5b84e5d3a3055004e8b4dc21ae37.json</LangPath>\r\n      <LangPath Language=\"lt\">lt/shellstrings.d6ace0a0920b52211f09f13007024dd7.json</LangPath>\r\n      <LangPath Language=\"lv\">lv/shellstrings.aee77dad82877f85004022d503e06ad4.json</LangPath>\r\n      <LangPath Language=\"mi-nz\">mi-nz/shellstrings.c33167d9ea0cd6179fc4992e45996cf5.json</LangPath>\r\n      <LangPath Language=\"mk\">mk/shellstrings.d4a1bae8d48086886144991a6dc78cb3.json</LangPath>\r\n      <LangPath Language=\"ml\">ml/shellstrings.93e152821f5ef80f677f5d0997a2c86c.json</LangPath>\r\n      <LangPath Language=\"mn-mn\">mn-mn/shellstrings.38b0118254bc3af33f4cbe3a119579b4.json</LangPath>\r\n      <LangPath Language=\"mr\">mr/shellstrings.f5f57ad40eac7a9f3de8c059ab44ff14.json</LangPath>\r\n      <LangPath Language=\"ms\">ms/shellstrings.45e2f04d7e76725a5b49429cca6d8426.json</LangPath>\r\n      <LangPath Language=\"mt-mt\">mt-mt/shellstrings.a375ad8d5f2c5b38e11101b4f1e9baac.json</LangPath>\r\n      <LangPath Language=\"ne-np\">ne-np/shellstrings.d6fe2d16d9583b716a50890af7049818.json</LangPath>\r\n      <LangPath Language=\"nl\">nl/shellstrings.d86821c3e07c12c72393b100f2aaf3ed.json</LangPath>\r\n      <LangPath Language=\"nn-no\">nn-no/shellstrings.6f3f2b7567c49e1ebd03376fc362a199.json</LangPath>\r\n      <LangPath Language=\"no\">no/shellstrings.78ee9a0e4e99155993598a4ab42c103f.json</LangPath>\r\n      <LangPath Language=\"or\">or/shellstrings.b2fdb7329a20e9c11f948cfaf63e09e1.json</LangPath>\r\n      <LangPath Language=\"pa-in\">pa-in/shellstrings.bef7ae54a1bf5b9702d5eb9c3d98d85a.json</LangPath>\r\n      <LangPath Language=\"pl\">pl/shellstrings.adc5b4939bb02d7f9ba6f0360a87c7e0.json</LangPath>\r\n      <LangPath Language=\"prs\">prs/shellstrings.bbe83c3524e2b06f2d6b3a2e9605a1c5.json</LangPath>\r\n      <LangPath Language=\"pt-pt\">pt-pt/shellstrings.7d0fe1be9c813e3bb395ca2b6a87ed96.json</LangPath>\r\n      <LangPath Language=\"pt\">pt/shellstrings.7a7254aac13a20d3803c996a6991e72d.json</LangPath>\r\n      <LangPath Language=\"qps-ploca\">qps-ploca/shellstrings.2e238fff89983aa74feb5ad5098e1aa3.json</LangPath>\r\n      <LangPath Language=\"qps-plocm\">qps-plocm/shellstrings.2e238fff89983aa74feb5ad5098e1aa3.json</LangPath>\r\n      <LangPath Language=\"quz-pe\">quz-pe/shellstrings.0551d990ebc7196f6ffc0f2b56498251.json</LangPath>\r\n      <LangPath Language=\"ro\">ro/shellstrings.d4d7d057a404e40f483733160c50ee34.json</LangPath>\r\n      <LangPath Language=\"ru\">ru/shellstrings.aa403bd8501809771bf897a951774fa8.json</LangPath>\r\n      <LangPath Language=\"sd-arab-pk\">sd-arab-pk/shellstrings.9c08a027c60f19589c54c8ee1003f43e.json</LangPath>\r\n      <LangPath Language=\"si-lk\">si-lk/shellstrings.06a514f0059b92876ee97b19f306df38.json</LangPath>\r\n      <LangPath Language=\"sk\">sk/shellstrings.2dd3643adeca45c231c6e878a455a21c.json</LangPath>\r\n      <LangPath Language=\"sl\">sl/shellstrings.723e72ee6a03bb2c367e67e9d7113dc3.json</LangPath>\r\n      <LangPath Language=\"sq\">sq/shellstrings.ec89d84fac3cf3e80ed4f37a3004d427.json</LangPath>\r\n      <LangPath Language=\"sr-cyrl-ba\">sr-cyrl-ba/shellstrings.cb87fc9274bd99379af9f3b142a336fb.json</LangPath>\r\n      <LangPath Language=\"sr-cyrl\">sr-cyrl/shellstrings.0f4f4b809d085528de3e80babdb450ea.json</LangPath>\r\n      <LangPath Language=\"sr\">sr/shellstrings.89f53c88d4daf062b2c01c0311f2af5b.json</LangPath>\r\n      <LangPath Language=\"sv\">sv/shellstrings.92d09d6c4bc770d14582a38c9001d874.json</LangPath>\r\n      <LangPath Language=\"sw\">sw/shellstrings.eef718478f1bf9c40c2c59d47662cf90.json</LangPath>\r\n      <LangPath Language=\"ta\">ta/shellstrings.914be41119015f12ea755b3dd44c3573.json</LangPath>\r\n      <LangPath Language=\"te\">te/shellstrings.7267d5f9553820d3ce323d33e7dbdb90.json</LangPath>\r\n      <LangPath Language=\"th\">th/shellstrings.5605796426fc8b6b4663bdec95aa933b.json</LangPath>\r\n      <LangPath Language=\"tk-tm\">tk-tm/shellstrings.c3fff57542737dba64c81670d9f435aa.json</LangPath>\r\n      <LangPath Language=\"tr\">tr/shellstrings.f93a03945897b3e7276f7a0a518ef112.json</LangPath>\r\n      <LangPath Language=\"tt\">tt/shellstrings.76b2b9989dca5acf7221fe8898d360d7.json</LangPath>\r\n      <LangPath Language=\"ug-cn\">ug-cn/shellstrings.334a1d33d43342d36f82f3bd8c17c3aa.json</LangPath>\r\n      <LangPath Language=\"uk\">uk/shellstrings.5df1e30489d0436eb6c6b9c0d9d767f1.json</LangPath>\r\n      <LangPath Language=\"ur\">ur/shellstrings.51e910bce8d342ffcb3b18a93f312355.json</LangPath>\r\n      <LangPath Language=\"uz\">uz/shellstrings.9d14823b27bf43782f8c8ba062cb7e71.json</LangPath>\r\n      <LangPath Language=\"vi\">vi/shellstrings.20834e5a0de4354d1bea0d4691814fac.json</LangPath>\r\n      <LangPath Language=\"zh-hans\">zh-hans/shellstrings.868bd38179b0f01174b33a8e23d6dff8.json</LangPath>\r\n      <LangPath Language=\"zh-hant\">zh-hant/shellstrings.d88270a02b6244ee1b0d0a036db6295b.json</LangPath>\r\n    </Resource>\r\n  </Resources>\r\n  <Pages />\r\n</ResourceCatalog>", "ResourceCatalogHostUrl": "https://res-1.cdn.office.net/shellux"}, "IsRtl": false, "AriaTelemetryTenantToken": "c6c190a1b73c4a63bba89835d546cf28-f2a0482f-a00d-48d9-822e-e89cc89eb64d-7688", "OTelTelemetryTenantToken": "b0c82c6598ad49f3848b1d3dc0d8dd25-299cbfe9-b72e-4e9d-a5e3-20319efba5b5-7268", "Environment": "seaprod", "CultureHierarchy": ["en-US", "en"]}