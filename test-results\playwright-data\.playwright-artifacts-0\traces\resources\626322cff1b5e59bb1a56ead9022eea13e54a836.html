<head>
    <meta name="referrer" content="no-referrer">
</head>
<script>
window.onload = function() {
    // modal style
var css = '\
  .ns-template-wrap { \
     z-index: 2; \
     font-size: 12px; \
     background: #ffffff; \
     line-height: 16px; \
     color: #646464; \
     border-top: 8px solid transparent; \
     padding: 24px 32px 32px; \
     font-family: Lato, sans-serif; \
  } \
  .ns-template-inner-wrap { \
     width: 600px; \
     margin: 0 auto; \
  } \
  .ns-template-wrap * { \
     box-sizing: border-box; \
  } \
  .ns-template-wrap h1 { \
     margin-top: 0; \
     color: #000000; \
     font-size: 20px; \
     line-height: 24px; \
     font-weight: normal; \
     margin-bottom: 16px; \
  } \
  .ns-template-wrap h2 { \
     margin-top: 0; \
     color: #000000; \
     font-size: 14px; \
     line-height: 20px; \
     font-weight: normal; \
     margin-bottom: 16px; \
  } \
  .ns-template-wrap p { \
     color: #646464; \
     font-size: 12px; \
     line-height: 16px; \
     margin-bottom: 24px; \
  } \
  .ns-template-wrap p.message-max-height { \
     max-height: 40vh; \
     overflow: auto; \
     word-wrap: break-word; \
     margin-bottom: 20px; \
  } \
  .ns-template-wrap fieldset { \
     margin: 0 0 12px 0; \
     padding: 0; \
     border: 0; \
  } \
  .ns-template-wrap fieldset label{ \
     margin: 0 0 8px 0; \
     padding: 0; \
     border: 0; \
     font-size: 14px; \
     line-height: 20px; \
     color: #000000; \
  } \
  .ns-template-wrap fieldset label::after{ \
     content: ""; \
     display: table; \
     width: 100%; \
     clear: both; \
  } \
  .ns-template-wrap fieldset label:last-of-type{ \
     margin-bottom: 0; \
  } \
  .ns-template-wrap .custom-logo { \
     margin-bottom: 24px; \
  } \
  .ns-template-wrap .custom-logo.small { \
     max-height: 48px; \
  } \
  .ns-template-wrap .custom-logo.medium { \
     max-height: 64px; \
  } \
  .ns-template-wrap .custom-logo.large { \
     max-height: 128px; \
  } \
  .ns-template-wrap textarea { \
     font-family: inherit; \
     padding: 8px 12px; \
     height: 80px; \
     width: 100% !important; \
     resize: none; \
     border: 1px solid #E1E1E1; \
     border-radius: 4px; \
     background-color: #FFFFFF; \
     color: #434343; \
     font-size: 13px; \
     line-height: 16px; \
  } \
  .ns-template-wrap button[disabled] { \
     background: #EEEEEE; \
     border-color: #E3E3E3; \
     color: #ccc; \
  } \
  .ns-template-wrap button { \
     height: auto; \
     min-width: 88px; \
     max-width: 160px; \
     overflow: hidden; \
     text-overflow: ellipsis; \
     padding: 8px 16px; \
     font-size: 12px; \
     font-weight: bold; \
     line-height: 16px; \
     text-align: center; \
     text-transform: uppercase; \
  } \
  .ns-template-wrap button.ns-btn-primary { \
     border: 1px solid #1BB7DF; \
     color: #FFFFFF; \
     border-radius: 4px; \
     background-color: #1BB7DF; \
  } \
  .ns-template-wrap button.ns-btn-cancel { \
     border: 1px solid #1BB7DF; \
     border-radius: 4px; \
     background-color: #FFFFFF; \
     color: #12A3D4; \
     margin-right: 8px; \
  }\
  .ns-user-justify-modal-wrap { \
      font-size: 14px; \
      color: #333333; \
      text-align: center; \
  } \
  .ns-user-justify-modal-backdrop { \
      position: fixed; \
      z-index: 1; \
      top: 0; \
      left: 0; \
      height: 100%; \
      width: 100%; \
      opacity: .6; \
      background: #000000; \
  } \
  .ns-user-justify-modal-wrap .modal-header { \
      min-height: 40px; \
      height: auto; \
      padding: 0; \
      background: #FFFFFF; \
      border-bottom: 0; \
  } \
  .ns-user-justify-modal-wrap .modal-header .modal-header { \
      padding: 0 0 24px 0; \
  } \
  .ns-user-justify-modal-wrap textarea { \
      font-size: inherit; \
      font-family: inherit; \
  } \
  .ns-user-justify-modal-wrap * { \
      box-sizing: border-box; \
  } \
  .ns-user-justify-modal { \
      position: relative; \
      display: inline-block; \
      z-index: 2; \
      padding: 72px 32px 0; \
      width: 100%; \
      max-width: 480px; \
      text-align: left; \
      border-radius: 2px; \
      background-color: #FFFFFF; \
      color: #242424; \
      font-family:Lato, Arial, sans-serif; \
      font-size: 14px; \
      line-height: 20px; \
  } \
  .ns-user-justify-modal h1 { \
      margin-top: 0; \
      color: #000100; \
      font-size: 20px; \
      line-height: 24px; \
      font-family:Lato, Arial, sans-serif; \
  } \
  .ns-user-justify-modal h2 { \
      margin-top: 0; \
      color: #000100; \
      font-size: 18px; \
      line-height: 24px; \
      font-family:Lato, Arial, sans-serif; \
  } \
  .ns-user-justify-modal h3 { \
      margin-top: 0; \
      color: #000100; \
      font-size: 16px; \
      line-height: 24px; \
      font-family:Lato, Arial, sans-serif; \
  } \
  .ns-user-justify-modal p { \
      margin: 24px 0; \
      color: #434343; \
      font-size: 12px; \
      line-height: 15px; \
      max-height: 150px; \
      overflow-y: auto; \
  } \
  .ns-user-justify-modal label { \
      display: block; \
      margin-right: .5em; \
  } \
  .ns-user-justify-modal fieldset { \
      margin: 1em 0 0; \
      padding: 0; \
      border: 0; \
  } \
  .ns-user-justify-modal .custom-logo { \
      position: absolute; \
      top: 24px; \
      left: 32px; \
      max-height: 60px; \
      max-width: 128px; \
  } \
  #ns-justify-description-box { \
      padding: .5em; \
      height: 7.5em; \
      width: 100% !important; \
      resize: none; \
      margin-bottom: 0; \
      border: 1px solid #E1E1E1; \
      border-radius: 4px; \
      background-color: #FFFFFF; \
      color: #9B9B9B; \
      font-size: 13px; \
      line-height: 16px; \
  } \
  .ns-user-justify-modal-wrap button[disabled] { \
      background: #EEEEEE; \
      border-color: #E3E3E3; \
      color: #ccc; \
  } \
  .ns-btn-group { \
      margin-top: 40px; \
      padding: 16px 0; \
      text-align: right; \
      position: relative; \
  } \
  .ns-btn-group:after { \
      content: ""; \
      background: #EEEEEE; \
      height: 1px; \
      width: calc(100% + 64px); \
      position: absolute; \
      top: 0; \
      left: -32px; \
  } \
  .ns-btn-group button { \
      height: auto; \
      min-width: 88px; \
      max-width: 160px; \
      overflow: hidden; \
      text-overflow: ellipsis; \
      padding: 8px 16px; \
      font-size: 12px; \
      font-weight: bold; \
      line-height: 16px; \
      text-align: center; \
      text-transform: uppercase; \
  } \
  .ns-btn-cancel { \
      border: 1px solid #1BB7DF; \
      border-radius: 4px; \
      background-color: #FFFFFF; \
      color: #1BB7DF; \
      float: left; \
  } \
  .ns-btn-primary { \
      color: #FFFFFF; \
      border-radius: 4px; \
      background-color: #1BB7DF; \
      border: 1px solid #1BB7DF; \
  } \
  .ns-btn-group.ns-no-border { \
      text-align: left; \
      padding: 0; \
      margin-top: 32px; \
  } \
  .ns-btn-group.ns-no-border:after { \
      background: transparent; \
  } \
  .ns-btn-group.ns-no-border .ns-btn-cancel { \
      margin-right: 8px; \
  } \
  .ns-user-justify-modal h4 { \
      margin-top:0; \
      margin-bottom:0; \
      color: #000100; \
      font-size: 20px; \
      line-height: 24px; \
      font-family:Lato, Arial, sans-serif; \
      font-weight:normal; \
  } \
  .ns-user-justify-modal h5 { \
      margin-top: 0; \
      margin-bottom: 0; \
      color: #434343; \
      font-size: 12px; \
      line-height: 16px; \
      font-family:Lato, Arial, sans-serif; \
      font-weight: normal; \
  } \
  .ns-form-input { \
      width: 280px; \
      border: 1px solid #E1E1E1; \
      border-radius: 4px; \
      background-color: #FFFFFF; \
      color: #9B9B9B; \
      font-family:Lato, Arial, sans-serif; \
      font-size: 13px; \
      line-height: 16px; \
      padding:8px 12px; \
      margin-top: 16px; \
      margin-bottom:0; \
  } \
  .ns-padding-none { \
      padding: 0 !important; \
  } \
  .ns-margin-none { \
      margin: 0 !important; \
  }';

var notify_css = document.createElement('style');
notify_css.innerHTML = css;
document.getElementsByTagName("head")[0].appendChild(notify_css);

function renderMessage(params) {
    /*
    // params comes from server
    // The top format is for rendering the justification html
    // and the bottom format is for rendering the block html.
    @params = {
    msg: (string),
    justify: (htmlString)
    } or
    @params = {
    rawmsg: (htmlString)
    }
     */
    var notify_modal_container = document.createElement('div');
    if (document.body) {
        document.body.appendChild(notify_modal_container);
    }
    // Display justification pages
    doc = window.document;

    notify_css = doc.createElement('style');
    notify_css.innerHTML = css;
    doc.getElementsByTagName("head")[0].appendChild(notify_css);

    doc.body.innerHTML = params.justify;
    doc.querySelector('.msg').innerHTML = params.msg;

    formData = {};
    var justifyForm = doc.querySelector('.ns-template-inner-wrap form');
    var descriptionField = doc.querySelector('#ns-justify-description');
    var descriptionFieldBox = doc.querySelector('#ns-justify-description-box');
    var justifySubmitBtns = justifyForm.querySelectorAll('.ns-submit');
    var justifyRadio = justifyForm.querySelector('#justify-usage-radio');
    var falsePositiveRadio = justifyForm.querySelector('#false-positive-radio');

    var justification_req = 1;
    _ns_justify_message_ = 1;
    _ns_url_ = justifyForm.getAttribute('action');

    var justifyFields = justifyForm.querySelectorAll('[name]');
    for (var i=0; i<justifyFields.length; i++) {
        var field = justifyFields[i];
        var key = field.getAttribute('name');
        if(key == 'msuid') {
            _ns_msuid_ = field.value;
            break;
        }
    }

    function disableSubmitBtns() {
        if (justifySubmitBtns.length == 2) {
            justifySubmitBtns[1].setAttribute('disabled', 'disabled');
        } else if (justifySubmitBtns.length == 1) {
            justifySubmitBtns[0].setAttribute('disabled', 'disabled');
        }
    }

    function enableSubmitBtns() {
        if (justifySubmitBtns.length == 2) {
            justifySubmitBtns[1].removeAttribute('disabled');
        } else if (justifySubmitBtns.length == 1) {
            justifySubmitBtns[0].removeAttribute('disabled');
        }
    }

    function validateForm() {
        if(params.force_justify == "false")
            return;

        if (!descriptionFieldBox || !justifySubmitBtns) {
            return;
        }
        if (justification_req == 1 && !descriptionFieldBox.value.length) {
            disableSubmitBtns();
        } else {
            if (justification_req == 1 &&
                descriptionFieldBox.value.length &&
                !descriptionFieldBox.value.match(/([^\u0000-\u007F]|\w)+/)) {
                disableSubmitBtns();
            } else {
                enableSubmitBtns();
            }
        }
    }

    function submitModal() {
        var justifyFields = justifyForm.querySelectorAll('[name]');
        var url = justifyForm.getAttribute('action');

        var cancel_button = 0;
        if(this.value == 'cancel')
            cancel_button = 1;

        var xhr = new XMLHttpRequest();
        xhr.open("POST", url, true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.onreadystatechange = function () {
            if (xhr.readyState == 2) {
                if(cancel_button)
                    window.location = "https://www.google.com";
                else
                    document.location.reload();
            }
        }

        formData["justification_type"] = "justification";
        formData["_uj_category_id"] = "10001";
        formData["web_category"] = "10001";
        formData["policy"] = "[Web] - Default Blocked Categories";
        formData["_uj_dest_ip"] = "**************";
        formData["uj_response_file_name"] = "";
        formData["app"] = "static";
        formData["from_user"] = "";
        formData["instance_id"] = "";
        formData["suppress_interval"] = "";
        formData["suppress_data"] = "";
        for (var i=0; i<justifyFields.length; i++) {
            var field = justifyFields[i];
            var key = field.getAttribute('name');
            var value = field.value;
            if ((key == "justification_type") && (!field.checked)) {
                continue;
            }
            formData[key] = value;
        }

        if (this.value) {
            formData.submitId = this.value;
        } else {
            formData.submitId = 'cancel';
        }
        xhr.send(JSON.stringify(formData));
    }

    validateForm();
    // add events
    if (descriptionField) {
        descriptionField.addEventListener('input', validateForm);
    }

    if(justifyRadio) {
        justifyRadio.addEventListener('click', function() {
            descriptionField.style.display = 'block';
            justification_req = 1;
            validateForm();
        });
    }

    if(falsePositiveRadio) {
        falsePositiveRadio.addEventListener('click', function() {
            descriptionField.style.display = 'none';
            descriptionField.value = '';
            justification_req = 0;
            validateForm();
        });
    }

    for (var i=0; i<justifySubmitBtns.length; i++) {
        justifySubmitBtns[i].addEventListener('click', submitModal);
    }

    if (doc.querySelector('#ns-justify-description-box')) {
        var keydownfunc = function(e) {
            e.stopPropagation();
        }
        doc.querySelector('#ns-justify-description-box').addEventListener("keydown", keydownfunc, false);

        var keypressfunc = function(e) {
            e.stopPropagation();
        }
        doc.querySelector('#ns-justify-description-box').addEventListener("keypress", keypressfunc, false);
    }
}

renderMessage({
    "msg" : "Access to this URL is blocked.",
    "expire" : 600000,
    "justify" : "<div class='ns-template-outer-wrap'><div class='ns-template-wrap'><div class='ns-template-inner-wrap'><img class='custom-logo small' alt='logo' src='data:image/png;base64,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'><h1 id='preview-title-box' class='msg'>Access to this URL is blocked.</h1><h2 id='preview-subtitle-box'></h2><p id='preview-information-box' class='message-max-height'>Access has been blocked as per KGS Internet Access Policy, driven by the information security risk assessment done by ITS team (NITSO). Please get an approval from your Director/Partner if it is required for business delivery.<br/><br/>[Web] - Default Blocked Categories<br/>URL - res-1.public.onecdn.static.microsoft/files/odsp-web-prod_2025-05-23.004/spwebworker.js<br/>Category - All Predefined Categories, Finance/Accounting, Content Server<br/>Application Name - static</p><form action=/3c90321867231d0540b3895e02d078abe212f3a1dbc4e6fb67fb376cbc18a06b/s><input type='hidden' name='msuid' value='6345637029200775212' /><input type='hidden' name='referer' value='https://www.google.com'/><input type='hidden' name='policy_name' value='[Web] - Default Blocked Categories'/><input type='hidden' name='app' value='static'/><input type='hidden' name='activity' value='Browse'/><input type='hidden' name='filename' value=''/><p id='preview-footer-box'></p><div class='ns-btn-group'><button class='ns-btn-cancel ns-submit' type='button' value='cancel' id='ns-justify-cancel'>OK</button></div></form></div></div></div>",
    "force_justify" : "false"
});
}
</script>


