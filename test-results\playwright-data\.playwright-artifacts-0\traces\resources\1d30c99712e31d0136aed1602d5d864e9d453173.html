<!DOCTYPE html>

<html>
<head>
    <script type="text/javascript" src="https://res-1.cdn.office.net/shellux/suiteux.shell.msaltokenfactoryiframe.f11d0cf8b9d8aeb5d88d.js"></script>
</head>
<body>
    <script type="text/javascript" nonce="5EtjI&#x2B;LigoMhIjbJ7sYv5VTgyItSXHbz0FS4&#x2B;oPEUko=">
        
                var requestExecutorSettings = {
                    originAuthorityValidator: O365SuiteServiceProxy.ServiceHandlers.validateOrigin,
                    trustedOriginAuthorities: [],
                    allowedEndpoints: [{host: "^wcss\\.wwprod\\.officeshell\\.svc\\.cloud\\.microsoft$",paths: ["^api/(.*?)"]},{host: "^wwdb\\.wcss\\.wwprod\\.officeshell\\.svc\\.cloud\\.microsoft$",paths: ["^api/(.*?)"]},{host: "^eudb\\.wcss\\.wwprod\\.officeshell\\.svc\\.cloud\\.microsoft$",paths: ["^api/(.*?)"]},{host: "^webshell\\.suite\\.office\\.com$",paths: ["^api/(.*?)"]},{host: "^wwdb\\.webshell\\.suite\\.office\\.com$",paths: ["^api/(.*?)"]},{host: "^eudb\\.webshell\\.suite\\.office\\.com$",paths: ["^api/(.*?)"]},{host: "^ocws\\.officeapps\\.live\\.com$",paths: ["^ocs/(.*?)"]},{host: "^graph\\.microsoft\\.com$",paths: ["^v1\\.0/(.*?)"]},{host: "^management\\.azure\\.com$",paths: ["^providers/Microsoft.BusinessAppDiscovery/(.*?)"]},{host: "^outlook\\.office\\.com$",paths: ["^/calendar/opxdeeplink/myday$"]},{host: "^clients\\.config\\.office\\.net$",paths: ["^user/v1\\.0/(.*?)"]}],
                    allowedResources: [{resource: "api.office.net",hosts: ["ocws.officeapps.live.com"]},{resource: "graph.microsoft.com",hosts: ["graph.microsoft.com"]},{resource: "wcss.wwprod.officeshell.svc.cloud.microsoft",hosts: ["wcss.wwprod.officeshell.svc.cloud.microsoft", "eudb.wcss.wwprod.officeshell.svc.cloud.microsoft", "wwdb.wcss.wwprod.officeshell.svc.cloud.microsoft"]},{resource: "webshell.suite.office.com",hosts: ["webshell.suite.office.com", "eudb.webshell.suite.office.com", "wwdb.webshell.suite.office.com"]},{resource: "management.azure.com",hosts: ["management.azure.com"]},{resource: "outlook.office.com",hosts: ["outlook.office.com"]},{resource: "clients.config.office.net",hosts: ["clients.config.office.net"]}],
                    shouldAttachOauthTokens: true,
                    trustedOriginAuthoritiesSetFromServer: false
                };

                O365SuiteServiceProxy.RequestExecutorMessageProcessor.init(requestExecutorSettings);

                var tokenFactorySettings = {
                    clientId: "89bee1f7-5e6e-4d8a-9f3d-ecd601259da7",
                    authority: "https\u003A//login.microsoftonline.com/organizations",
                    cacheLocation: "sessionStorage",
                    parentPageOrigin: "https\u003A//spo-global.kpmg.com",
                    upn: "jagannathak\u0040kpmg.com"
                };

                O365MSALTokenFactoryIframe.TokenFactoryInsideIframe.init(tokenFactorySettings);
    </script>
</body>
</html>