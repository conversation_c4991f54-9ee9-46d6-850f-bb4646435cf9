
        <!DOCTYPE html>
        <html>

        <head>
          <title>SPFx MSAL V4 Single Sign On Redirect Page</title>
          <script type='text/javascript'>
            const DEFAULT_CLIENT_ID = '08e18876-6177-487e-b8b5-cf950c1e598c';

            const STORAGE_KEYS = {
              AUTHORITY: 'spfx.msal.authority',
              CLIENT_ID: 'spfx.msal.clientId',
              V1_CLIENT_ID: 'msalRedirectClientId',
              IS_REDIRECT_IN_PROGRESS: 'spfx.msal.isRedirectInProgress',
              REDIRECTED_FROM: 'spfx.msal.redirectedFrom',
              SHOULD_USE_MSAL_BROWSER: 'spfx.msal.shouldUseMsalBrowser',
              MSAL_V4_OVERRIDE: 'spfx.msalv4.override',
            };

            const storageState = {
              authority: loadItem(STORAGE_KEYS.AUTHORITY),
              clientId: loadItem(STORAGE_KEYS.CLIENT_ID),
              v1ClientId: loadItem(STORAGE_KEYS.V1_CLIENT_ID),
              redirectedFrom: loadItem(STORAGE_KEYS.REDIRECTED_FROM),
              shouldUseMsalBrowser: loadItem(STORAGE_KEYS.SHOULD_USE_MSAL_BROWSER) === 'true',
            };

            function handleResponse() {
                var authority = storageState.authority;
                var clientId = storageState.clientId;

                if (!clientId) {
                clientId = DEFAULT_CLIENT_ID;
                authority = undefined;
                }
                const msalBrowserConfig = {
                auth: {
                    authority: authority,
                    clientId: clientId,
                    navigateToLoginRequestUrl: false
                }
                };
                try {
                    if (localStorage) {
                        localStorage.setItem('lsTest','lsTest');
                        localStorage.removeItem('lsTest');
                        msalBrowserConfig.cache = { cacheLocation: 'localStorage' };
                    }
                }
                catch {
                }

                var msalBrowserInstance = new msal.PublicClientApplication(msalBrowserConfig);
                msalBrowserInstance
                .initialize()
                .then(() => msalBrowserInstance.handleRedirectPromise())
                .then((tokenResponse) => {
                    if (tokenResponse) {
                    cleanV3Storage();

                    if (tokenResponse.account) {
                        msalBrowserInstance.setActiveAccount(tokenResponse.account);
                    }

                    if (tokenResponse.state) {
                        window.location.href = tokenResponse.state;
                    }
                    }

                    if (storageState.redirectedFrom) {
                    window.location.href = storageState.redirectedFrom;
                    }
                })
                .catch((error) => {
                    // If we get an error, we can attempt to at least navigate back.
                    if (storageState.redirectedFrom) {
                    window.location.href = storageState.redirectedFrom;
                    }
                });
            }

            function isEmbedded() {
                try {
                    return window.self !== window.top;
                } catch (e) {
                    return false;
                }
            }

            function isPopup() {
                try {
                    return typeof window !== 'undefined' &&
                        !!window.opener &&
                        window.opener !== window &&
                        typeof window.name === 'string' &&
                        window.name.indexOf('msal') === 0;
                } catch (e) {
                    return false;
                }
            }

            function cleanV3Storage() {
              window.sessionStorage.removeItem(STORAGE_KEYS.AUTHORITY);
              window.sessionStorage.removeItem(STORAGE_KEYS.CLIENT_ID);
              window.sessionStorage.removeItem(STORAGE_KEYS.REDIRECTED_FROM);
              window.sessionStorage.removeItem(STORAGE_KEYS.SHOULD_USE_MSAL_BROWSER);
              window.sessionStorage.removeItem(STORAGE_KEYS.IS_REDIRECT_IN_PROGRESS);
            }

            function loadItem(key) {
              return window.sessionStorage.getItem(key) || undefined;
            }

            function dynamicLoadJs(url, callback) {
              var head = document.getElementsByTagName('head')[0];
              var script = document.createElement('script');
              script.type = 'text/javascript';
              script.src = url;
              script.onreadystatechange = callback;
              script.onload = callback;
              head.appendChild(script);
            }

            function isMsalV4Override() {
                if (loadItem(STORAGE_KEYS.MSAL_V4_OVERRIDE)) {
                  window.sessionStorage.removeItem(STORAGE_KEYS.MSAL_V4_OVERRIDE);
                  return true;
                } else {
                  return false;
                }
            }

            function main() {
              if (!isEmbedded() && !isPopup()) {
                if (isMsalV4Override()) {
                  dynamicLoadJs('https://spo-global.kpmg.com/_layouts/15/msal_browser_v4_min.js', handleResponse);
                } else {
                  dynamicLoadJs('https://spo-global.kpmg.com/_layouts/15/msal_browser_min.js', handleResponse);
                }
              }
            }

            main();
          </script>
        </head>
        </html>
        