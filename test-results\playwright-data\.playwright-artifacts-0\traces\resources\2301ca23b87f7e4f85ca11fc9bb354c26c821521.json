{"clientType": "Odsp", "ecsSettings": {"searchux.enableConvergedVivaQnAAnswer": true, "searchux.enableTriggerControlVivaEngageQnAAnswer": false, "searchux.enableTriggerControlVivaEngageNotificationAnswer": false, "searchux.serpVersion": "1.20250519.3.0", "searchux.overrideprcranking": true, "searchux.shouldUsePersonOverviewVerticalFromSharedSerp": true, "searchux.shouldUsePersonContentVerticalFromSharedSerp": true, "searchux.shouldUsePersonMessagesVerticalFromSharedSerp": true, "searchux.shouldUsePersonContactVerticalFromSharedSerp": true, "searchux.shouldUsePersonOrganizationVerticalFromSharedSerp": true, "searchux.shouldUsePersonScopedHeaderFromSharedSerp": true, "searchux.enableNew3sSdk": true, "useEcsSettings": true, "searchux.isUpdateProfileButtonEnabled": true, "searchux.enableSensitivityLabels": true, "searchux.optimizeSiteKQL": true, "searchux.substrateSearchClientFlightsV2": {"DisableSiteScopedQuery": "DisableSiteScopedQuery", "EnableClientContextIn3S": "EnableClientContextIn3S"}, "searchux.shouldFetchVerticalsFromDepartmentId": true, "searchux.cosmicThorEndpoint": "https://prod-config.thor.aesir.office.com", "searchux.tokenProviderURL": "https://outlook.office365.com/search", "searchux.serviceEnvironmentConfig": "Prod", "searchux.enableImprovedFilesKQL": true, "searchux.enableModifierUPNsForReadOnlyOpen": true, "searchux.teamsURL": "https://teams.microsoft.com", "searchux.enableConvergedBookmarkAnswer": true, "searchux.killSwitches.4bb41019-8cd9-4fbe-bd3d-1a2e84ce214e": true, "searchux.enableConvergedAnswerFramework": true, "searchux.enableSorting": true, "searchux.isSortingOnTheRight": true, "searchux.enableSortingWithConnectors": true, "searchux.enableColoredFolders": true, "searchux.enableImprovedFileAndPageLocation": true, "searchux.sortHubSiteActivities": true, "searchux.isWorkplaceSearchFeatureAwarenessEnabled": true, "searchux.enableBookmarkV2": true, "searchux.shouldUseNewInterleavingSchema": true, "searchux.enableFilePrivacyIndicator": true, "searchux.shouldUseConvergedPersonResult": true, "searchux.enableShortcutUrls": true, "searchux.shouldUseSiteAllVerticalFromSharedSerp": true, "searchux.shouldUseSiteFilesVerticalFromSharedSerp": true, "searchux.shouldUseSiteSitesVerticalFromSharedSerp": true, "searchux.shouldUseSiteImagesVerticalFromSharedSerp": true, "searchux.shouldUseSiteNewsVerticalFromSharedSerp": true, "searchux.shouldUseSiteCustomVerticalFromSharedSerp": true, "searchux.shouldUseSiteScopedHeaderFromSharedSerp": true, "searchux.enableSiteScopeClientSideRecourseLink": true, "searchux.useTopLevelRouter": true, "searchux.useVerticalNavigationFromSharedSerp": true, "searchux.answerBlock": true, "searchux.enable3SClientContext": true, "searchux.callUserConfigApi": true, "searchux.isTokenBasedProfileImagesEnabled": true, "searchux.enableContentSourceFilter": true, "searchux.enableContentSourceFilterOnUX": true, "searchux.loggableConnectionIds": "SharePoint,OneDriveBusiness,MicrosoftPowerBI,MicrosoftVivaEngage", "searchux.contentSourceFilterFieldNameChange": true, "searchux.allTabConnectorClusterViaAdminKnob": true, "searchux.enable3SEventsLogging": true, "searchux.isMetaPreviewEnabled": true, "searchux.redirectToPcsFromPeopleAnswer": true, "searchux.enableCustomSERPConfiguration": true, "searchux.UDTConnectorsClientFlights": "ConnectorExternalSearchFlight,EnableBingSpeller,FanoutBookmarksFlight,ShouldEnableDefaultModernResultTypes", "searchux.enableFileProtocolLinkCopyInteraction": true, "searchux.enableFileLikeUDTResults": true, "searchux.allTabConnectorClusterClientFlights": "ConnectorExternalSearchFlight,ShouldEnableDefaultModernResultTypes", "searchux.allTabConnectorClusterWithDefaultMRT": true, "searchux.enableCustomRefiners": true, "searchux.enableRefinerValuesCap": true, "searchux.refinerValuesCapLimit": 50, "searchux.enableCustomRefinersViaFlight": false, "searchux.pfp.preloadConfiguredRefiners": true, "searchux.enableAdaptiveCardTemplates": true, "searchux.prefetchAndWaitAdaptiveCardLibrary": true, "searchux.enableDotNetCoreEndPoint": true, "searchux.overrideConnectorClusterInterleaving": false, "searchux.allTabConnectorClusterEnableInterleaving": "ConnectorInterleaving,EnableConnectorsInterleavingGA", "searchux.connectorResultsPerCluster": 0, "searchux.consumeAdminUXSettingForCRE": true, "searchux.enableAdaptiveCardsInMobile": true, "searchux.isTopicBrandingEnabled": true, "searchux.isPCSOverviewVerticalEnabled": true, "searchux.isPeopleCentricSearchHeaderEnabled": true, "EnablePeopleCentricSearchController": false, "searchux.telemetryBoundary": "EU", "searchux.killSwitches.fd103984-5798-4920-987c-c6fe11cbbad1": false, "searchux.graphURLSetting": "https://graph.microsoft.com", "searchux.isTopicSdkEnabled": true, "searchux.enableSharePointResultsCustomization": true, "searchux.checkMRTConfigurations": true, "searchux.msbUrlOverride": "https://mss.office.com/api", "searchux.shouldUseNewPageBodyStructure": true, "searchux.renderSerpBelowPeopleAnswer": true, "searchux.tenantFeedback": true, "searchux.useOdsFeedback": true, "searchux.generalFeedback": true, "searchux.EnableResultAnnotations": true, "searchux.isPCSConversationVertical": true, "searchux.killSwitches.9f1996d5-b37a-43b3-8c24-f0f5d1a535d8": true, "searchux.killSwitches.81c82f22-412e-47f1-b57b-0c0489aa2afc": true, "searchux.shouldUseAllVerticalFromSharedSerp": true, "searchux.shouldUseFilesVerticalFromSharedSerp": true, "searchux.shouldUseSitesVerticalFromSharedSerp": true, "searchux.shouldUsePeopleVerticalFromSharedSerp": true, "searchux.shouldUseImagesVerticalFromSharedSerp": true, "searchux.enableServiceSideRecourseLink": true, "searchux.shouldForwardLegacyLogsToNewLogger": true, "searchux.shareIframeWithSharedSerp": true, "searchux.shouldUseCustomVerticalFromSharedSerp": true, "searchux.shouldUseNewsVerticalFromSharedSerp": true, "searchux.shouldUseMessagesVerticalFromSharedSerp": true, "searchux.shouldUseVideosVerticalFromSharedSerp": true, "searchux.noSourceIdForFiles": false, "searchux.isTopicTypesSchemaEnabled": false, "searchux.isTopicLabelV2Enabled": true, "searchux.isTopicVerticalEnabledInTopicCenterSearch": true, "searchux.isTopicClusterEnabledInTopicCenterSearch": true, "searchux.isTopicExternalLinkEnabled": true, "searchux.shouldUseConvergedAnswerStyles": true, "searchux.isNSATSurveyEnabled": true, "searchux.isNSATSurveyInProduction": true, "searchux.appIdValue": 2144, "searchux.isNSATSurveyOnMainOCV": true, "searchux.enableConfiguredOOBRefiners": true, "searchux.pfp.preloadRefinerPanel": false, "searchux.isConversationVerticalEnabled": true, "searchux.enableConversationResultsMerger": true, "searchux.isConversationEntityEnabled": false, "searchux.isMessagesUrlAliasingEnabled": false, "searchux.enableRelatedTopicsInTopicAnswer": true, "enableClientTypeFilter": false, "searchux.enableSharepointCustomVerticals": true, "searchux.enableVerticalsViaConfig": true, "enableVerticalsViaConfig": true, "searchux.enableQueryStringParam": true, "searchux.flightedCustomRefinerIds": "", "siteadmin.EnableSharePointAsAContentSource": true, "siteadmin.EnableOOBVerticalsinMicrosoftSearch": true, "searchux.enableMultiAnswers": true, "searchux.ignoreHeroDomainCheck": true, "searchux.waitForAnswersAndDeepSerp": true, "searchux.enableEnrichedRanking": true, "searchux.enableLayoutHints": true, "searchux.useV3WprSchema": true, "searchux.shouldStartQueryWithoutRoute": true, "searchux.pfp.shouldPreloadCreateRoutes": true, "searchux.queryDYMNoResultsImage": true, "searchux.queryDYMImage": false, "searchux.enableMrtCacheFlight": false, "searchux.enableRPCCall": true, "searchux.updateCacheInBackground": true, "searchux.isPeopleVerticalEnabled": true, "searchux.enableSPVHitHighlightMitigation": true, "searchux.isMSBPowerBIUserLicenseCheckEnabled": true, "searchux.fixResultsRenderedLogicalId": true, "searchux.webEqualsOneForPdf": true, "searchux.webEqualsOneQueryParameter": true, "searchux.fileFormatsOpenedInPreviewer": "url,pdf", "searchBoxPluginConfiguration.SearchBoxClearButton": null, "searchBoxPluginConfiguration.SearchBoxSubmitButton": null, "searchBoxPluginConfiguration.SearchBoxMagnifierButton": null, "searchBoxPluginConfiguration.SearchBoxHideableMagnifierButton": {"position": 10}, "searchBoxPluginConfiguration.SearchBoxExitButton": {"position": 20}, "searchBoxPluginConfiguration.SearchBoxHideableMagnifierSubmitButton": {"position": 120}, "searchux.exitSearchInSearchBox": true, "searchux.isDurationOnThumbnailEnabled": true, "searchux.isPeopleResultClusterEnabled": true, "searchux.useContextRegionClientLayout": true, "searchux.useComponentClientLayout": true, "searchux.isItemActionsMenuEnabled": true, "searchux.enableManageAccess": true, "searchux.fetchTuringQnA": true, "searchux.enableUserQualificationChecks": true, "userQualificationCheckTimeoutInMiliseconds": 1000, "searchux.pfp.msbObservableTimeout": 9000, "searchux.isImageVerticalEnabled": true, "searchux.useFileSpecificWithImage": true, "siteadmin.EnableMultipleConnectionSupportForVerticals": true, "siteadmin.EnableCaptionsInResultTypes": true, "siteadmin.EnableResultClusterForMicrosoftSearch": true, "siteadmin.isResultCaptionsInResultTypesEnabled": true, "searchux.externalContentPreview": true, "searchBoxLoggingDelay": 0, "searchux.enableAlteration": true, "searchux.queryDYMNL": true, "searchux.use3SAnswers": true, "searchux.answersWithoutIFrame": true, "searchux.rawMsbSubstrateToken": false, "searchux.enableQueryAnnotation": true, "searchBoxPluginConfiguration.SearchBoxPersonaPill": {"position": 40}, "suggestionProviderGroups.person": null, "suggestionProviderGroups.peoplepill": {"position": 40, "optimalCount": 2, "heading": "searchux.strings.suggestionGroup.person.heading", "component": "searchux.suggestionComponent.peoplePill.default", "ariaLabel": "searchux.strings.suggestionGroup.person.ariaLabel", "providers.autosuggestpeople": {"position": 10}}, "searchux.peopleCentricSearchV1Enabled": true, "searchux.isOfficeVerticalEnabled": false, "searchux.isCalendarVerticalEnabled": false, "searchux.isPeopleFlashinEnabledInPCS": true, "searchux.EnablePilledPersonShallowSorting": true, "searchux.EnablePeopleCentricSearchClientFlights": true, "searchux.EnablePeopleCentricSearchClientFlightsValues": "EnablePilledPersonShallowSorting,DisableFederationPaginationFlight", "searchux.isContextualRailEnabled": true, "searchux.constrainSERPVerticalWidth": "index,powerbi,conversation,files,sites,news,content", "searchux.isEmbeddedLPCEnabled": true, "searchux.shouldScopeToPersonOnClick": true, "searchux.noResults.useClearSearch": true, "searchux.excludeBestBets": true, "searchux.userRing": "Prod", "searchux.extension.fileClickTargetWrapper.openInPreviewer": "url,pdf", "searchux.enable3sAsyncSearchSupport": true, "searchux.fetchAdaptive": true, "searchux.fetchExternal": true, "searchux.enableAsyncAnswerInstrumentation": true, "searchux.use3sInPeopleVertical": true, "searchux.IsVROOMNewsPreviewEnabled": true, "searchux.isMSBPowerBIVerticalEnabled": true, "searchux.use3sPeople": true, "searchux.useRelatedHubs": true, "searchux.relatedSitesTrimDuplicates": true, "searchux.siteScopeBreadcrumbMaxItems": 10, "searchux.useLssViewCount": true, "searchux.log3sAnswersDiagnosticsInfo": true, "searchux.isLPCOnGroupClickEnabled": true, "searchux.showDYM": true, "searchux.spellerType": "SPOBingSpeller", "searchux.queryDYM": true, "searchux.queryDYMNoResults": true, "searchux.fetchAcronyms": true, "searchux.fetchTopics": true, "searchux.passFlightsToMsb": true, "serpConfigCacheTime": 360, "searchux.newMediaFileLocationResolution": true, "searchux.substrateDiagnosticsLogging": true, "searchux.itemExclusionKQL": "NOT ContentClass:\"STS_ListItem_UserInformation\"", "searchux.outlookUrl": "https://outlook.office.com", "searchBoxPluginConfiguration.BasicSuggestionProviderHost": {"position": 210}, "searchBoxPluginConfiguration.SubmitSearchSuggestion": {"position": 240}, "disableSharepointInfo": true, "enableDebugPanel": false, "searchBoxSuggestionProviderConfiguration": {"streamPolicy": "searchux.suggestionProvider.streamPolicy.eager", "groups": []}, "searchux.enableCultureOnSubstrateRequestBody": true, "searchux.enableTelemetryEventsForOobRefiners": true, "searchux.isMrtConfigured": false}, "flights": ["disablesharepointinfo", "enableverticalsviaconfig", "searchux.alltabconnectorclusterviaadminknob", "searchux.alltabconnectorclusterwithdefaultmrt", "searchux.answerblock", "searchux.answerswithoutiframe", "searchux.calluserconfigapi", "searchux.checkmrtconfigurations", "searchux.consumeadminuxsettingforcre", "searchux.contentsourcefilterfieldnamechange", "searchux.enable3sasyncsearchsupport", "searchux.enable3sclientcontext", "searchux.enable3seventslogging", "searchux.enableadaptivecardsinmobile", "searchux.enableadaptivecardtemplates", "searchux.enablealteration", "searchux.enableasyncanswerinstrumentation", "searchux.enablebookmarkv2", "searchux.enablecoloredfolders", "searchux.enableconfiguredoobrefiners", "searchux.enablecontentsourcefilter", "searchux.enablecontentsourcefilteronux", "searchux.enableconvergedanswerframework", "searchux.enableconvergedbookmarkanswer", "searchux.enableconvergedvivaqnaanswer", "searchux.enableconversationresultsmerger", "searchux.enablecultureonsubstraterequestbody", "searchux.enablecustomrefiners", "searchux.enablecustomserpconfiguration", "searchux.enabledotnetcoreendpoint", "searchux.enableenrichedranking", "searchux.enablefilelikeudtresults", "searchux.enablefileprivacyindicator", "searchux.enablefileprotocollinkcopyinteraction", "searchux.enableimprovedfileandpagelocation", "searchux.enableimprovedfileskql", "searchux.enablelayouthints", "searchux.enablemanageaccess", "searchux.enablemodifierupnsforreadonlyopen", "searchux.enablemultianswers", "searchux.enablenew3ssdk", "searchux.enablepeoplecentricsearchclientflights", "searchux.enablepilledpersonshallowsorting", "searchux.enablequeryannotation", "searchux.enablequerystringparam", "searchux.enablerefinervaluescap", "searchux.enablerelatedtopicsintopicanswer", "searchux.enableresultannotations", "searchux.enablerpccall", "searchux.enablesensitivitylabels", "searchux.enableservicesiderecourselink", "searchux.enablesharepointcustomverticals", "searchux.enablesharepointresultscustomization", "searchux.enableshortcuturls", "searchux.enablesitescopeclientsiderecourselink", "searchux.enablesorting", "searchux.enablesortingwithconnectors", "searchux.enablespvhithighlightmitigation", "searchux.enabletelemetryeventsforoobrefiners", "searchux.enableuserqualificationchecks", "searchux.enableverticalsviaconfig", "searchux.excludebestbets", "searchux.exitsearchinsearchbox", "searchux.externalcontentpreview", "searchux.fetchacronyms", "searchux.fetchadaptive", "searchux.fetchexternal", "searchux.fetchtopics", "searchux.fetchturingqna", "searchux.fixresultsrenderedlogicalid", "searchux.generalfeedback", "searchux.ignoreherodomaincheck", "searchux.iscontextualrailenabled", "searchux.isconversationverticalenabled", "searchux.isdurationonthumbnailenabled", "searchux.isembeddedlpcenabled", "searchux.isimageverticalenabled", "searchux.isitemactionsmenuenabled", "searchux.islpcongroupclickenabled", "searchux.ismetapreviewenabled", "searchux.ismsbpowerbiuserlicensecheckenabled", "searchux.ismsbpowerbiverticalenabled", "searchux.isnsatsurveyenabled", "searchux.isnsatsurveyinproduction", "searchux.isnsatsurveyonmainocv", "searchux.ispcsconversationvertical", "searchux.ispcsoverviewverticalenabled", "searchux.ispeoplecentricsearchheaderenabled", "searchux.ispeopleflashinenabledinpcs", "searchux.ispeopleresultclusterenabled", "searchux.ispeopleverticalenabled", "searchux.issortingontheright", "searchux.istokenbasedprofileimagesenabled", "searchux.istopicbrandingenabled", "searchux.istopicclusterenabledintopiccentersearch", "searchux.istopicexternallinkenabled", "searchux.istopiclabelv2enabled", "searchux.istopicsdkenabled", "searchux.istopicverticalenabledintopiccentersearch", "searchux.isupdateprofilebuttonenabled", "searchux.isvroomnewspreviewenabled", "searchux.isworkplacesearchfeatureawarenessenabled", "searchux.killswitches.4bb41019-8cd9-4fbe-bd3d-1a2e84ce214e", "searchux.killswitches.81c82f22-412e-47f1-b57b-0c0489aa2afc", "searchux.killswitches.9f1996d5-b37a-43b3-8c24-f0f5d1a535d8", "searchux.log3sanswersdiagnosticsinfo", "searchux.newmediafilelocationresolution", "searchux.noresults.useclearsearch", "searchux.optimizesitekql", "searchux.overrideprcranking", "searchux.passflightstomsb", "searchux.peoplecentricsearchv1enabled", "searchux.pfp.preloadconfiguredrefiners", "searchux.pfp.shouldpreloadcreateroutes", "searchux.prefetchandwaitadaptivecardlibrary", "searchux.querydym", "searchux.querydymnl", "searchux.querydymnoresults", "searchux.querydymnoresultsimage", "searchux.redirecttopcsfrompeopleanswer", "searchux.relatedsitestrimduplicates", "searchux.renderserpbelowpeopleanswer", "searchux.shareiframewithsharedserp", "searchux.shouldfetchverticalsfromdepartmentid", "searchux.shouldforwardlegacylogstonewlogger", "searchux.shouldscopetopersononclick", "searchux.shouldstartquerywithoutroute", "searchux.shoulduseallverticalfromsharedserp", "searchux.shoulduseconvergedanswerstyles", "searchux.shoulduseconvergedpersonresult", "searchux.shouldusecustomverticalfromsharedserp", "searchux.shouldusefilesverticalfromsharedserp", "searchux.shoulduseimagesverticalfromsharedserp", "searchux.shouldusemessagesverticalfromsharedserp", "searchux.shouldusenewinterleavingschema", "searchux.shouldusenewpagebodystructure", "searchux.shouldusenewsverticalfromsharedserp", "searchux.shouldusepeopleverticalfromsharedserp", "searchux.shouldusepersoncontactverticalfromsharedserp", "searchux.shouldusepersoncontentverticalfromsharedserp", "searchux.shouldusepersonmessagesverticalfromsharedserp", "searchux.shouldusepersonorganizationverticalfromsharedserp", "searchux.shouldusepersonoverviewverticalfromsharedserp", "searchux.shouldusepersonscopedheaderfromsharedserp", "searchux.shouldusesiteallverticalfromsharedserp", "searchux.shouldusesitecustomverticalfromsharedserp", "searchux.shouldusesitefilesverticalfromsharedserp", "searchux.shouldusesiteimagesverticalfromsharedserp", "searchux.shouldusesitenewsverticalfromsharedserp", "searchux.shouldusesitescopedheaderfromsharedserp", "searchux.shouldusesitesitesverticalfromsharedserp", "searchux.shouldusesitesverticalfromsharedserp", "searchux.shouldusevideosverticalfromsharedserp", "searchux.showdym", "searchux.sorthubsiteactivities", "searchux.substratediagnosticslogging", "searchux.tenantfeedback", "searchux.updatecacheinbackground", "searchux.use3sanswers", "searchux.use3sinpeoplevertical", "searchux.use3speople", "searchux.usecomponentclientlayout", "searchux.usecontextregionclientlayout", "searchux.usefilespecificwithimage", "searchux.uselssviewcount", "searchux.useodsfeedback", "searchux.userelatedhubs", "searchux.usetoplevelrouter", "searchux.usev3wprschema", "searchux.useverticalnavigationfromsharedserp", "searchux.waitforanswersanddeepserp", "searchux.webequalsoneforpdf", "searchux.webequalsonequeryparameter", "siteadmin.enablecaptionsinresulttypes", "siteadmin.enablemultipleconnectionsupportforverticals", "siteadmin.enableoobverticalsinmicrosoftsearch", "siteadmin.enableresultclusterformicrosoftsearch", "siteadmin.enablesharepointasacontentsource", "siteadmin.isresultcaptionsinresulttypesenabled", "useecssettings"], "userAadObjectId": "3c6c9030-6ba2-42cd-9593-93723e717819", "tenantAadObjectId": "deff24bb-2089-4400-8c8e-f71e680378b2", "userPuid": "10032001F9BFF1F1", "ecsEtag": "\"2My4X+Eu8dMxAs6M1hQT6xND/1aO8Ga5thh7iMjp1DM=\"", "ecsConfigIds": ["P-R-1054961-1-5,P-R-1087953-1-1", "P-E-1262888-2-3,P-E-1264727-C1-4,P-R-1000561-4-455,P-R-1568955-4-7,P-R-1547330-4-11,P-R-1517381-4-5,P-R-1472558-4-3,P-R-1425225-11-27,P-R-1290478-4-6,P-R-1277141-4-4,P-R-1266138-11-3,P-R-1257332-4-6,P-R-1254770-11-3,P-R-1254763-4-3,P-R-1245486-4-4,P-R-1243878-4-3,P-R-1235408-4-5,P-R-1233590-4-5,P-R-1226368-1-3,P-R-1223877-4-4,P-R-1191403-4-8,P-R-1175053-4-4,P-R-1173833-4-3,P-R-1161809-4-3,P-R-1161451-4-4,P-R-1160875-4-7,P-R-1158899-4-9,P-R-1158020-4-6,P-R-1152655-4-14,P-R-1151591-4-5,P-R-1084726-4-26,P-R-1146108-4-3,P-R-1128015-4-10,P-R-1092097-4-44,P-R-73278-14-11,P-R-1113593-4-39,P-R-1065942-4-6,P-R-1087266-4-33,P-R-1081185-4-2,P-R-1072147-12-7,P-R-1065934-4-8,P-R-1065935-4-2,P-R-1056220-4-5,P-R-116889-16-96,P-R-1043619-4-6,P-R-1048746-4-3,P-R-1047186-4-17,P-R-1039673-11-7,P-R-1036816-2-17,P-R-1036795-1-6,P-R-1035352-4-7,P-R-102223-7-108,P-R-1018027-4-10,P-R-1028005-4-10,P-R-1025739-4-3,P-R-1025564-4-3,P-R-1020457-4-3,P-R-1019718-4-10,P-R-1017675-11-7,P-R-1015193-4-55,P-R-1016878-11-7,P-R-1013345-4-12,P-R-1004431-6-9,P-R-1003618-4-6,P-R-1003339-4-6,P-R-1000354-4-13,P-R-117697-7-16,P-R-116582-3-10,P-R-116428-4-33,P-R-114066-7-35,P-R-113159-7-38,P-R-112867-4-9,P-R-110459-2-19,P-R-106802-3-77,P-R-106259-3-78,P-R-102579-4-17,P-R-100154-2-7,P-R-98397-4-5,P-R-85485-12-3,P-R-89633-15-33,P-R-89076-4-25,P-R-88095-1-2,P-R-87704-1-2,P-R-86415-6-7,P-R-81904-12-23,P-R-77837-4-5,P-R-75407-13-18,P-R-62830-6-40,P-R-74417-4-11,P-R-73158-4-19,P-R-72227-4-15,P-R-71200-14-47,P-R-69872-4-7,P-R-69732-4-11,P-R-69532-12-29,P-R-68771-3-75,P-R-67741-3-81,P-R-66065-8-50,P-R-61827-4-7,P-R-60693-1-10,P-R-59942-3-2,P-R-59554-4-7,P-R-59063-4-18,P-R-59052-20-150,P-R-58858-4-11,P-R-58741-4-5,P-R-58739-4-15,P-R-58234-4-21,P-R-58074-4-18,P-R-55729-4-10,P-R-55502-4-13,P-R-55148-4-18,P-R-54782-4-15,P-R-54363-1-5,P-R-51700-4-5,P-R-51405-4-8,P-R-47530-4-14,P-R-47293-4-18,P-R-46545-4-56,P-R-46465-4-5,P-R-45563-1-8,P-R-45559-6-3,P-R-43604-6-6,P-R-43263-6-10,P-D-1610708-4-1,P-D-1601929-4-1,P-D-1558603-4-1,P-D-68653-1-20,P-D-31828-5-66"], "upn": "jagan<PERSON><EMAIL>", "lokiUrl": "https://apc-cosmic.thor.aesir.office.com/", "workplaceSearchServiceUrl": "https://apc-cosmic.thor.aesir.office.com/", "verticalConfig": [{"id": "siteall", "displayName": "", "queryTemplate": "", "state": 1, "verticalType": 0, "extendedQueryTemplate": "", "entities": [{"contentSources": [{"id": "All", "name": "All", "maxResultsPercent": 0}], "entityType": "File", "refinerIds": ["************************************", "613e3206-63e8-4781-9ff5-ee56205b663e"]}], "refiners": [{"id": "************************************", "displayName": "", "state": 1, "category": 0, "layout": {"mappedProperties": [], "fieldName": "FileType", "type": 0, "values": [], "displayInterface": 1, "manualEntryEnabled": false, "showCount": false}}, {"id": "613e3206-63e8-4781-9ff5-ee56205b663e", "displayName": "", "state": 1, "category": 0, "layout": {"mappedProperties": [], "fieldName": "LastModifiedTime", "type": 2, "values": [], "displayInterface": 0, "manualEntryEnabled": true, "showCount": false}}], "scope": 1, "allowedActions": 0, "includeConnectorResults": true, "isMrtConfigured": false}, {"id": "sitefiles", "displayName": "", "queryTemplate": "", "state": 1, "verticalType": 0, "extendedQueryTemplate": "", "entities": [{"contentSources": [{"id": "SharePoint", "name": "SharePoint", "maxResultsPercent": 0}], "entityType": "File", "refinerIds": ["************************************", "405346eb-90e9-48dd-a736-ffe60184e9a0"]}], "refiners": [{"id": "************************************", "displayName": "", "state": 1, "category": 0, "layout": {"mappedProperties": [], "fieldName": "FileType", "type": 0, "values": [], "displayInterface": 1, "manualEntryEnabled": false, "showCount": false}}, {"id": "405346eb-90e9-48dd-a736-ffe60184e9a0", "displayName": "", "state": 1, "category": 0, "layout": {"mappedProperties": [], "fieldName": "LastModifiedTime", "type": 2, "values": [], "displayInterface": 0, "manualEntryEnabled": true, "showCount": false}}], "scope": 1, "allowedActions": 0, "includeConnectorResults": false, "isMrtConfigured": false}, {"id": "sitesites", "displayName": "", "queryTemplate": "", "state": 1, "verticalType": 0, "extendedQueryTemplate": "", "entities": [{"contentSources": [{"id": "SharePoint", "name": "SharePoint", "maxResultsPercent": 0}], "entityType": "File", "refinerIds": []}], "refiners": [], "scope": 1, "allowedActions": 0, "includeConnectorResults": false, "isMrtConfigured": false}, {"id": "sitenews", "displayName": "", "queryTemplate": "", "state": 1, "verticalType": 0, "extendedQueryTemplate": "", "entities": [{"contentSources": [{"id": "SharePoint", "name": "SharePoint", "maxResultsPercent": 0}], "entityType": "File", "refinerIds": ["************************************"]}], "refiners": [{"id": "************************************", "displayName": "", "state": 1, "category": 0, "layout": {"mappedProperties": [], "fieldName": "LastModifiedTime", "type": 2, "values": [], "displayInterface": 0, "manualEntryEnabled": true, "showCount": false}}], "scope": 1, "allowedActions": 0, "includeConnectorResults": false, "isMrtConfigured": false}, {"id": "siteimages", "displayName": "", "queryTemplate": "", "state": 1, "verticalType": 0, "extendedQueryTemplate": "", "entities": [{"contentSources": [{"id": "SharePoint", "name": "SharePoint", "maxResultsPercent": 0}], "entityType": "External", "refinerIds": ["************************************"]}, {"contentSources": [{"id": "OneDriveBusiness", "name": "OneDriveBusiness", "maxResultsPercent": 0}], "entityType": "External", "refinerIds": ["************************************"]}], "refiners": [{"id": "************************************", "displayName": "", "state": 1, "category": 0, "layout": {"mappedProperties": [], "fieldName": "LastModifiedTime", "type": 2, "values": [], "displayInterface": 0, "manualEntryEnabled": true, "showCount": false}}], "scope": 1, "allowedActions": 0, "includeConnectorResults": false, "isMrtConfigured": false}], "ocpsPolicies": {"connectedExperiences": true, "policyAllowFeedback": false, "policyAllowSurvey": true, "policyAllowScreenshot": false, "policyAllowContact": false, "policyAllowContent": false}, "environment": "prod"}