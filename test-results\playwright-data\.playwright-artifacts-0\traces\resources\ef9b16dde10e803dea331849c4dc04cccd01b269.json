{"EntityRequests": [{"EntityType": "File", "ContentSources": ["SharePoint"], "From": 0, "Fields": [".callerStack", ".correlationId", ".mediaBaseUrl", ".spResourceUrl", ".thumbnailUrl", "AuthorOWSUSER", "ContentClass", "ContentTypeId", "Created", "DefaultEncodingURL", "DepartmentId", "Description", "DocId", "EditorOWSUSER", "FileExtension", "Filename", "FileType", "GeoLocationSource", "HitHighlightedSummary", "isDocument", "isexternalcontent", "IsHubSite", "LastModifiedTime", "ListID", "ListTemplateTypeId", "MediaDuration", "ModifiedBy", "ParentLink", "Path", "PictureThumbnailURL", "PiSearchResultId", "ProgID", "PromotedState", "RelatedHubSites", "SecondaryFileExtension", "ServerRedirectedPreviewURL", "ServerRedirectedUrl", "ShortcutUrl", "SiteId", "SiteLogo", "SiteTemplateId", "SiteTitle", "SPSiteUrl", "SPWebUrl", "Title", "UniqueID", "UniqueId", "ViewsLifeTimeUniqueUsers", "WebId", "ColorHex", "ModifierUPNs", "InformationProtectionLabelId", "SiteSensitivityLabelID"], "Size": 15, "Query": {"QueryString": "kpmg", "DisplayQueryString": "kpmg", "QueryTemplate": "({searchterms}) (NOT ContentClass:ExternalLink AND NOT FileExtension:vtt AND NOT (Title:OneNote_DeletedPages OR Title:OneNote_RecycleBin) AND NOT SecondaryFileExtension:onetoc2 AND NOT (ContentClass:STS_List_544 OR ContentClass:STS_ListItem_544) AND NOT WebTemplate:SPSPERS AND NOT (ContentClass:STS_Site AND SiteTemplateId:21) AND NOT (ContentClass:STS_Site AND SiteTemplateId:22) AND NOT (ContentClass:STS_List_DocumentLibrary AND SiteTemplateId:21) AND NOT (ContentClass:STS_List_DocumentLibrary AND Author:\"system account\")) AND (DepartmentId:36072d22-c34a-430f-96a5-6f4e3e097513 OR RelatedHubSites:36072d22-c34a-430f-96a5-6f4e3e097513) NOT ContentClass:\"STS_ListItem_UserInformation\""}, "Sort": [{"Field": "PersonalScore", "SortDirection": "Desc"}], "EnableQueryUnderstanding": false, "EnableSpeller": false, "IdFormat": 0, "EnableResultAnnotations": true, "FederationContext": {"SpoFederationContext": {"UserContextUrl": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch"}}, "ExtendedQueries": [{"SearchProvider": "SharePoint", "Query": {"Culture": 1033, "EnableQueryRules": false, "EnableMultiGeo": true, "TrimDuplicates": false, "BypassResultTypes": true, "ProcessBestBets": false, "ProcessPersonalFavorites": false, "EnableInterleaving": false, "SourceId": "8413CD39-2156-4E00-B54D-11EFD9ABDB89", "TimeSpanToUTC": "05:30"}}], "HitHighlight": {"HitHighlightedProperties": ["Title"], "SummaryLength": 200}}, {"EntityType": "Bookmark", "ContentSources": ["SharePoint"], "From": 0, "Fields": ["piSearchResultid"], "Size": 100, "Query": {"QueryString": "kpmg", "DisplayQueryString": "kpmg"}, "EnableQueryUnderstanding": false, "FederationContext": {"SpoFederationContext": {"UserContextUrl": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch"}}, "ExtendedQueries": [{"SearchProvider": "SharePoint", "Query": {"Culture": "1033", "EnableMultiGeo": true}}]}, {"EntityType": "External", "ContentSources": ["Connectors"], "From": 0, "Fields": [], "Size": 2, "Query": {"QueryString": "kpmg", "DisplayQueryString": "kpmg"}, "Sort": [{"Field": "Score", "SortDirection": "Desc"}], "BypassResultTypes": false, "PreferredResultSourceFormat": "AdaptiveCardTemplateBinding", "FederationContext": {"SpoFederationContext": {"UserContextUrl": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch", "Endpoints": [{"SearchFarmSpoTarget": {"PortalUrl": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch", "SiteId": "36072d22-c34a-430f-96a5-6f4e3e097513"}}]}}, "HitHighlight": {"HitHighlightedProperties": ["HitHighlightedSummary"], "SummaryLength": 200}}], "TextDecorations": "Off", "Cvid": "6cb08fc7-9ccd-2e51-d3b7-3048aafa3841", "LogicalId": "ca1c4799-b61c-1695-1ac5-f99f065bbd02", "Culture": "en-us", "UICulture": "en-us", "TimeZone": "UTC", "Context": {"ClientContext": {"SharePoint": {"SiteId": "36072d22-c34a-430f-96a5-6f4e3e097513", "WebId": "4754d42b-7943-4e09-a0ad-e06158af2627", "HubId": "36072d22-c34a-430f-96a5-6f4e3e097513"}}}, "Scenario": {"Name": "HubSiteSearch", "Dimensions": [{"DimensionName": "QueryType", "DimensionValue": "AllSiteResults"}, {"DimensionName": "FormFactor", "DimensionValue": "Web"}]}, "QueryAlterationOptions": {"EnableSuggestion": true, "EnableAlteration": true, "SupportedRecourseDisplayTypes": ["Suggestion", "NoResultModification", "NoRequeryModification"]}}