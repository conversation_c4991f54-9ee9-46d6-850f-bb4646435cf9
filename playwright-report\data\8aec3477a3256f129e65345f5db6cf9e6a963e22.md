# Test info

- Name: SPO Health Check >> TC0101- Login and Check the Default Data Source Order
- Location: C:\workspace\Playwright_Automation_Framework\tests\cucumber-wrapper.spec.ts:34:11

# Error details

```
TimeoutError: locator.waitFor: Timeout 30000ms exceeded.
Call log:
  - waiting for getBy<PERSON>abel('Datasources') to be visible
    62 × locator resolved to hidden <div class="" role="dialog" aria-modal="true" aria-labelledby="Panel137-headerText">…</div>

    at PlaywrightWorld.waitForElement (C:\workspace\Playwright_Automation_Framework\src\core\playwright-world.ts:250:19)
    at Object.handler (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:170:21)
    at StepDefinitionRegistry.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:310:23)
    at PlaywrightCucumberRunner.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:196:31)
    at PlaywrightCucumberRunner.executeScenario (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:231:20)
    at C:\workspace\Playwright_Automation_Framework\tests\cucumber-wrapper.spec.ts:45:9
```

# Test source

```ts
  150 |       }
  151 |     } else {
  152 |       return this.page.getByText(text);
  153 |     }
  154 |   }
  155 |
  156 |   /**
  157 |    * Parse getByLabel locator
  158 |    */
  159 |   private parseGetByLabel(selector: string) {
  160 |     const match = selector.match(/getByLabel\('([^']+)'(?:,\s*({[^}]+}))?\)/);
  161 |     if (!match) {
  162 |       throw new Error(`Invalid getByLabel syntax: ${selector}`);
  163 |     }
  164 |
  165 |     const label = match[1];
  166 |     const optionsStr = match[2];
  167 |
  168 |     if (optionsStr) {
  169 |       try {
  170 |         const options = eval(`(${optionsStr})`);
  171 |         return this.page.getByLabel(label, options);
  172 |       } catch (error) {
  173 |         return this.page.getByLabel(label);
  174 |       }
  175 |     } else {
  176 |       return this.page.getByLabel(label);
  177 |     }
  178 |   }
  179 |
  180 |   /**
  181 |    * Parse getByPlaceholder locator
  182 |    */
  183 |   private parseGetByPlaceholder(selector: string) {
  184 |     const match = selector.match(/getByPlaceholder\('([^']+)'(?:,\s*({[^}]+}))?\)/);
  185 |     if (!match) {
  186 |       throw new Error(`Invalid getByPlaceholder syntax: ${selector}`);
  187 |     }
  188 |
  189 |     const placeholder = match[1];
  190 |     const optionsStr = match[2];
  191 |
  192 |     if (optionsStr) {
  193 |       try {
  194 |         const options = eval(`(${optionsStr})`);
  195 |         return this.page.getByPlaceholder(placeholder, options);
  196 |       } catch (error) {
  197 |         return this.page.getByPlaceholder(placeholder);
  198 |       }
  199 |     } else {
  200 |       return this.page.getByPlaceholder(placeholder);
  201 |     }
  202 |   }
  203 |
  204 |   /**
  205 |    * Parse getByTestId locator
  206 |    */
  207 |   private parseGetByTestId(selector: string) {
  208 |     const match = selector.match(/getByTestId\('([^']+)'\)/);
  209 |     if (!match) {
  210 |       throw new Error(`Invalid getByTestId syntax: ${selector}`);
  211 |     }
  212 |
  213 |     const testId = match[1];
  214 |     return this.page.getByTestId(testId);
  215 |   }
  216 |
  217 |   /**
  218 |    * Click on element
  219 |    */
  220 |   async clickElement(selector: string): Promise<void> {
  221 |     this.logger.info(`🖱️ Clicking: ${selector}`);
  222 |     const element = this.parseLocator(selector);
  223 |     await element.click();
  224 |   }
  225 |
  226 |   /**
  227 |    * Fill input field
  228 |    */
  229 |   async fillField(selector: string, value: string): Promise<void> {
  230 |     this.logger.info(`⌨️ Filling ${selector} with: ${value}`);
  231 |     const element = this.parseLocator(selector);
  232 |     await element.fill(value);
  233 |   }
  234 |
  235 |   /**
  236 |    * Type in input field
  237 |    */
  238 |   async typeInField(selector: string, value: string): Promise<void> {
  239 |     this.logger.info(`⌨️ Typing ${value} in: ${selector}`);
  240 |     const element = this.parseLocator(selector);
  241 |     await element.type(value);
  242 |   }
  243 |
  244 |   /**
  245 |    * Wait for element to be visible
  246 |    */
  247 |   async waitForElement(selector: string, timeout: number = 30000): Promise<void> {
  248 |     this.logger.info(`⏳ Waiting for element: ${selector}`);
  249 |     const element = this.parseLocator(selector);
> 250 |     await element.waitFor({ state: 'visible', timeout });
      |                   ^ TimeoutError: locator.waitFor: Timeout 30000ms exceeded.
  251 |   }
  252 |
  253 |   /**
  254 |    * Check if element is visible
  255 |    */
  256 |   async isElementVisible(selector: string): Promise<boolean> {
  257 |     try {
  258 |       const element = this.parseLocator(selector);
  259 |       return await element.isVisible();
  260 |     } catch {
  261 |       return false;
  262 |     }
  263 |   }
  264 |
  265 |   /**
  266 |    * Get element text
  267 |    */
  268 |   async getElementText(selector: string): Promise<string> {
  269 |     const element = this.parseLocator(selector);
  270 |     return await element.textContent() || '';
  271 |   }
  272 |
  273 |   /**
  274 |    * Press keyboard key
  275 |    */
  276 |   async pressKey(key: string): Promise<void> {
  277 |     this.logger.info(`⌨️ Pressing key: ${key}`);
  278 |     await this.page.keyboard.press(key);
  279 |   }
  280 |
  281 |   /**
  282 |    * Scroll to element
  283 |    */
  284 |   async scrollToElement(selector: string): Promise<void> {
  285 |     this.logger.info(`📜 Scrolling to: ${selector}`);
  286 |     const element = this.parseLocator(selector);
  287 |     await element.scrollIntoViewIfNeeded();
  288 |   }
  289 |
  290 |   /**
  291 |    * Wait for specified time
  292 |    */
  293 |   async wait(milliseconds: number): Promise<void> {
  294 |     this.logger.info(`⏳ Waiting for ${milliseconds}ms`);
  295 |     await this.page.waitForTimeout(milliseconds);
  296 |   }
  297 |
  298 |   /**
  299 |    * Verify text is present on page
  300 |    */
  301 |   async verifyTextPresent(text: string): Promise<void> {
  302 |     this.logger.info(`🔍 Verifying page contains: ${text}`);
  303 |     const bodyLocator = this.page.locator('body');
  304 |     await expect(bodyLocator).toContainText(text);
  305 |   }
  306 |
  307 |   /**
  308 |    * Verify element is visible
  309 |    */
  310 |   async verifyElementVisible(selector: string): Promise<void> {
  311 |     this.logger.info(`👁️ Verifying element is visible: ${selector}`);
  312 |     const element = this.parseLocator(selector);
  313 |     await expect(element).toBeVisible();
  314 |   }
  315 |
  316 |   /**
  317 |    * Verify page title contains text
  318 |    */
  319 |   async verifyTitleContains(text: string): Promise<void> {
  320 |     this.logger.info(`📄 Verifying title contains: ${text}`);
  321 |     const title = await this.getTitle();
  322 |     if (!title.includes(text)) {
  323 |       throw new Error(`Expected title to contain "${text}", but got "${title}"`);
  324 |     }
  325 |   }
  326 |
  327 |   /**
  328 |    * Verify element contains text
  329 |    */
  330 |   async verifyElementContainsText(selector: string, text: string): Promise<void> {
  331 |     this.logger.info(`🔍 Verifying ${selector} contains: ${text}`);
  332 |     const element = this.parseLocator(selector);
  333 |     await expect(element).toContainText(text);
  334 |   }
  335 | }
  336 |
```