
@combined @spo
Feature: SPO Health Check
  Data Source - Health Check - General
  en
  Background:
    # Store credentials for later use
    Given I store "jagannath<PERSON>@kpmg.com" as "USERNAME"

  Scenario: TC0101- Login and Check the Default Data Source Order
    Given I navigate to URL "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/"
    Then I should be in private/incognito mode
    Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000
    When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>"
    And I click on "getByRole('button', { name: 'Next' })"
    Then I wait for element "getByRole('link', { name: 'OI Development', exact: true })" to be visible with timeout 60000
    And I wait for page title to contain "OI Development - Home"
    Then I wait for element "getByRole('combobox', { name: 'Search box. Suggestions' })" to be visible with timeout 60000
    When I fill "getByRole('combobox', { name: 'Search box. Suggestions' })" with "kpmg"
    And I press "Enter"
    And I wait for page title to contain "Results"
    Then I wait for element "getByRole('link', { name: 'Organizational Logo' })" to be visible with timeout 60000
    Then I wait for "getByRole('button', { name: 'All', exact: true })" to be visible with timeout 30000
    When I click on "getByRole('button', { name: 'All', exact: true })"
    Then I wait for "getByRole('heading', { name: 'Datasources' })" to be visible with timeout 30000
    Then "getByRole('heading', { name: 'Datasources' })" should contain text "Datasources"
    And I wait for "getByLabel('Datasources').getByText('Global', { exact: true })" to be visible with timeout 30000
    And "getByLabel('Datasources').getByText('Global', { exact: true })" should contain text "Global"
    When I click on "getByLabel('Datasources').getByText('Global', { exact: true })"
    Then the URL parameter "datasource" should be "intranet"
    Then the URL parameter "origin" should be "global"
    When I click on "getByRole('button', { name: 'All', exact: true })"
    Then I wait for "getByRole('heading', { name: 'Datasources' })" to be visible with timeout 30000
    And I wait for "getByLabel('Datasources').getByText('Portal', { exact: true })" to be visible with timeout 30000
    And "getByLabel('Datasources').getByText('Portal', { exact: true })" should contain text "Portal"
    When I click on "getByLabel('Datasources').getByText('Portal', { exact: true })"
    Then the URL parameter "datasource" should be "intranet"
    Then the URL parameter "origin" should be "portal"
    When I click on "getByRole('button', { name: 'All', exact: true })"
    Then I wait for "getByRole('heading', { name: 'Datasources' })" to be visible with timeout 30000
    And I wait for "getByLabel('Datasources').getByText('Site', { exact: true })" to be visible with timeout 30000
    And "getByLabel('Datasources').getByText('Site', { exact: true })" should contain text "Site"
    When I click on "getByLabel('Datasources').getByText('Site', { exact: true })"
    Then the URL parameter "datasource" should be "intranet"
    Then the URL parameter "origin" should be "site"