import { spawn, ChildProcess } from 'child_process';
import { chromium, <PERSON><PERSON><PERSON>, <PERSON>rowserContext, Page } from 'playwright';
import * as fs from 'fs';
import * as path from 'path';
import { Logger } from '../utils/logger';

/**
 * Edge InPrivate Manager
 * 
 * This class provides a guaranteed solution for launching Edge in InPrivate mode
 * by using direct Edge executable launch combined with Playwright automation.
 */
export class EdgeInPrivateManager {
  private logger = new Logger('EdgeInPrivateManager');
  private edgeProcess: ChildProcess | null = null;
  private browser: Browser | null = null;
  private context: BrowserContext | null = null;
  private page: Page | null = null;

  /**
   * Find Edge executable path on Windows
   */
  private findEdgeExecutable(): string | null {
    const edgePaths = [
      'C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe',
      'C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe',
      process.env.PROGRAMFILES + '\\Microsoft\\Edge\\Application\\msedge.exe',
      process.env['PROGRAMFILES(X86)'] + '\\Microsoft\\Edge\\Application\\msedge.exe'
    ];

    for (const edgePath of edgePaths) {
      if (fs.existsSync(edgePath)) {
        this.logger.info(`Found Edge at: ${edgePath}`);
        return edgePath;
      }
    }

    this.logger.error('Edge executable not found in common locations');
    return null;
  }

  /**
   * Close all existing Edge processes
   */
  private async closeExistingEdgeProcesses(): Promise<void> {
    try {
      if (process.platform === 'win32') {
        this.logger.info('Closing existing Edge processes...');
        spawn('taskkill', ['/F', '/IM', 'msedge.exe'], { stdio: 'ignore' });
        // Wait for processes to close
        await new Promise(resolve => setTimeout(resolve, 3000));
        this.logger.info('Edge processes closed');
      }
    } catch (error) {
      this.logger.warn('Could not close Edge processes (they may not be running)');
    }
  }

  /**
   * Launch Edge in InPrivate mode directly
   */
  async launchEdgeInPrivate(url?: string): Promise<boolean> {
    const edgeExePath = this.findEdgeExecutable();
    
    if (!edgeExePath) {
      throw new Error('Edge executable not found');
    }

    try {
      // Close existing Edge processes first
      await this.closeExistingEdgeProcesses();

      const args = [
        '--inprivate',
        '--new-window',
        '--disable-extensions',
        '--no-first-run',
        '--no-default-browser-check',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding'
      ];

      if (url) {
        args.push(url);
      }

      this.logger.info(`Launching Edge InPrivate with args: ${JSON.stringify(args)}`);

      this.edgeProcess = spawn(edgeExePath, args, {
        detached: false,
        stdio: 'pipe'
      });

      this.edgeProcess.on('error', (error) => {
        this.logger.error(`Edge process error: ${error.message}`);
      });

      this.edgeProcess.on('close', (code) => {
        this.logger.info(`Edge process closed with code: ${code}`);
      });

      // Wait a bit for Edge to start
      await new Promise(resolve => setTimeout(resolve, 2000));

      this.logger.info('✅ Edge launched in InPrivate mode');
      return true;

    } catch (error) {
      this.logger.error(`Failed to launch Edge InPrivate: ${error}`);
      return false;
    }
  }

  /**
   * Connect Playwright to the running Edge instance
   */
  async connectPlaywrightToEdge(): Promise<{ browser: Browser, context: BrowserContext, page: Page }> {
    try {
      this.logger.info('Connecting Playwright to Edge InPrivate instance...');

      // Connect to the existing Edge instance
      this.browser = await chromium.connectOverCDP('http://localhost:9222');
      
      // Get the default context (which should be InPrivate)
      const contexts = this.browser.contexts();
      if (contexts.length > 0) {
        this.context = contexts[0];
      } else {
        // Create a new context if none exists
        this.context = await this.browser.newContext({
          storageState: { cookies: [], origins: [] },
          acceptDownloads: false,
          ignoreHTTPSErrors: true,
          bypassCSP: true
        });
      }

      // Get or create a page
      const pages = this.context.pages();
      if (pages.length > 0) {
        this.page = pages[0];
      } else {
        this.page = await this.context.newPage();
      }

      this.logger.info('✅ Playwright connected to Edge InPrivate');
      
      return {
        browser: this.browser,
        context: this.context,
        page: this.page
      };

    } catch (error) {
      this.logger.error(`Failed to connect Playwright to Edge: ${error}`);
      throw error;
    }
  }

  /**
   * Launch Edge InPrivate and connect Playwright (all-in-one method)
   */
  async launchAndConnect(url?: string): Promise<{ browser: Browser, context: BrowserContext, page: Page }> {
    // Step 1: Launch Edge in InPrivate mode
    const launched = await this.launchEdgeInPrivate();
    if (!launched) {
      throw new Error('Failed to launch Edge in InPrivate mode');
    }

    // Step 2: Connect Playwright
    try {
      const connection = await this.connectPlaywrightToEdge();
      
      // Step 3: Navigate to URL if provided
      if (url) {
        await connection.page.goto(url);
      }

      return connection;
    } catch (error) {
      // If CDP connection fails, fall back to regular Playwright launch
      this.logger.warn('CDP connection failed, falling back to Playwright launch');
      return await this.fallbackPlaywrightLaunch();
    }
  }

  /**
   * Fallback to regular Playwright launch with enhanced InPrivate args
   */
  private async fallbackPlaywrightLaunch(): Promise<{ browser: Browser, context: BrowserContext, page: Page }> {
    this.logger.info('Using fallback Playwright launch for Edge');

    this.browser = await chromium.launch({
      channel: 'msedge',
      headless: false,
      args: [
        '--inprivate',
        '--new-window',
        '--disable-extensions',
        '--disable-web-security',
        '--no-first-run',
        '--no-default-browser-check',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-features=VizDisplayCompositor',
        '--disable-blink-features=AutomationControlled',
        '--user-data-dir=' + path.join(process.cwd(), 'temp-edge-inprivate-' + Date.now())
      ]
    });

    this.context = await this.browser.newContext({
      storageState: { cookies: [], origins: [] },
      acceptDownloads: false,
      ignoreHTTPSErrors: true,
      bypassCSP: true
    });

    this.page = await this.context.newPage();

    return {
      browser: this.browser,
      context: this.context,
      page: this.page
    };
  }

  /**
   * Cleanup all resources
   */
  async cleanup(): Promise<void> {
    this.logger.info('Cleaning up Edge InPrivate resources...');

    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.context = null;
      this.page = null;
    }

    if (this.edgeProcess) {
      this.edgeProcess.kill();
      this.edgeProcess = null;
    }

    this.logger.info('✅ Edge InPrivate cleanup completed');
  }
}
