const { chromium } = require('playwright');

async function testEdgePrivate() {
  console.log('Testing Edge Private Session...');

  try {
    // Launch Edge with InPrivate mode - Enhanced args
    const browser = await chromium.launch({
      channel: 'msedge',
      headless: false,
      args: [
        '--inprivate',              // Edge InPrivate mode - primary flag
        '--new-window',             // Force new window
        '--disable-extensions',     // Disable extensions in private mode
        '--disable-web-security',   // Allow cross-origin requests
        '--no-first-run',
        '--no-default-browser-check',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding'
      ]
    });

    console.log('✅ Edge browser launched successfully');

    const context = await browser.newContext({
      storageState: undefined
    });

    console.log('✅ Browser context created');

    const page = await context.newPage();
    console.log('✅ New page created');

    await page.goto('https://www.google.com');
    console.log('✅ Navigated to Google');

    // Check if we're in private mode by looking for InPrivate indicators
    const title = await page.title();
    console.log(`Page title: ${title}`);

    // Check for InPrivate mode indicators
    try {
      // Check if the page has InPrivate indicators
      const userAgent = await page.evaluate(() => navigator.userAgent);
      console.log(`User Agent: ${userAgent}`);

      // Check window properties that might indicate private mode
      const isPrivate = await page.evaluate(() => {
        // Some browsers expose private mode indicators
        return !window.indexedDB ||
               navigator.webdriver === true ||
               window.chrome?.runtime === undefined;
      });

      console.log(`Private mode detected: ${isPrivate}`);

      // Check if localStorage is restricted (common in private mode)
      const storageTest = await page.evaluate(() => {
        try {
          localStorage.setItem('test', 'test');
          localStorage.removeItem('test');
          return false; // localStorage works
        } catch (e) {
          return true; // localStorage restricted (private mode)
        }
      });

      console.log(`Storage restricted (private mode indicator): ${storageTest}`);

    } catch (error) {
      console.log('Could not check private mode indicators:', error.message);
    }

    // Take a screenshot to verify
    await page.screenshot({ path: 'edge-private-test.png' });
    console.log('✅ Screenshot taken: edge-private-test.png');

    await browser.close();
    console.log('✅ Browser closed');

    console.log('🎉 Edge Private Session test completed successfully!');

  } catch (error) {
    console.error('❌ Error testing Edge Private Session:', error.message);
    
    // Try fallback with different args
    console.log('Trying fallback configuration...');
    
    try {
      const browser = await chromium.launch({
        channel: 'msedge',
        headless: false,
        args: ['--incognito'] // Fallback to Chrome-style args
      });

      console.log('✅ Edge browser launched with fallback args');
      
      const context = await browser.newContext();
      const page = await context.newPage();
      await page.goto('https://www.google.com');
      
      console.log('✅ Fallback test successful');
      
      await browser.close();
      
    } catch (fallbackError) {
      console.error('❌ Fallback also failed:', fallbackError.message);
    }
  }
}

testEdgePrivate();
