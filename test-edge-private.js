const { chromium } = require('playwright');

async function testEdgePrivate() {
  console.log('Testing Edge Private Session...');
  
  try {
    // Launch Edge with InPrivate mode
    const browser = await chromium.launch({
      channel: 'msedge',
      headless: false,
      args: [
        '--inprivate',
        '--new-window',
        '--disable-extensions',
        '--no-first-run',
        '--no-default-browser-check'
      ]
    });

    console.log('✅ Edge browser launched successfully');

    const context = await browser.newContext({
      storageState: undefined
    });

    console.log('✅ Browser context created');

    const page = await context.newPage();
    console.log('✅ New page created');

    await page.goto('https://www.google.com');
    console.log('✅ Navigated to Google');

    // Check if we're in private mode by looking for InPrivate indicators
    const title = await page.title();
    console.log(`Page title: ${title}`);

    // Take a screenshot to verify
    await page.screenshot({ path: 'edge-private-test.png' });
    console.log('✅ Screenshot taken: edge-private-test.png');

    await browser.close();
    console.log('✅ Browser closed');

    console.log('🎉 Edge Private Session test completed successfully!');

  } catch (error) {
    console.error('❌ Error testing Edge Private Session:', error.message);
    
    // Try fallback with different args
    console.log('Trying fallback configuration...');
    
    try {
      const browser = await chromium.launch({
        channel: 'msedge',
        headless: false,
        args: ['--incognito'] // Fallback to Chrome-style args
      });

      console.log('✅ Edge browser launched with fallback args');
      
      const context = await browser.newContext();
      const page = await context.newPage();
      await page.goto('https://www.google.com');
      
      console.log('✅ Fallback test successful');
      
      await browser.close();
      
    } catch (fallbackError) {
      console.error('❌ Fallback also failed:', fallbackError.message);
    }
  }
}

testEdgePrivate();
