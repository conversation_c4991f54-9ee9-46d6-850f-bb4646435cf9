{"version":7,"type":"context-options","origin":"library","browserName":"chromium","options":{"noDefaultViewport":false,"viewport":{"width":1280,"height":720},"ignoreHTTPSErrors":true,"javaScriptEnabled":true,"bypassCSP":false,"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.25 Safari/537.36","locale":"en-US","offline":false,"deviceScaleFactor":1,"isMobile":false,"hasTouch":false,"colorScheme":"light","acceptDownloads":"accept","baseURL":"https://www.google.com","recordVideo":{"dir":"C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\.playwright-artifacts-0","size":{"width":1280,"height":720}},"serviceWorkers":"allow"},"platform":"win32","wallTime":1749104554549,"monotonicTime":4329.033,"sdkLanguage":"javascript","testIdAttributeName":"data-testid","contextId":"browser-context@124c9345c896d388a60c72fe42504e89","title":"cucumber-wrapper.spec.ts:34 › SPO Health Check › TC0101- Login and Check the Default Data Source Order"}
{"type":"before","callId":"call@9","startTime":4347.966,"apiName":"browserContext.newPage","class":"BrowserContext","method":"newPage","params":{},"stepId":"pw:api@7","beforeSnapshot":"before@call@9"}
{"type":"event","time":4650.227,"class":"BrowserContext","method":"page","params":{"pageId":"page@c6886d2b5677aac36ea20fab1f024056"}}
{"type":"after","callId":"call@9","endTime":4650.401,"result":{"page":"<Page>"},"afterSnapshot":"after@call@9"}
{"type":"before","callId":"call@11","startTime":4665.014,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{"type":"png","fullPage":true},"stepId":"pw:api@8","pageId":"page@c6886d2b5677aac36ea20fab1f024056","beforeSnapshot":"before@call@11"}
{"type":"log","callId":"call@11","time":5015.354,"message":"taking page screenshot"}
{"type":"log","callId":"call@11","time":5028.558,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@11","time":5033.677,"message":"fonts loaded"}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":5233.403,"frameSwapWallTime":1749104555453.306}
{"type":"after","callId":"call@11","endTime":5294.227,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@11"}
{"type":"frame-snapshot","snapshot":{"callId":"call@11","snapshotName":"after@call@11","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"about:blank","html":["HTML",{},["HEAD",{},["BASE",{"href":"about:blank"}]],["BODY"]],"viewport":{"width":1280,"height":720},"timestamp":5297.971,"wallTime":1749104555525,"collectionTime":0.900000000372529,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@13","startTime":5346.983,"apiName":"page.goto","class":"Frame","method":"goto","params":{"url":"https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/","waitUntil":"load"},"stepId":"pw:api@10","pageId":"page@c6886d2b5677aac36ea20fab1f024056","beforeSnapshot":"before@call@13"}
{"type":"frame-snapshot","snapshot":{"callId":"call@13","snapshotName":"before@call@13","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"about:blank","html":[[1,3]],"viewport":{"width":1280,"height":720},"timestamp":5348.574,"wallTime":1749104555576,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@13","time":5351.69,"message":"navigating to \"https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/\", waiting until \"load\""}
{"type":"after","callId":"call@13","endTime":7071.467,"result":{"response":"<Response>"},"afterSnapshot":"after@call@13"}
{"type":"console","messageType":"info","text":"BSSO Telemetry: {\"result\":\"Error\",\"error\":\"NoExtension\",\"type\":\"ChromeSsoTelemetry\",\"data\":{},\"traces\":[\"BrowserSSO Initialized\",\"Creating ChromeBrowserCore provider\",\"Sending message for method CreateProviderAsync\",\"Received message for method CreateProviderAsync\",\"Error: ChromeBrowserCore error NoExtension: Extension is not installed.\"]}","args":[{"preview":"BSSO Telemetry: {\"result\":\"Error\",\"error\":\"NoExtension\",\"type\":\"ChromeSsoTelemetry\",\"data\":{},\"traces\":[\"BrowserSSO Initialized\",\"Creating ChromeBrowserCore provider\",\"Sending message for method CreateProviderAsync\",\"Received message for method CreateProviderAsync\",\"Error: ChromeBrowserCore error NoExtension: Extension is not installed.\"]}","value":"BSSO Telemetry: {\"result\":\"Error\",\"error\":\"NoExtension\",\"type\":\"ChromeSsoTelemetry\",\"data\":{},\"traces\":[\"BrowserSSO Initialized\",\"Creating ChromeBrowserCore provider\",\"Sending message for method CreateProviderAsync\",\"Received message for method CreateProviderAsync\",\"Error: ChromeBrowserCore error NoExtension: Extension is not installed.\"]}"}],"location":{"url":"https://aadcdn.msftauth.net/shared/1.0/content/js/BssoInterrupt_Core_bazYuVH6rF7OQmuNhACwPg2.js","lineNumber":17,"columnNumber":79899},"time":7075.893,"pageId":"page@c6886d2b5677aac36ea20fab1f024056"}
{"type":"before","callId":"call@15","startTime":7085.299,"apiName":"page.waitForLoadState","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"af3e8f4392c91b615c51601c90a66a09","phase":"before","event":""}},"stepId":"pw:api@11","pageId":"page@c6886d2b5677aac36ea20fab1f024056","beforeSnapshot":"before@call@15"}
{"type":"console","messageType":"error","text":"Failed to load resource: the server responded with a status of 404 ()","args":[],"location":{"url":"https://login.microsoftonline.com/favicon.ico","lineNumber":0,"columnNumber":0},"time":7173.254,"pageId":"page@c6886d2b5677aac36ea20fab1f024056"}
{"type":"log","callId":"call@15","time":7293.386,"message":"  \"commit\" event fired"}
{"type":"log","callId":"call@15","time":7820.161,"message":"  \"domcontentloaded\" event fired"}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":7832.34,"frameSwapWallTime":1749104558050.906}
{"type":"log","callId":"call@15","time":8036.537,"message":"  \"load\" event fired"}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":8103.286,"frameSwapWallTime":1749104558318.48}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":8227.772,"frameSwapWallTime":1749104558444.347}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":8287.867,"frameSwapWallTime":1749104558501.558}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":8291.135,"frameSwapWallTime":1749104558506.8381}
{"type":"console","messageType":"verbose","text":"[DOM] Input elements should have autocomplete attributes (suggested: \"current-password\"): (More info: https://goo.gl/9p2vKq) %o","args":[],"location":{"url":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","lineNumber":0,"columnNumber":0},"time":8297.597,"pageId":"page@c6886d2b5677aac36ea20fab1f024056"}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":8332.122,"frameSwapWallTime":1749104558543.653}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":8344.739,"frameSwapWallTime":1749104558553.555}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":8349.294,"frameSwapWallTime":1749104558566.223}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":8388.598,"frameSwapWallTime":1749104558601.709}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":8449.498,"frameSwapWallTime":1749104558666.362}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":8479.806,"frameSwapWallTime":1749104558692.57}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":8516.244,"frameSwapWallTime":1749104558734.49}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":8581.583,"frameSwapWallTime":1749104558796.656}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":8764.99,"frameSwapWallTime":1749104558983.4548}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":8977.896,"frameSwapWallTime":1749104559196.135}
{"type":"log","callId":"call@15","time":9058.004,"message":"  \"networkidle\" event fired"}
{"type":"after","callId":"call@15","endTime":9058.26,"afterSnapshot":"after@call@15"}
{"type":"before","callId":"call@27","startTime":9059.268,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{"type":"png","fullPage":true},"stepId":"pw:api@12","pageId":"page@c6886d2b5677aac36ea20fab1f024056","beforeSnapshot":"before@call@27"}
{"type":"frame-snapshot","snapshot":{"callId":"call@15","snapshotName":"after@call@15","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":["HTML",{"dir":"ltr","class":"","lang":"en"},["HEAD",{},["BASE",{"href":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true"}],"\n    ",["TITLE",{},"Sign in to your account"],"\n    ",["META",{"http-equiv":"Content-Type","content":"text/html; charset=utf-8"}],"\n    ",["META",{"http-equiv":"X-UA-Compatible","content":"IE=edge"}],"\n    ",["META",{"name":"viewport","content":"width=device-width, initial-scale=1.0, maximum-scale=2.0, user-scalable=yes"}],"\n    ",["META",{"http-equiv":"Pragma","content":"no-cache"}],"\n    ",["META",{"http-equiv":"Expires","content":"-1"}],"\n    ",["LINK",{"rel":"preconnect","href":"https://aadcdn.msauth.net","crossorigin":""}],"\n",["META",{"http-equiv":"x-dns-prefetch-control","content":"on"}],"\n",["LINK",{"rel":"dns-prefetch","href":"//aadcdn.msauth.net"}],"\n",["LINK",{"rel":"dns-prefetch","href":"//aadcdn.msftauth.net"}],"\n\n    ",["META",{"name":"PageID","content":"ConvergedSignIn"}],"\n    ",["META",{"name":"SiteID","content":""}],"\n    ",["META",{"name":"ReqLC","content":"1033"}],"\n    ",["META",{"name":"LocLC","content":"en-US"}],"\n\n\n        ",["META",{"name":"format-detection","content":"telephone=no"}],"\n\n    ","\n\n    \n    \n",["META",{"name":"robots","content":"none"}],"\n\n"," \n"," \n","\n\n    \n        ","\n                ",["LINK",{"rel":"shortcut icon","href":"https://aadcdn.msauth.net/shared/1.0/content/images/favicon_a_eupayfgghqiai7k9sol6lg2.ico"}],"\n\n    ","\n\n\n    \n    ",["LINK",{"data-loader":"cdn","crossorigin":"anonymous","href":"https://aadcdn.msauth.net/ests/2.1/content/cdnbundles/converged.v2.login.min_81imvbluez-v5hbzpkxfcg2.css","rel":"stylesheet"}],"\n\n\n    ","\n\n    ","\n\n\n\n"],"\n\n",["BODY",{"data-bind":"defineGlobals: ServerData, bodyCssClass","class":"cb","style":"display: block;"},"\n    ","\n    \n\n",["DIV",{},"\n\n","\n\n",["DIV",{"data-bind":"if: activeDialog"}],"\n\n",["FORM",{"name":"f1","id":"i0281","novalidate":"novalidate","spellcheck":"false","method":"post","target":"_top","autocomplete":"off","data-bind":"visible: !isLoginPageHidden(), autoSubmit: forceSubmit, attr: { action: postUrl }, ariaHidden: !!activeDialog(), css: { 'provide-min-height': svr.fUseMinHeight }","action":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/login","class":"provide-min-height"},"\n    ","\n    ",["DIV",{"class":"login-paginated-page","data-bind":"component: { name: 'master-page',\n        publicMethods: masterPageMethods,\n        params: {\n            serverData: svr,\n            showButtons: svr.fShowButtons,\n            showFooterLinks: true,\n            useWizardBehavior: svr.fUseWizardBehavior,\n            handleWizardButtons: false,\n            password: password,\n            hideFromAria: ariaHidden },\n        event: {\n            footerAgreementClick: footer_agreementClick } }"},"\n\n","\n\n","\n    ","\n        ","\n        ",["DIV",{"id":"lightboxTemplateContainer","data-bind":"component: { name: 'lightbox-template', params: { serverData: svr, showHeader: $page.showHeader(), headerLogo: $page.headerLogo() } }, css: { 'provide-min-height': svr.fUseMinHeight }","class":"provide-min-height"},"\n\n",["DIV",{"id":"lightboxBackgroundContainer","data-bind":"css: { 'provide-min-height': svr.fUsePlaywrightMinHeight },\n    component: { name: 'background-image-control',\n        publicMethods: $page.backgroundControlMethods,\n        event: { load: $page.backgroundImageControl_onLoad } }"},["DIV",{"class":"background-image-holder","role":"presentation","data-bind":"css: { app: isAppBranding }, style: { background: backgroundStyle }","style":"background: rgb(255, 255, 255);"},"\n    ","\n\n    ","\n    ",["DIV",{"id":"backgroundImage","role":"img","data-bind":"backgroundImage: backgroundImageUrl(), externalCss: { 'background-image': true }, ariaLabel: str['STR_Background_Image_AltText']","class":"background-image ext-background-image","aria-label":"Organization background image","style":"background-image: url(\"https://aadcdn.msauthimages.net/c1c6b6c8-mwlevwhpqhztl4daar5f31lrkbmawuowudsskp3-s8c/logintenantbranding/0/illustration?ts=636344395043619803\");"}],"\n        ","\n        ",["DIV",{"data-bind":"externalCss: { 'background-overlay': true }","class":"background-overlay ext-background-overlay"}],"\n        ","\n    ","\n"]],"\n\n","\n\n","\n",["DIV",{"class":"outer","data-bind":"css: { 'app': $page.backgroundLogoUrl }"},"\n    ","\n\n    ",["DIV",{"class":"template-section main-section"},"\n        ",["DIV",{"data-bind":"externalCss: { 'middle': true }","class":"middle ext-middle"},"\n            ",["DIV",{"class":"full-height","data-bind":"component: { name: 'content-control', params: { serverData: svr, isVerticalSplitTemplate: $page.isVerticalSplitTemplate(), hasHeader: showHeader } }"},"\n\n","\n",["DIV",{"class":"flex-column"},"\n    ","\n\n    ","\n\n    ",["DIV",{"class":"win-scroll"},"\n        ",["DIV",{"id":"lightbox","data-bind":"\n            animationEnd: $page.paginationControlHelper.animationEnd,\n            externalCss: { 'sign-in-box': true },\n            css: {\n                'inner':  $content.isVerticalSplitTemplate,\n                'vertical-split-content': $content.isVerticalSplitTemplate,\n                'app': $page.backgroundLogoUrl,\n                'wide': $page.paginationControlHelper.useWiderWidth,\n                'fade-in-lightbox': $page.fadeInLightBox,\n                'has-popup': $page.showFedCredAndNewSession && ($page.showFedCredButtons() || $page.newSession()),\n                'transparent-lightbox': $page.backgroundControlMethods() && $page.backgroundControlMethods().useTransparentLightBox,\n                'lightbox-bottom-margin-debug': $page.showDebugDetails,\n                'has-header': $content.hasHeader }","class":"sign-in-box ext-sign-in-box fade-in-lightbox has-popup"},"\n\n            ","\n\n        ","\n\n        ",["DIV",{"class":"lightbox-cover","data-bind":"css: { 'disable-lightbox': svr.fAllowGrayOutLightBox && showLightboxProgress() }"}],"\n\n        ","\n\n        ","\n        ",["DIV",{"data-bind":"component: { name: 'logo-control',\n            params: {\n                isChinaDc: svr.fIsChinaDc,\n                bannerLogoUrl: bannerLogoUrl() } }"},"\n\n","\n    ","\n    ",["IMG",{"__playwright_current_src__":"https://aadcdn.msauthimages.net/c1c6b6c8-mwlevwhpqhztl4daar5f31lrkbmawuowudsskp3-s8c/logintenantbranding/0/bannerlogo?ts=636344395095152708","id":"bannerLogo","data-bind":"addEventHandlers, attr: { src: bannerLogoUrl, alt: str['STR_Banner_Logo_AltText'] }, externalCss: { 'banner-logo': true }","src":"https://aadcdn.msauthimages.net/c1c6b6c8-mwlevwhpqhztl4daar5f31lrkbmawuowudsskp3-s8c/logintenantbranding/0/bannerlogo?ts=636344395095152708","alt":"Organization banner logo","class":"banner-logo ext-banner-logo"}],"\n    ","\n    ","\n","\n\n","\n\n"],"\n        ","\n\n        ","\n\n        ","\n        ",["DIV",{"role":"main","data-bind":"component: { name: 'pagination-control',\n            publicMethods: paginationControlMethods,\n            params: {\n                enableCssAnimation: svr.fEnableCssAnimation,\n                disableAnimationIfAnimationEndUnsupported: svr.fDisableAnimationIfAnimationEndUnsupported,\n                initialViewId: initialViewId,\n                currentViewId: currentViewId,\n                initialSharedData: initialSharedData,\n                initialError: $loginPage.getServerError() },\n            event: {\n                cancel: paginationControl_onCancel,\n                load: paginationControlHelper.onLoad,\n                unload: paginationControlHelper.onUnload,\n                loadView: view_onLoadView,\n                showView: view_onShow,\n                setLightBoxFadeIn: view_onSetLightBoxFadeIn,\n                animationStateChange: paginationControl_onAnimationStateChange } }"},"\n\n",["DIV",{"data-bind":"css: { 'zero-opacity': hidePaginatedView() }","class":""},"\n    ","\n\n    ",["DIV",{"class":"pagination-view animate slide-in-next","data-bind":"css: {\n        'has-identity-banner': showIdentityBanner() && (sharedData.displayName || svr.sPOST_Username),\n        'zero-opacity': hidePaginatedView.hideSubView(),\n        'animate': animate(),\n        'slide-out-next': animate.isSlideOutNext(),\n        'slide-in-next': animate.isSlideInNext(),\n        'slide-out-back': animate.isSlideOutBack(),\n        'slide-in-back': animate.isSlideInBack() }"},"\n\n        ","\n            ","\n                ",["DIV",{"data-viewid":"1","data-showfedcredbutton":"true","data-bind":"pageViewComponent: { name: 'login-paginated-username-view',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    isInitialView: isInitialState,\n                    displayName: sharedData.displayName,\n                    otherIdpRedirectUrl: sharedData.otherIdpRedirectUrl,\n                    prefillNames: $loginPage.prefillNames,\n                    flowToken: sharedData.flowToken,\n                    availableSignupCreds: sharedData.availableSignupCreds,\n                    customStrings: $loginPage.stringCustomizationObservables.customStrings(),\n                    isCustomizationFailure: $loginPage.stringCustomizationObservables.isCustomStringsLoadFailure(),\n                    userIdLabel: $loginPage.userIdLabel,\n                    cantAccessYourAccountText: $loginPage.cantAccessYourAccountText,\n                    hideAccountResetCredentials: $loginPage.hideAccountResetCredentials,\n                    accessRecoveryLink: $loginPage.accessRecoveryLink,\n                    boilerPlateText: $loginPage.boilerPlateText },\n                event: {\n                    restoreIsRecoveryAttemptPost: $loginPage.view_onRestoreIsRecoveryAttemptPost,\n                    redirect: $loginPage.view_onRedirect,\n                    updateDFPUrl: $loginPage.view_onUpdateDFPUrl,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest,\n                    registerDialog: $loginPage.view_onRegisterDialog,\n                    unregisterDialog: $loginPage.view_onUnregisterDialog,\n                    showDialog: $loginPage.view_onShowDialog,\n                    updateAvailableCredsWithoutUsername: $loginPage.view_onUpdateAvailableCreds,\n                    agreementClick: $loginPage.footer_agreementClick } }"},"\n\n",["DIV",{"data-bind":"component: { name: 'header-control',\n    params: {\n        serverData: svr,\n        title: customTitle() || str['WF_STR_HeaderDefault_Title'],\n        headerDescription: customDescription() } }"},["DIV",{},"\n    ",["DIV",{"class":"row title ext-title","id":"loginHeader","data-bind":"externalCss: { 'title': true }"},"\n        ",["DIV",{"role":"heading","aria-level":"1","data-bind":"text: title"},"Sign in"],"\n        ","\n    "],"\n\n    ","\n"]],"\n\n","\n\n",["DIV",{"class":"row"},"\n    ","\n\n    ","\n    ",["DIV",{"role":"alert","aria-live":"assertive"},"\n        ","\n    "],"\n    ","\n\n    ",["DIV",{"class":"form-group col-md-24"},"\n        ","\n\n        ","\n        ",["DIV",{"class":"placeholderContainer","data-bind":"component: { name: 'placeholder-textbox-field',\n            publicMethods: usernameTextbox.placeholderTextboxMethods,\n            params: {\n                serverData: svr,\n                hintText: svr.fEnableLivePreview ? userIdLabel : tenantBranding.unsafe_userIdLabel || str['STR_SSSU_Username_Hint'] || str['CT_PWD_STR_Email_Example'],\n                hintCss: 'placeholder' + (!svr.fAllowPhoneSignIn ? ' ltr_override' : '') },\n            event: {\n                updateFocus: usernameTextbox.textbox_onUpdateFocus } }"},"\n    ","\n\n            ",["INPUT",{"__playwright_value_":"","type":"email","name":"loginfmt","id":"i0116","maxlength":"113","class":"form-control ltr_override input ext-input text-box ext-text-box","aria-required":"true","data-report-event":"Signin_Email_Phone_Skype","data-report-trigger":"click","data-report-value":"Email_Phone_Skype_Entry","data-bind":"\n                    attr: { lang: svr.fApplyAsciiRegexOnInput ? null : 'en',\n                    autocomplete: svr.fEnablePasskeyAutofillUI ? 'username webauthn' : 'username' },\n                    externalCss: {\n                        'input': true,\n                        'text-box': true,\n                        'has-error': usernameTextbox.error },\n                    ariaLabel: tenantBranding.unsafe_userIdLabel || str['CT_PWD_STR_Username_AriaLabel'],\n                    ariaDescribedBy:\n                     svr.fWin10HostAccessibilityFix ?\n                        'winBodyHeader winBodySubHeader loginHeader' + (pageDescription && !svr.fHideLoginDesc ? ' loginDescription usernameError' : ' usernameError') :\n                        'loginHeader' + (pageDescription && !svr.fHideLoginDesc ? ' loginDescription usernameError' : ' usernameError'),\n                    textInput: usernameTextbox.value,\n                    hasFocusEx: usernameTextbox.focused,\n                    placeholder: $placeholderText","autocomplete":"username","aria-label":"Sign in with your KPMG email address","aria-describedby":"loginHeader usernameError","placeholder":"Sign in with your KPMG email address","data-report-attached":"1"}],"\n\n            ",["INPUT",{"__playwright_value_":"","name":"passwd","type":"password","id":"i0118","data-bind":"moveOffScreen, textInput: passwordBrowserPrefill","class":"moveOffScreen","tabindex":"-1","aria-hidden":"true"}],"\n\n        ","\n","\n"],"\n        ","\n    "],"\n"],"\n\n",["DIV",{"data-bind":"css: { 'position-buttons': !tenantBranding.BoilerPlateText && !boilerPlateText }, externalCss: { 'password-reset-links-container': true }","class":"password-reset-links-container ext-password-reset-links-container"},"\n    ",["DIV",{"class":"row"},"\n        ",["DIV",{"class":"col-md-24"},"\n            ",["DIV",{"class":"text-13"},"\n                ","\n\n                ","\n                ",["DIV",{"class":"form-group"},"\n                    ",["A",{"id":"cantAccessAccount","name":"cannotAccessAccount","data-bind":"\n                        text: svr.fEnableLivePreview ? cantAccessYourAccountText : unsafe_cantAccessYourAccountText,\n                        click: accessRecoveryLink ? null : cantAccessAccount_onClick,\n                        attr: { target: accessRecoveryLink && '_blank' },\n                        href: accessRecoveryLink || '#'","target":"","href":"#"},"Can’t access your account?"],"\n                "],"\n                ","\n\n                ","\n\n                ","\n                    ","\n                ","\n\n                ","\n\n                ","\n            "],"\n        "],"\n    "],"\n"],"\n\n","\n\n",["DIV",{"class":"win-button-pin-bottom boilerplate-button-bottom","data-bind":"css : { 'boilerplate-button-bottom': tenantBranding.BoilerPlateText || boilerPlateText }"},"\n    ",["DIV",{"class":"row move-buttons","data-bind":"css: { 'move-buttons': tenantBranding.BoilerPlateText || boilerPlateText }"},"\n        ",["DIV",{"data-bind":"component: { name: 'footer-buttons-field',\n            params: {\n                serverData: svr,\n                isPrimaryButtonEnabled: !isRequestPending(),\n                isPrimaryButtonVisible: svr.fShowButtons,\n                isSecondaryButtonEnabled: true,\n                isSecondaryButtonVisible: svr.fShowButtons && isSecondaryButtonVisible(),\n                secondaryButtonText: secondaryButtonText() },\n            event: {\n                primaryButtonClick: primaryButton_onClick,\n                secondaryButtonClick: secondaryButton_onClick } }"},["DIV",{"class":"col-xs-24 no-padding-left-right button-container button-field-container ext-button-field-container","data-bind":"\n    visible: isPrimaryButtonVisible() || isSecondaryButtonVisible(),\n    css: { 'no-margin-bottom': removeBottomMargin },\n    externalCss: { 'button-field-container': true }"},"\n\n    ","\n\n    ",["DIV",{"data-bind":"css: { 'inline-block': isPrimaryButtonVisible }, externalCss: { 'button-item': true }","class":"inline-block button-item ext-button-item"},"\n        ","\n        ","\n        ",["INPUT",{"__playwright_value_":"Next","type":"submit","id":"idSIButton9","class":"win-button button_primary high-contrast-overrides button ext-button primary ext-primary","data-report-event":"Signin_Submit","data-report-trigger":"click","data-report-value":"Submit","data-bind":"\n                attr: primaryButtonAttributes,\n                css: { 'high-contrast-overrides': svr.fUseHighContrastDetectionMode },\n                externalCss: {\n                    'button': true,\n                    'primary': true },\n                value: primaryButtonText() || str['CT_PWD_STR_SignIn_Button_Next'],\n                hasFocus: focusOnPrimaryButton,\n                click: svr.fEnableLivePreview ?  function() { } : primaryButton_onClick,\n                clickBubble: !svr.fEnableLivePreview,\n                enable: isPrimaryButtonEnabled,\n                visible: isPrimaryButtonVisible,\n                preventTabbing: primaryButtonPreventTabbing","value":"Next","data-report-attached":"1"}],"\n        ","\n        ","\n    "],"\n"]],"\n    "],"\n"],"\n\n","\n",["DIV",{"id":"idBoilerPlateText","class":"wrap-content boilerplate-text ext-boilerplate-text","data-bind":"\n    htmlWithMods: svr.fEnableLivePreview ? boilerPlateText : tenantBranding.BoilerPlateText,\n    htmlMods: { filterLinks: svr.fIsHosted },\n    css: { 'transparent-lightbox': tenantBranding.UseTransparentLightBox },\n    externalCss: { 'boilerplate-text': true }"},["P",{},"Access to this service is restricted to authorized users only. Unauthorized use may subject you to prosecution or other legal actions. Activity by any user of this system may be monitored."],"\n",["P",{},["EM",{},"For more information about KPMG’s privacy policies and practices, please visit: ",["A",{"href":"https://landing.kpmg.com/privacy.html","rel":"noopener noreferrer","target":"_blank"},"KPMG Privacy"]]],"\n"],"\n"],"\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n            ","\n        ","\n    "],"\n"]],"\n        ","\n\n        ",["INPUT",{"__playwright_value_":"","type":"hidden","name":"ps","data-bind":"value: postedLoginStateViewId","value":""}],"\n        ",["INPUT",{"__playwright_value_":"","type":"hidden","name":"psRNGCDefaultType","data-bind":"value: postedLoginStateViewRNGCDefaultType","value":""}],"\n        ",["INPUT",{"__playwright_value_":"","type":"hidden","name":"psRNGCEntropy","data-bind":"value: postedLoginStateViewRNGCEntropy","value":""}],"\n        ",["INPUT",{"__playwright_value_":"","type":"hidden","name":"psRNGCSLK","data-bind":"value: postedLoginStateViewRNGCSLK","value":""}],"\n        ","\n        ",["INPUT",{"__playwright_value_":"h9RVXTGHbYxs/uvbsSd7acRY52CKkvOITIUzgWC575Q=6:1:CANARY:drht7J5kT/XgmHkRfnR/7AxI1JLSqDPim3/Y9Xv1C2g=","type":"hidden","data-bind":"attr: { name: svr.sCanaryTokenName }, value: svr.canary","name":"canary","value":"h9RVXTGHbYxs/uvbsSd7acRY52CKkvOITIUzgWC575Q=6:1:CANARY:drht7J5kT/XgmHkRfnR/7AxI1JLSqDPim3/Y9Xv1C2g="}],"\n        ","\n        ","\n        ",["INPUT",{"__playwright_value_":"rQQIARAAlVE9aBNhGL7LpWcT-5N2spscnYqX-777_S5QMPddzrQVWtOqLSLhfpMjuVyau8TQ0kUXJ-kiok4KLp1EHKQgdO4UHIuDi1A61J-pUEEvuDjWl5eHh5eHl-d93jkK5mFhFvwtgR0iCzwPsrY7ZP9UZzqbe_Vm4uvMCnFwOrh--pw5P98juXoct6MCx0XtkK01Q8ts5hvtoJa3w4CremEniDjH9cxuM86bUbv_gSQHJHlMknupTUnUsaJpMlYVQzFEWZKRwmsSFlRe0ooQAUUERR4JyMCaZsASW1JLyDCEki4qgiEDGegGj5EEkpkGAUKqgbGKi5oqSkjSjUSmqxgXDUFFWJCwfJSaXC524zo_hLDjb7k_U5mhx2o7jOIXVHpZBw_2qAvF8Y6aTe7yeNGyWB4glRXFRIFs5LKeAl0ZAUFBFn9I0WHbbfnOIE2epMcAVRgdzeaIK8RV4ixNvh5JQq1-CmbeP_u19Gjy47b_uUwcjnB1tXJnfe1G2droR1y3Z0WrjmLalQ2Jx0uN3vLC2sLtrdpdLCnSrXm5AHdpcpem9-nMKJUjGAqvwO80-fgSsZ_53_cMLpNHYzCbsUOrY7Yc35mehdByEEACixQXsCK0JNayFMQCyxYFG5nIRebRmJil7abpB9H03DbjO9U4bLgtprDN9IOoattD1jObXTdiCveYxCBzf2dn5-H4hbYfjBNnE09-vPz29Pfxaflk8prTqcfKotRY49ZrQblR8VoVTin2F-DizdVNfcUPBG5DXe9BzNfm3-aIL0lPEWdTu6k_0","type":"hidden","name":"ctx","data-bind":"value: ctx","value":"rQQIARAAlVE9aBNhGL7LpWcT-5N2spscnYqX-777_S5QMPddzrQVWtOqLSLhfpMjuVyau8TQ0kUXJ-kiok4KLp1EHKQgdO4UHIuDi1A61J-pUEEvuDjWl5eHh5eHl-d93jkK5mFhFvwtgR0iCzwPsrY7ZP9UZzqbe_Vm4uvMCnFwOrh--pw5P98juXoct6MCx0XtkK01Q8ts5hvtoJa3w4CremEniDjH9cxuM86bUbv_gSQHJHlMknupTUnUsaJpMlYVQzFEWZKRwmsSFlRe0ooQAUUERR4JyMCaZsASW1JLyDCEki4qgiEDGegGj5EEkpkGAUKqgbGKi5oqSkjSjUSmqxgXDUFFWJCwfJSaXC524zo_hLDjb7k_U5mhx2o7jOIXVHpZBw_2qAvF8Y6aTe7yeNGyWB4glRXFRIFs5LKeAl0ZAUFBFn9I0WHbbfnOIE2epMcAVRgdzeaIK8RV4ixNvh5JQq1-CmbeP_u19Gjy47b_uUwcjnB1tXJnfe1G2droR1y3Z0WrjmLalQ2Jx0uN3vLC2sLtrdpdLCnSrXm5AHdpcpem9-nMKJUjGAqvwO80-fgSsZ_53_cMLpNHYzCbsUOrY7Yc35mehdByEEACixQXsCK0JNayFMQCyxYFG5nIRebRmJil7abpB9H03DbjO9U4bLgtprDN9IOoattD1jObXTdiCveYxCBzf2dn5-H4hbYfjBNnE09-vPz29Pfxaflk8prTqcfKotRY49ZrQblR8VoVTin2F-DizdVNfcUPBG5DXe9BzNfm3-aIL0lPEWdTu6k_0"}],"\n        ",["INPUT",{"__playwright_value_":"196dd25f-97b4-4bfc-830f-bd7b69dc4800","type":"hidden","name":"hpgrequestid","data-bind":"value: svr.sessionId","value":"196dd25f-97b4-4bfc-830f-bd7b69dc4800"}],"\n        ",["INPUT",{"__playwright_value_":"AQABIQEAAABVrSpeuWamRam2jAF1XRQEgJ9dmtPX4YLnWlvOWa5t0OA8DVGFlPcw_zaNz15aQzbhi4Jd-I1bELc7MCtGubGpdZWZXYvt4IUy_qS6uMd38IP3MYPodYl5WlWZzLz4fzxh1VmfHTLpA50Tf6SqQq9BeAFb5HpgeltK2gM4yDGmYG4Qgm0ex66dYVmbCb_4Nbvw4zE46MJzRVKOTV4x8kICOTwvFMmMqwB1JC13HBXstXAGWeJhBWZUCnzr56qu_DiqNXoOZkV7BBonSQOUgIXIGDc97SJqrek3JZZO-Oy1pczeb7Lueji1D-QJziNfW1-iB8zUubPYPt87uOYSVOSDumxn0zqrdqI0kTBbMRyWcmm1oV1CuoS_qG9aqIjc9FowBDTtF8LOZfoefVODIKQ450qIaL2Bx1ZS8odiOX_BSmFQHaBRyXvu-yaYtysWhgJ65O8JGvJeqhVS2Z4mGA-UPMe7WAbi52Yan90U3xVue4tXbhebsN2GWmIp3dto1ExU7-BzyqBOx4TuwhIwx-7OIAA","type":"hidden","id":"i0327","data-bind":"attr: { name: svr.sFTName }, value: flowToken","name":"flowToken","value":"AQABIQEAAABVrSpeuWamRam2jAF1XRQEgJ9dmtPX4YLnWlvOWa5t0OA8DVGFlPcw_zaNz15aQzbhi4Jd-I1bELc7MCtGubGpdZWZXYvt4IUy_qS6uMd38IP3MYPodYl5WlWZzLz4fzxh1VmfHTLpA50Tf6SqQq9BeAFb5HpgeltK2gM4yDGmYG4Qgm0ex66dYVmbCb_4Nbvw4zE46MJzRVKOTV4x8kICOTwvFMmMqwB1JC13HBXstXAGWeJhBWZUCnzr56qu_DiqNXoOZkV7BBonSQOUgIXIGDc97SJqrek3JZZO-Oy1pczeb7Lueji1D-QJziNfW1-iB8zUubPYPt87uOYSVOSDumxn0zqrdqI0kTBbMRyWcmm1oV1CuoS_qG9aqIjc9FowBDTtF8LOZfoefVODIKQ450qIaL2Bx1ZS8odiOX_BSmFQHaBRyXvu-yaYtysWhgJ65O8JGvJeqhVS2Z4mGA-UPMe7WAbi52Yan90U3xVue4tXbhebsN2GWmIp3dto1ExU7-BzyqBOx4TuwhIwx-7OIAA"}],"\n        ",["INPUT",{"__playwright_value_":"","type":"hidden","name":"PPSX","data-bind":"value: svr.sRandomBlob","value":""}],"\n        ",["INPUT",{"__playwright_value_":"1","type":"hidden","name":"NewUser","value":"1"}],"\n        ",["INPUT",{"__playwright_value_":"","type":"hidden","name":"FoundMSAs","data-bind":"value: svr.sFoundMSAs","value":""}],"\n        ",["INPUT",{"__playwright_value_":"0","type":"hidden","name":"fspost","data-bind":"value: svr.fPOST_ForceSignin ? 1 : 0","value":"0"}],"\n        ",["INPUT",{"__playwright_value_":"0","type":"hidden","name":"i21","data-bind":"value: wasLearnMoreShown() ? 1 : 0","value":"0"}],"\n        ",["INPUT",{"__playwright_value_":"0","type":"hidden","name":"CookieDisclosure","data-bind":"value: svr.fShowCookieBanner ? 1 : 0","value":"0"}],"\n        ",["INPUT",{"__playwright_value_":"1","type":"hidden","name":"IsFidoSupported","data-bind":"value: isFidoSupported() ? 1 : 0","value":"1"}],"\n        ",["INPUT",{"__playwright_value_":"0","type":"hidden","name":"isSignupPost","data-bind":"value: isSignupPost() ? 1 : 0","value":"0"}],"\n        ","\n        ","\n        ",["INPUT",{"__playwright_value_":"","type":"hidden","name":"DfpArtifact","data-bind":"value: dfpResult()","value":""}],"\n        ","\n        ","\n        ",["DIV",{"data-bind":"component: { name: 'instrumentation-control',\n            publicMethods: instrumentationMethods,\n            params: { serverData: svr } }"},"\n",["INPUT",{"__playwright_value_":"","type":"hidden","name":"i19","data-bind":"value: timeOnPage","value":""}]],"\n    ","\n        "],"\n\n        ","\n        ","\n\n        ","\n        ","\n        ",["DIV",{"data-bind":"component: { name: 'fed-cred-buttons-control',\n            params: {\n                serverData: svr,\n                fedCredOptions: $page.otherSigninOptions },\n            event: {\n                fedCredButtonClick: $page.otherSigninOptionsButton_onClick } }"},"\n\n","\n",["DIV",{"data-bind":"css: { 'app': $page.backgroundLogoUrl }, externalCss: { 'promoted-fed-cred-box': true }","class":"promoted-fed-cred-box ext-promoted-fed-cred-box"},"\n    ",["DIV",{"class":"promoted-fed-cred-content","data-bind":"css: {\n        'animate': $page.useCssAnimations && $page.animate(),\n        'slide-out-next': $page.animate.isSlideOutNext,\n        'slide-in-next': $page.animate.isSlideInNext,\n        'slide-out-back': $page.animate.isSlideOutBack,\n        'slide-in-back': $page.animate.isSlideInBack,\n        'app': $page.backgroundLogoUrl }"},"\n\n        ","\n\n        ","\n        ","\n        ","\n        ",["DIV",{"class":"tile-container","data-bind":"css: { 'binaryChoice list': svr.fSupportWindowsStyles }"},"\n            ",["DIV",{"class":"row tile"},"\n                ",["DIV",{"class":"table","role":"button","tabindex":"0","data-bind":"\n                    css: { 'list-item': svr.fSupportWindowsStyles },\n                    pressEnter: $fedCredButtonsControl.fedCredButton_onClick,\n                    click: $fedCredButtonsControl.fedCredButton_onClick,\n                    ariaLabel: $data.text + ' ' + $data.helpText","aria-label":"Sign-in options undefined"},"\n\n                    ",["DIV",{"class":"table-row"},"\n                        ",["DIV",{"class":"table-cell tile-img medium"},"\n                            ","\n","\n",["IMG",{"__playwright_current_src__":"https://aadcdn.msauth.net/shared/1.0/content/images/signin-options_3e3f6b73c3f310c31d2c4d131a8ab8c6.svg","class":"tile-img medium","role":"presentation","data-bind":"addEventHandlers, attr: { src: $data.darkIconUrl }","src":"https://aadcdn.msauth.net/shared/1.0/content/images/signin-options_3e3f6b73c3f310c31d2c4d131a8ab8c6.svg"}],"\n","\n                        "],"\n                        ",["DIV",{"class":"table-cell text-left content","data-bind":"css: { 'content': !svr.fSupportWindowsStyles }"},"\n                            ","\n                            ","\n                            ",["DIV",{"data-bind":"text: $data.text, attr: { 'data-test-id': $data.testId }","data-test-id":"signinOptions"},"Sign-in options"],"\n                            ","\n\n                            ","\n                        "],"\n                    "],"\n                "],"\n            "],"\n            ","\n        "],"\n        ","\n        ","\n        ","\n\n        ","\n\n    "],"\n"],"\n"],"\n        ","\n\n        ","\n        ","\n\n        ","\n\n        ","\n        ","\n\n        ","\n    "],"\n"],"\n"],"\n        "],"\n    "],"\n\n    ","\n    ",["DIV",{"id":"footer","role":"contentinfo","data-bind":"\n        externalCss: {\n            'footer': true,\n            'has-background': !$page.useDefaultBackground() && $page.showFooter(),\n            'background-always-visible': $page.backgroundLogoUrl }","class":"footer ext-footer"},"\n\n        ",["DIV",{"data-bind":"component: { name: 'footer-control',\n            publicMethods: $page.footerMethods,\n            params: {\n                serverData: svr,\n                useDefaultBackground: $page.useDefaultBackground(),\n                hasDarkBackground: $page.backgroundLogoUrl(),\n                showLinks: true,\n                showFooter: $page.showFooter(),\n                hideTOU: $page.hideTOU(),\n                termsText: $page.termsText(),\n                termsLink: $page.termsLink(),\n                hidePrivacy: $page.hidePrivacy(),\n                privacyText: $page.privacyText(),\n                privacyLink: $page.privacyLink() },\n            event: {\n                agreementClick: $page.footer_agreementClick,\n                showDebugDetails: $page.toggleDebugDetails_onClick } }"},"\n",["DIV",{"id":"footerLinks","class":"footerNode text-secondary footer-links ext-footer-links","data-bind":"externalCss: { 'footer-links': true }"},"\n\n    ","\n        ","\n        ",["A",{"id":"ftrTerms","data-bind":"\n            text: termsText,\n            href: termsLink,\n            click: termsLink_onClick,\n            externalCss: {\n                'footer-content': true,\n                'footer-item': true,\n                'has-background': !useDefaultBackground,\n                'background-always-visible': hasDarkBackground }","href":"https://www.microsoft.com/en-US/servicesagreement/","class":"footer-content ext-footer-content footer-item ext-footer-item has-background ext-has-background"},"Terms of use"],"\n        ","\n\n        ","\n        ",["A",{"id":"ftrPrivacy","data-bind":"\n            text: privacyText,\n            href: privacyLink,\n            click: privacyLink_onClick,\n            externalCss: {\n                'footer-content': true,\n                'footer-item': true,\n                'has-background': !useDefaultBackground,\n                'background-always-visible': hasDarkBackground }","href":"https://privacy.microsoft.com/en-US/privacystatement","class":"footer-content ext-footer-content footer-item ext-footer-item has-background ext-has-background"},"Privacy & cookies"],"\n        ","\n\n        ","\n\n        ","\n\n        ","\n    ","\n\n    ","\n    ",["A",{"id":"moreOptions","href":"#","role":"button","data-bind":"\n        click: moreInfo_onClick,\n        ariaLabel: str['CT_STR_More_Options_Ellipsis_AriaLabel'],\n        attr: { 'aria-expanded': showDebugDetails().toString() },\n        hasFocusEx: focusMoreInfo(),\n        externalCss: {\n            'footer-content': true,\n            'footer-item': true,\n            'debug-item': true,\n            'has-background': !useDefaultBackground,\n            'background-always-visible': hasDarkBackground }","aria-label":"Click here for troubleshooting information","aria-expanded":"false","class":"footer-content ext-footer-content footer-item ext-footer-item debug-item ext-debug-item has-background ext-has-background"},"..."],"\n"],"\n","\n\n"],"\n    "],"\n    ","\n"],"\n"],"\n        ","\n\n        ","\n    ","\n"],"\n    ","\n"],"\n\n",["FORM",{"data-bind":"postRedirectForm: postRedirect","method":"POST","aria-hidden":"true","target":"_top"}],"\n\n","\n\n"]]],"viewport":{"width":1280,"height":720},"timestamp":9066.13,"wallTime":1749104559291,"collectionTime":3.200000001117587,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@27","snapshotName":"before@call@27","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":[[1,477]],"viewport":{"width":1280,"height":720},"timestamp":9069.007,"wallTime":1749104559293,"collectionTime":0.900000000372529,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@27","time":9070.212,"message":"taking page screenshot"}
{"type":"log","callId":"call@27","time":9080.415,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@27","time":9084.608,"message":"fonts loaded"}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":9088.056,"frameSwapWallTime":1749104559298.758}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":9103.656,"frameSwapWallTime":1749104559312.667}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":9117.74,"frameSwapWallTime":1749104559331.27}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":9132.191,"frameSwapWallTime":1749104559345.106}
{"type":"after","callId":"call@27","endTime":9225.841,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@27"}
{"type":"frame-snapshot","snapshot":{"callId":"call@27","snapshotName":"after@call@27","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":["HTML",{"dir":"ltr","class":"","lang":"en"},[[2,47]],[[2,48]],["BODY",{"data-bind":"defineGlobals: ServerData, bodyCssClass","class":"cb","style":"display: block;"},[[2,49]],[[2,50]],["DIV",{},[[2,51]],[[2,52]],[[2,53]],[[2,54]],["FORM",{"name":"f1","id":"i0281","novalidate":"novalidate","spellcheck":"false","method":"post","target":"_top","autocomplete":"off","data-bind":"visible: !isLoginPageHidden(), autoSubmit: forceSubmit, attr: { action: postUrl }, ariaHidden: !!activeDialog(), css: { 'provide-min-height': svr.fUseMinHeight }","action":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/login","class":"provide-min-height"},[[2,55]],[[2,56]],["DIV",{"class":"login-paginated-page","data-bind":"component: { name: 'master-page',\n        publicMethods: masterPageMethods,\n        params: {\n            serverData: svr,\n            showButtons: svr.fShowButtons,\n            showFooterLinks: true,\n            useWizardBehavior: svr.fUseWizardBehavior,\n            handleWizardButtons: false,\n            password: password,\n            hideFromAria: ariaHidden },\n        event: {\n            footerAgreementClick: footer_agreementClick } }"},[[2,57]],[[2,58]],[[2,59]],[[2,60]],[[2,61]],["DIV",{"id":"lightboxTemplateContainer","data-bind":"component: { name: 'lightbox-template', params: { serverData: svr, showHeader: $page.showHeader(), headerLogo: $page.headerLogo() } }, css: { 'provide-min-height': svr.fUseMinHeight }","class":"provide-min-height"},[[2,62]],[[2,74]],[[2,75]],[[2,76]],[[2,77]],["DIV",{"class":"outer","data-bind":"css: { 'app': $page.backgroundLogoUrl }"},[[2,78]],[[2,79]],["DIV",{"class":"template-section main-section"},[[2,80]],["DIV",{"data-bind":"externalCss: { 'middle': true }","class":"middle ext-middle"},[[2,81]],["DIV",{"class":"full-height","data-bind":"component: { name: 'content-control', params: { serverData: svr, isVerticalSplitTemplate: $page.isVerticalSplitTemplate(), hasHeader: showHeader } }"},[[2,82]],[[2,83]],["DIV",{"class":"flex-column"},[[2,84]],[[2,85]],[[2,86]],["DIV",{"class":"win-scroll"},[[2,87]],["DIV",{"id":"lightbox","data-bind":"\n            animationEnd: $page.paginationControlHelper.animationEnd,\n            externalCss: { 'sign-in-box': true },\n            css: {\n                'inner':  $content.isVerticalSplitTemplate,\n                'vertical-split-content': $content.isVerticalSplitTemplate,\n                'app': $page.backgroundLogoUrl,\n                'wide': $page.paginationControlHelper.useWiderWidth,\n                'fade-in-lightbox': $page.fadeInLightBox,\n                'has-popup': $page.showFedCredAndNewSession && ($page.showFedCredButtons() || $page.newSession()),\n                'transparent-lightbox': $page.backgroundControlMethods() && $page.backgroundControlMethods().useTransparentLightBox,\n                'lightbox-bottom-margin-debug': $page.showDebugDetails,\n                'has-header': $content.hasHeader }","class":"sign-in-box ext-sign-in-box fade-in-lightbox has-popup"},[[2,88]],[[2,89]],[[2,90]],[[2,91]],[[2,92]],[[2,93]],[[2,94]],[[2,104]],[[2,105]],[[2,106]],[[2,107]],[[2,108]],["DIV",{"role":"main","data-bind":"component: { name: 'pagination-control',\n            publicMethods: paginationControlMethods,\n            params: {\n                enableCssAnimation: svr.fEnableCssAnimation,\n                disableAnimationIfAnimationEndUnsupported: svr.fDisableAnimationIfAnimationEndUnsupported,\n                initialViewId: initialViewId,\n                currentViewId: currentViewId,\n                initialSharedData: initialSharedData,\n                initialError: $loginPage.getServerError() },\n            event: {\n                cancel: paginationControl_onCancel,\n                load: paginationControlHelper.onLoad,\n                unload: paginationControlHelper.onUnload,\n                loadView: view_onLoadView,\n                showView: view_onShow,\n                setLightBoxFadeIn: view_onSetLightBoxFadeIn,\n                animationStateChange: paginationControl_onAnimationStateChange } }"},[[2,109]],["DIV",{"data-bind":"css: { 'zero-opacity': hidePaginatedView() }","class":""},[[2,110]],[[2,111]],["DIV",{"class":"pagination-view animate slide-in-next","data-bind":"css: {\n        'has-identity-banner': showIdentityBanner() && (sharedData.displayName || svr.sPOST_Username),\n        'zero-opacity': hidePaginatedView.hideSubView(),\n        'animate': animate(),\n        'slide-out-next': animate.isSlideOutNext(),\n        'slide-in-next': animate.isSlideInNext(),\n        'slide-out-back': animate.isSlideOutBack(),\n        'slide-in-back': animate.isSlideInBack() }"},[[2,112]],[[2,113]],[[2,114]],["DIV",{"data-viewid":"1","data-showfedcredbutton":"true","data-bind":"pageViewComponent: { name: 'login-paginated-username-view',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    isInitialView: isInitialState,\n                    displayName: sharedData.displayName,\n                    otherIdpRedirectUrl: sharedData.otherIdpRedirectUrl,\n                    prefillNames: $loginPage.prefillNames,\n                    flowToken: sharedData.flowToken,\n                    availableSignupCreds: sharedData.availableSignupCreds,\n                    customStrings: $loginPage.stringCustomizationObservables.customStrings(),\n                    isCustomizationFailure: $loginPage.stringCustomizationObservables.isCustomStringsLoadFailure(),\n                    userIdLabel: $loginPage.userIdLabel,\n                    cantAccessYourAccountText: $loginPage.cantAccessYourAccountText,\n                    hideAccountResetCredentials: $loginPage.hideAccountResetCredentials,\n                    accessRecoveryLink: $loginPage.accessRecoveryLink,\n                    boilerPlateText: $loginPage.boilerPlateText },\n                event: {\n                    restoreIsRecoveryAttemptPost: $loginPage.view_onRestoreIsRecoveryAttemptPost,\n                    redirect: $loginPage.view_onRedirect,\n                    updateDFPUrl: $loginPage.view_onUpdateDFPUrl,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest,\n                    registerDialog: $loginPage.view_onRegisterDialog,\n                    unregisterDialog: $loginPage.view_onUnregisterDialog,\n                    showDialog: $loginPage.view_onShowDialog,\n                    updateAvailableCredsWithoutUsername: $loginPage.view_onUpdateAvailableCreds,\n                    agreementClick: $loginPage.footer_agreementClick } }"},[[2,115]],[[2,126]],[[2,127]],[[2,128]],["DIV",{"class":"row"},[[2,129]],[[2,130]],[[2,131]],[[2,134]],[[2,135]],[[2,136]],["DIV",{"class":"form-group col-md-24"},[[2,137]],[[2,138]],[[2,139]],["DIV",{"class":"placeholderContainer","data-bind":"component: { name: 'placeholder-textbox-field',\n            publicMethods: usernameTextbox.placeholderTextboxMethods,\n            params: {\n                serverData: svr,\n                hintText: svr.fEnableLivePreview ? userIdLabel : tenantBranding.unsafe_userIdLabel || str['STR_SSSU_Username_Hint'] || str['CT_PWD_STR_Email_Example'],\n                hintCss: 'placeholder' + (!svr.fAllowPhoneSignIn ? ' ltr_override' : '') },\n            event: {\n                updateFocus: usernameTextbox.textbox_onUpdateFocus } }"},[[2,140]],[[2,141]],["INPUT",{"__playwright_value_":"","type":"email","name":"loginfmt","id":"i0116","maxlength":"113","class":"form-control ltr_override input ext-input text-box ext-text-box","aria-required":"true","data-report-event":"Signin_Email_Phone_Skype","data-report-trigger":"click","data-report-value":"Email_Phone_Skype_Entry","data-bind":"\n                    attr: { lang: svr.fApplyAsciiRegexOnInput ? null : 'en',\n                    autocomplete: svr.fEnablePasskeyAutofillUI ? 'username webauthn' : 'username' },\n                    externalCss: {\n                        'input': true,\n                        'text-box': true,\n                        'has-error': usernameTextbox.error },\n                    ariaLabel: tenantBranding.unsafe_userIdLabel || str['CT_PWD_STR_Username_AriaLabel'],\n                    ariaDescribedBy:\n                     svr.fWin10HostAccessibilityFix ?\n                        'winBodyHeader winBodySubHeader loginHeader' + (pageDescription && !svr.fHideLoginDesc ? ' loginDescription usernameError' : ' usernameError') :\n                        'loginHeader' + (pageDescription && !svr.fHideLoginDesc ? ' loginDescription usernameError' : ' usernameError'),\n                    textInput: usernameTextbox.value,\n                    hasFocusEx: usernameTextbox.focused,\n                    placeholder: $placeholderText","autocomplete":"username","aria-label":"Sign in with your KPMG email address","aria-describedby":"loginHeader usernameError","placeholder":"Sign in with your KPMG email address","data-report-attached":"1","style":""}],[[2,143]],["INPUT",{"__playwright_value_":"","name":"passwd","type":"password","id":"i0118","data-bind":"moveOffScreen, textInput: passwordBrowserPrefill","class":"moveOffScreen","tabindex":"-1","aria-hidden":"true","style":""}],[[2,145]],[[2,146]],[[2,147]]],[[2,149]],[[2,150]]],[[2,152]]],[[2,154]],[[2,180]],[[2,181]],[[2,182]],["DIV",{"class":"win-button-pin-bottom boilerplate-button-bottom","data-bind":"css : { 'boilerplate-button-bottom': tenantBranding.BoilerPlateText || boilerPlateText }"},[[2,183]],["DIV",{"class":"row move-buttons","data-bind":"css: { 'move-buttons': tenantBranding.BoilerPlateText || boilerPlateText }"},[[2,184]],["DIV",{"data-bind":"component: { name: 'footer-buttons-field',\n            params: {\n                serverData: svr,\n                isPrimaryButtonEnabled: !isRequestPending(),\n                isPrimaryButtonVisible: svr.fShowButtons,\n                isSecondaryButtonEnabled: true,\n                isSecondaryButtonVisible: svr.fShowButtons && isSecondaryButtonVisible(),\n                secondaryButtonText: secondaryButtonText() },\n            event: {\n                primaryButtonClick: primaryButton_onClick,\n                secondaryButtonClick: secondaryButton_onClick } }"},["DIV",{"class":"col-xs-24 no-padding-left-right button-container button-field-container ext-button-field-container","data-bind":"\n    visible: isPrimaryButtonVisible() || isSecondaryButtonVisible(),\n    css: { 'no-margin-bottom': removeBottomMargin },\n    externalCss: { 'button-field-container': true }"},[[2,185]],[[2,186]],["DIV",{"data-bind":"css: { 'inline-block': isPrimaryButtonVisible }, externalCss: { 'button-item': true }","class":"inline-block button-item ext-button-item"},[[2,187]],[[2,188]],[[2,189]],["INPUT",{"__playwright_value_":"Next","type":"submit","id":"idSIButton9","class":"win-button button_primary high-contrast-overrides button ext-button primary ext-primary","data-report-event":"Signin_Submit","data-report-trigger":"click","data-report-value":"Submit","data-bind":"\n                attr: primaryButtonAttributes,\n                css: { 'high-contrast-overrides': svr.fUseHighContrastDetectionMode },\n                externalCss: {\n                    'button': true,\n                    'primary': true },\n                value: primaryButtonText() || str['CT_PWD_STR_SignIn_Button_Next'],\n                hasFocus: focusOnPrimaryButton,\n                click: svr.fEnableLivePreview ?  function() { } : primaryButton_onClick,\n                clickBubble: !svr.fEnableLivePreview,\n                enable: isPrimaryButtonEnabled,\n                visible: isPrimaryButtonVisible,\n                preventTabbing: primaryButtonPreventTabbing","value":"Next","data-report-attached":"1","style":""}],[[2,191]],[[2,192]],[[2,193]]],[[2,195]]]],[[2,198]]],[[2,200]]],[[2,202]],[[2,203]],[[2,213]],[[2,214]]],[[2,216]],[[2,217]],[[2,218]],[[2,219]],[[2,220]],[[2,221]],[[2,222]],[[2,223]],[[2,224]],[[2,225]],[[2,226]],[[2,227]],[[2,228]],[[2,229]],[[2,230]],[[2,231]],[[2,232]],[[2,233]],[[2,234]],[[2,235]],[[2,236]],[[2,237]],[[2,238]],[[2,239]],[[2,240]],[[2,241]],[[2,242]],[[2,243]],[[2,244]],[[2,245]],[[2,246]],[[2,247]],[[2,248]],[[2,249]],[[2,250]],[[2,251]],[[2,252]],[[2,253]],[[2,254]],[[2,255]],[[2,256]],[[2,257]],[[2,258]],[[2,259]],[[2,260]],[[2,261]],[[2,262]],[[2,263]],[[2,264]],[[2,265]],[[2,266]],[[2,267]],[[2,268]],[[2,269]],[[2,270]],[[2,271]],[[2,272]],[[2,273]],[[2,274]],[[2,275]],[[2,276]],[[2,277]],[[2,278]],[[2,279]],[[2,280]],[[2,281]],[[2,282]],[[2,283]],[[2,284]],[[2,285]],[[2,286]],[[2,287]],[[2,288]],[[2,289]],[[2,290]],[[2,291]],[[2,292]],[[2,293]],[[2,294]],[[2,295]],[[2,296]],[[2,297]],[[2,298]],[[2,299]],[[2,300]],[[2,301]],[[2,302]],[[2,303]],[[2,304]]],[[2,306]]]],[[2,309]],[[2,310]],["INPUT",{"__playwright_value_":"","type":"hidden","name":"ps","data-bind":"value: postedLoginStateViewId","value":"","style":""}],[[2,312]],["INPUT",{"__playwright_value_":"","type":"hidden","name":"psRNGCDefaultType","data-bind":"value: postedLoginStateViewRNGCDefaultType","value":"","style":""}],[[2,314]],["INPUT",{"__playwright_value_":"","type":"hidden","name":"psRNGCEntropy","data-bind":"value: postedLoginStateViewRNGCEntropy","value":"","style":""}],[[2,316]],["INPUT",{"__playwright_value_":"","type":"hidden","name":"psRNGCSLK","data-bind":"value: postedLoginStateViewRNGCSLK","value":"","style":""}],[[2,318]],[[2,319]],["INPUT",{"__playwright_value_":"h9RVXTGHbYxs/uvbsSd7acRY52CKkvOITIUzgWC575Q=6:1:CANARY:drht7J5kT/XgmHkRfnR/7AxI1JLSqDPim3/Y9Xv1C2g=","type":"hidden","data-bind":"attr: { name: svr.sCanaryTokenName }, value: svr.canary","name":"canary","value":"h9RVXTGHbYxs/uvbsSd7acRY52CKkvOITIUzgWC575Q=6:1:CANARY:drht7J5kT/XgmHkRfnR/7AxI1JLSqDPim3/Y9Xv1C2g=","style":""}],[[2,321]],[[2,322]],[[2,323]],["INPUT",{"__playwright_value_":"rQQIARAAlVE9aBNhGL7LpWcT-5N2spscnYqX-777_S5QMPddzrQVWtOqLSLhfpMjuVyau8TQ0kUXJ-kiok4KLp1EHKQgdO4UHIuDi1A61J-pUEEvuDjWl5eHh5eHl-d93jkK5mFhFvwtgR0iCzwPsrY7ZP9UZzqbe_Vm4uvMCnFwOrh--pw5P98juXoct6MCx0XtkK01Q8ts5hvtoJa3w4CremEniDjH9cxuM86bUbv_gSQHJHlMknupTUnUsaJpMlYVQzFEWZKRwmsSFlRe0ooQAUUERR4JyMCaZsASW1JLyDCEki4qgiEDGegGj5EEkpkGAUKqgbGKi5oqSkjSjUSmqxgXDUFFWJCwfJSaXC524zo_hLDjb7k_U5mhx2o7jOIXVHpZBw_2qAvF8Y6aTe7yeNGyWB4glRXFRIFs5LKeAl0ZAUFBFn9I0WHbbfnOIE2epMcAVRgdzeaIK8RV4ixNvh5JQq1-CmbeP_u19Gjy47b_uUwcjnB1tXJnfe1G2droR1y3Z0WrjmLalQ2Jx0uN3vLC2sLtrdpdLCnSrXm5AHdpcpem9-nMKJUjGAqvwO80-fgSsZ_53_cMLpNHYzCbsUOrY7Yc35mehdByEEACixQXsCK0JNayFMQCyxYFG5nIRebRmJil7abpB9H03DbjO9U4bLgtprDN9IOoattD1jObXTdiCveYxCBzf2dn5-H4hbYfjBNnE09-vPz29Pfxaflk8prTqcfKotRY49ZrQblR8VoVTin2F-DizdVNfcUPBG5DXe9BzNfm3-aIL0lPEWdTu6k_0","type":"hidden","name":"ctx","data-bind":"value: ctx","value":"rQQIARAAlVE9aBNhGL7LpWcT-5N2spscnYqX-777_S5QMPddzrQVWtOqLSLhfpMjuVyau8TQ0kUXJ-kiok4KLp1EHKQgdO4UHIuDi1A61J-pUEEvuDjWl5eHh5eHl-d93jkK5mFhFvwtgR0iCzwPsrY7ZP9UZzqbe_Vm4uvMCnFwOrh--pw5P98juXoct6MCx0XtkK01Q8ts5hvtoJa3w4CremEniDjH9cxuM86bUbv_gSQHJHlMknupTUnUsaJpMlYVQzFEWZKRwmsSFlRe0ooQAUUERR4JyMCaZsASW1JLyDCEki4qgiEDGegGj5EEkpkGAUKqgbGKi5oqSkjSjUSmqxgXDUFFWJCwfJSaXC524zo_hLDjb7k_U5mhx2o7jOIXVHpZBw_2qAvF8Y6aTe7yeNGyWB4glRXFRIFs5LKeAl0ZAUFBFn9I0WHbbfnOIE2epMcAVRgdzeaIK8RV4ixNvh5JQq1-CmbeP_u19Gjy47b_uUwcjnB1tXJnfe1G2droR1y3Z0WrjmLalQ2Jx0uN3vLC2sLtrdpdLCnSrXm5AHdpcpem9-nMKJUjGAqvwO80-fgSsZ_53_cMLpNHYzCbsUOrY7Yc35mehdByEEACixQXsCK0JNayFMQCyxYFG5nIRebRmJil7abpB9H03DbjO9U4bLgtprDN9IOoattD1jObXTdiCveYxCBzf2dn5-H4hbYfjBNnE09-vPz29Pfxaflk8prTqcfKotRY49ZrQblR8VoVTin2F-DizdVNfcUPBG5DXe9BzNfm3-aIL0lPEWdTu6k_0","style":""}],[[2,325]],["INPUT",{"__playwright_value_":"196dd25f-97b4-4bfc-830f-bd7b69dc4800","type":"hidden","name":"hpgrequestid","data-bind":"value: svr.sessionId","value":"196dd25f-97b4-4bfc-830f-bd7b69dc4800","style":""}],[[2,327]],["INPUT",{"__playwright_value_":"AQABIQEAAABVrSpeuWamRam2jAF1XRQEgJ9dmtPX4YLnWlvOWa5t0OA8DVGFlPcw_zaNz15aQzbhi4Jd-I1bELc7MCtGubGpdZWZXYvt4IUy_qS6uMd38IP3MYPodYl5WlWZzLz4fzxh1VmfHTLpA50Tf6SqQq9BeAFb5HpgeltK2gM4yDGmYG4Qgm0ex66dYVmbCb_4Nbvw4zE46MJzRVKOTV4x8kICOTwvFMmMqwB1JC13HBXstXAGWeJhBWZUCnzr56qu_DiqNXoOZkV7BBonSQOUgIXIGDc97SJqrek3JZZO-Oy1pczeb7Lueji1D-QJziNfW1-iB8zUubPYPt87uOYSVOSDumxn0zqrdqI0kTBbMRyWcmm1oV1CuoS_qG9aqIjc9FowBDTtF8LOZfoefVODIKQ450qIaL2Bx1ZS8odiOX_BSmFQHaBRyXvu-yaYtysWhgJ65O8JGvJeqhVS2Z4mGA-UPMe7WAbi52Yan90U3xVue4tXbhebsN2GWmIp3dto1ExU7-BzyqBOx4TuwhIwx-7OIAA","type":"hidden","id":"i0327","data-bind":"attr: { name: svr.sFTName }, value: flowToken","name":"flowToken","value":"AQABIQEAAABVrSpeuWamRam2jAF1XRQEgJ9dmtPX4YLnWlvOWa5t0OA8DVGFlPcw_zaNz15aQzbhi4Jd-I1bELc7MCtGubGpdZWZXYvt4IUy_qS6uMd38IP3MYPodYl5WlWZzLz4fzxh1VmfHTLpA50Tf6SqQq9BeAFb5HpgeltK2gM4yDGmYG4Qgm0ex66dYVmbCb_4Nbvw4zE46MJzRVKOTV4x8kICOTwvFMmMqwB1JC13HBXstXAGWeJhBWZUCnzr56qu_DiqNXoOZkV7BBonSQOUgIXIGDc97SJqrek3JZZO-Oy1pczeb7Lueji1D-QJziNfW1-iB8zUubPYPt87uOYSVOSDumxn0zqrdqI0kTBbMRyWcmm1oV1CuoS_qG9aqIjc9FowBDTtF8LOZfoefVODIKQ450qIaL2Bx1ZS8odiOX_BSmFQHaBRyXvu-yaYtysWhgJ65O8JGvJeqhVS2Z4mGA-UPMe7WAbi52Yan90U3xVue4tXbhebsN2GWmIp3dto1ExU7-BzyqBOx4TuwhIwx-7OIAA","style":""}],[[2,329]],["INPUT",{"__playwright_value_":"","type":"hidden","name":"PPSX","data-bind":"value: svr.sRandomBlob","value":"","style":""}],[[2,331]],["INPUT",{"__playwright_value_":"1","type":"hidden","name":"NewUser","value":"1","style":""}],[[2,333]],["INPUT",{"__playwright_value_":"","type":"hidden","name":"FoundMSAs","data-bind":"value: svr.sFoundMSAs","value":"","style":""}],[[2,335]],["INPUT",{"__playwright_value_":"0","type":"hidden","name":"fspost","data-bind":"value: svr.fPOST_ForceSignin ? 1 : 0","value":"0","style":""}],[[2,337]],["INPUT",{"__playwright_value_":"0","type":"hidden","name":"i21","data-bind":"value: wasLearnMoreShown() ? 1 : 0","value":"0","style":""}],[[2,339]],["INPUT",{"__playwright_value_":"0","type":"hidden","name":"CookieDisclosure","data-bind":"value: svr.fShowCookieBanner ? 1 : 0","value":"0","style":""}],[[2,341]],["INPUT",{"__playwright_value_":"1","type":"hidden","name":"IsFidoSupported","data-bind":"value: isFidoSupported() ? 1 : 0","value":"1","style":""}],[[2,343]],["INPUT",{"__playwright_value_":"0","type":"hidden","name":"isSignupPost","data-bind":"value: isSignupPost() ? 1 : 0","value":"0","style":""}],[[2,345]],[[2,346]],[[2,347]],["INPUT",{"__playwright_value_":"","type":"hidden","name":"DfpArtifact","data-bind":"value: dfpResult()","value":"","style":""}],[[2,349]],[[2,350]],[[2,351]],["DIV",{"data-bind":"component: { name: 'instrumentation-control',\n            publicMethods: instrumentationMethods,\n            params: { serverData: svr } }"},[[2,352]],["INPUT",{"__playwright_value_":"","type":"hidden","name":"i19","data-bind":"value: timeOnPage","value":"","style":""}]],[[2,355]],[[2,356]]],[[2,358]],[[2,359]],[[2,360]],[[2,361]],[[2,362]],[[2,410]],[[2,411]],[[2,412]],[[2,413]],[[2,414]],[[2,415]],[[2,416]],[[2,417]],[[2,418]]],[[2,420]]],[[2,422]]],[[2,424]]],[[2,426]]],[[2,428]],[[2,429]],[[2,457]],[[2,458]],[[2,459]]],[[2,461]]],[[2,463]],[[2,464]],[[2,465]],[[2,466]]],[[2,468]],[[2,469]]],[[2,471]],[[2,472]],[[2,473]],[[2,474]]]]],"viewport":{"width":1280,"height":720},"timestamp":9231.222,"wallTime":1749104559457,"collectionTime":2.599999999627471,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@29","startTime":9239.821,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{"type":"png","fullPage":true},"stepId":"pw:api@14","pageId":"page@c6886d2b5677aac36ea20fab1f024056","beforeSnapshot":"before@call@29"}
{"type":"frame-snapshot","snapshot":{"callId":"call@29","snapshotName":"before@call@29","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":[[1,46]],"viewport":{"width":1280,"height":720},"timestamp":9242.391,"wallTime":1749104559469,"collectionTime":0.900000000372529,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@29","time":9242.841,"message":"taking page screenshot"}
{"type":"log","callId":"call@29","time":9245.352,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@29","time":9253.149,"message":"fonts loaded"}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":9258.895,"frameSwapWallTime":1749104559474.528}
{"type":"after","callId":"call@29","endTime":9354.961,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@29"}
{"type":"frame-snapshot","snapshot":{"callId":"call@29","snapshotName":"after@call@29","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":[[2,46]],"viewport":{"width":1280,"height":720},"timestamp":9360.294,"wallTime":1749104559587,"collectionTime":2.299999998882413,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@31","startTime":9373.306,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"internal:role=textbox[name=\"Sign in with your KPMG email\"i]","strict":true,"timeout":10000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@16","pageId":"page@c6886d2b5677aac36ea20fab1f024056","beforeSnapshot":"before@call@31"}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":9374.069,"frameSwapWallTime":1749104559590.142}
{"type":"frame-snapshot","snapshot":{"callId":"call@31","snapshotName":"before@call@31","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":[[3,46]],"viewport":{"width":1280,"height":720},"timestamp":9375.902,"wallTime":1749104559603,"collectionTime":0.7999999988824129,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@31","time":9377.418,"message":"waiting for getByRole('textbox', { name: 'Sign in with your KPMG email' }) to be visible"}
{"type":"log","callId":"call@31","time":9386.955,"message":"  locator resolved to visible <input id=\"i0116\" type=\"email\" name=\"loginfmt\" maxlength=\"113\" aria-required=\"true\" autocomplete=\"username\" data-report-attached=\"1\" data-report-trigger=\"click\" data-report-value=\"Email_Phone_Skype_Entry\" data-report-event=\"Signin_Email_Phone_Skype\" aria-describedby=\"loginHeader usernameError\" aria-label=\"Sign in with your KPMG email address\" placeholder=\"Sign in with your KPMG email address\" class=\"form-control ltr_override input ext-input text-box ext-text-box\" data-bind=\"↵                    attr:…/>"}
{"type":"after","callId":"call@31","endTime":9387.077,"result":{},"afterSnapshot":"after@call@31"}
{"type":"frame-snapshot","snapshot":{"callId":"call@31","snapshotName":"after@call@31","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":[[4,46]],"viewport":{"width":1280,"height":720},"timestamp":9390.183,"wallTime":1749104559617,"collectionTime":1,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@33","startTime":9393.082,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{"type":"png","fullPage":true},"stepId":"pw:api@17","pageId":"page@c6886d2b5677aac36ea20fab1f024056","beforeSnapshot":"before@call@33"}
{"type":"frame-snapshot","snapshot":{"callId":"call@33","snapshotName":"before@call@33","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":[[5,46]],"viewport":{"width":1280,"height":720},"timestamp":9396.414,"wallTime":1749104559623,"collectionTime":1.300000000745058,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@33","time":9397.087,"message":"taking page screenshot"}
{"type":"log","callId":"call@33","time":9399.001,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@33","time":9400.842,"message":"fonts loaded"}
{"type":"after","callId":"call@33","endTime":9503.002,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@33"}
{"type":"frame-snapshot","snapshot":{"callId":"call@33","snapshotName":"after@call@33","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":[[6,46]],"viewport":{"width":1280,"height":720},"timestamp":9507.484,"wallTime":1749104559734,"collectionTime":1.700000001117587,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@35","startTime":9514.836,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{"type":"png","fullPage":true},"stepId":"pw:api@19","pageId":"page@c6886d2b5677aac36ea20fab1f024056","beforeSnapshot":"before@call@35"}
{"type":"frame-snapshot","snapshot":{"callId":"call@35","snapshotName":"before@call@35","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":[[7,46]],"viewport":{"width":1280,"height":720},"timestamp":9522.145,"wallTime":1749104559748,"collectionTime":1.900000000372529,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@35","time":9522.953,"message":"taking page screenshot"}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":9524.655,"frameSwapWallTime":1749104559743.1719}
{"type":"log","callId":"call@35","time":9525.08,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@35","time":9530.956,"message":"fonts loaded"}
{"type":"after","callId":"call@35","endTime":9635.053,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@35"}
{"type":"frame-snapshot","snapshot":{"callId":"call@35","snapshotName":"after@call@35","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":[[8,46]],"viewport":{"width":1280,"height":720},"timestamp":9639.522,"wallTime":1749104559866,"collectionTime":1.5,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@37","startTime":9646.187,"apiName":"locator.fill","class":"Frame","method":"fill","params":{"selector":"internal:role=textbox[name=\"Sign in with your KPMG email\"i]","strict":true,"value":"<EMAIL>"},"stepId":"pw:api@21","pageId":"page@c6886d2b5677aac36ea20fab1f024056","beforeSnapshot":"before@call@37"}
{"type":"frame-snapshot","snapshot":{"callId":"call@37","snapshotName":"before@call@37","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":[[9,46]],"viewport":{"width":1280,"height":720},"timestamp":9654.386,"wallTime":1749104559877,"collectionTime":1,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@37","time":9655.774,"message":"waiting for getByRole('textbox', { name: 'Sign in with your KPMG email' })"}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":9659.137,"frameSwapWallTime":1749104559877.2659}
{"type":"log","callId":"call@37","time":9661.467,"message":"  locator resolved to <input id=\"i0116\" type=\"email\" name=\"loginfmt\" maxlength=\"113\" aria-required=\"true\" autocomplete=\"username\" data-report-attached=\"1\" data-report-trigger=\"click\" data-report-value=\"Email_Phone_Skype_Entry\" data-report-event=\"Signin_Email_Phone_Skype\" aria-describedby=\"loginHeader usernameError\" aria-label=\"Sign in with your KPMG email address\" placeholder=\"Sign in with your KPMG email address\" class=\"form-control ltr_override input ext-input text-box ext-text-box\" data-bind=\"↵                    attr:…/>"}
{"type":"log","callId":"call@37","time":9665.43,"message":"  fill(\"<EMAIL>\")"}
{"type":"log","callId":"call@37","time":9665.467,"message":"attempting fill action"}
{"type":"input","callId":"call@37","inputSnapshot":"input@call@37"}
{"type":"frame-snapshot","snapshot":{"callId":"call@37","snapshotName":"input@call@37","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":["HTML",{"dir":"ltr","class":"","lang":"en"},[[12,47]],[[12,48]],["BODY",{"data-bind":"defineGlobals: ServerData, bodyCssClass","class":"cb","style":"display: block;"},[[12,49]],[[12,50]],["DIV",{},[[12,51]],[[12,52]],[[12,53]],[[12,54]],["FORM",{"name":"f1","id":"i0281","novalidate":"novalidate","spellcheck":"false","method":"post","target":"_top","autocomplete":"off","data-bind":"visible: !isLoginPageHidden(), autoSubmit: forceSubmit, attr: { action: postUrl }, ariaHidden: !!activeDialog(), css: { 'provide-min-height': svr.fUseMinHeight }","action":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/login","class":"provide-min-height"},[[12,55]],[[12,56]],["DIV",{"class":"login-paginated-page","data-bind":"component: { name: 'master-page',\n        publicMethods: masterPageMethods,\n        params: {\n            serverData: svr,\n            showButtons: svr.fShowButtons,\n            showFooterLinks: true,\n            useWizardBehavior: svr.fUseWizardBehavior,\n            handleWizardButtons: false,\n            password: password,\n            hideFromAria: ariaHidden },\n        event: {\n            footerAgreementClick: footer_agreementClick } }"},[[12,57]],[[12,58]],[[12,59]],[[12,60]],[[12,61]],["DIV",{"id":"lightboxTemplateContainer","data-bind":"component: { name: 'lightbox-template', params: { serverData: svr, showHeader: $page.showHeader(), headerLogo: $page.headerLogo() } }, css: { 'provide-min-height': svr.fUseMinHeight }","class":"provide-min-height"},[[12,62]],[[12,74]],[[12,75]],[[12,76]],[[12,77]],["DIV",{"class":"outer","data-bind":"css: { 'app': $page.backgroundLogoUrl }"},[[12,78]],[[12,79]],["DIV",{"class":"template-section main-section"},[[12,80]],["DIV",{"data-bind":"externalCss: { 'middle': true }","class":"middle ext-middle"},[[12,81]],["DIV",{"class":"full-height","data-bind":"component: { name: 'content-control', params: { serverData: svr, isVerticalSplitTemplate: $page.isVerticalSplitTemplate(), hasHeader: showHeader } }"},[[12,82]],[[12,83]],["DIV",{"class":"flex-column"},[[12,84]],[[12,85]],[[12,86]],["DIV",{"class":"win-scroll"},[[12,87]],["DIV",{"id":"lightbox","data-bind":"\n            animationEnd: $page.paginationControlHelper.animationEnd,\n            externalCss: { 'sign-in-box': true },\n            css: {\n                'inner':  $content.isVerticalSplitTemplate,\n                'vertical-split-content': $content.isVerticalSplitTemplate,\n                'app': $page.backgroundLogoUrl,\n                'wide': $page.paginationControlHelper.useWiderWidth,\n                'fade-in-lightbox': $page.fadeInLightBox,\n                'has-popup': $page.showFedCredAndNewSession && ($page.showFedCredButtons() || $page.newSession()),\n                'transparent-lightbox': $page.backgroundControlMethods() && $page.backgroundControlMethods().useTransparentLightBox,\n                'lightbox-bottom-margin-debug': $page.showDebugDetails,\n                'has-header': $content.hasHeader }","class":"sign-in-box ext-sign-in-box fade-in-lightbox has-popup"},[[12,88]],[[12,89]],[[12,90]],[[12,91]],[[12,92]],[[12,93]],[[12,94]],[[12,104]],[[12,105]],[[12,106]],[[12,107]],[[12,108]],["DIV",{"role":"main","data-bind":"component: { name: 'pagination-control',\n            publicMethods: paginationControlMethods,\n            params: {\n                enableCssAnimation: svr.fEnableCssAnimation,\n                disableAnimationIfAnimationEndUnsupported: svr.fDisableAnimationIfAnimationEndUnsupported,\n                initialViewId: initialViewId,\n                currentViewId: currentViewId,\n                initialSharedData: initialSharedData,\n                initialError: $loginPage.getServerError() },\n            event: {\n                cancel: paginationControl_onCancel,\n                load: paginationControlHelper.onLoad,\n                unload: paginationControlHelper.onUnload,\n                loadView: view_onLoadView,\n                showView: view_onShow,\n                setLightBoxFadeIn: view_onSetLightBoxFadeIn,\n                animationStateChange: paginationControl_onAnimationStateChange } }"},[[12,109]],["DIV",{"data-bind":"css: { 'zero-opacity': hidePaginatedView() }","class":""},[[12,110]],[[12,111]],["DIV",{"class":"pagination-view animate slide-in-next","data-bind":"css: {\n        'has-identity-banner': showIdentityBanner() && (sharedData.displayName || svr.sPOST_Username),\n        'zero-opacity': hidePaginatedView.hideSubView(),\n        'animate': animate(),\n        'slide-out-next': animate.isSlideOutNext(),\n        'slide-in-next': animate.isSlideInNext(),\n        'slide-out-back': animate.isSlideOutBack(),\n        'slide-in-back': animate.isSlideInBack() }"},[[12,112]],[[12,113]],[[12,114]],["DIV",{"data-viewid":"1","data-showfedcredbutton":"true","data-bind":"pageViewComponent: { name: 'login-paginated-username-view',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    isInitialView: isInitialState,\n                    displayName: sharedData.displayName,\n                    otherIdpRedirectUrl: sharedData.otherIdpRedirectUrl,\n                    prefillNames: $loginPage.prefillNames,\n                    flowToken: sharedData.flowToken,\n                    availableSignupCreds: sharedData.availableSignupCreds,\n                    customStrings: $loginPage.stringCustomizationObservables.customStrings(),\n                    isCustomizationFailure: $loginPage.stringCustomizationObservables.isCustomStringsLoadFailure(),\n                    userIdLabel: $loginPage.userIdLabel,\n                    cantAccessYourAccountText: $loginPage.cantAccessYourAccountText,\n                    hideAccountResetCredentials: $loginPage.hideAccountResetCredentials,\n                    accessRecoveryLink: $loginPage.accessRecoveryLink,\n                    boilerPlateText: $loginPage.boilerPlateText },\n                event: {\n                    restoreIsRecoveryAttemptPost: $loginPage.view_onRestoreIsRecoveryAttemptPost,\n                    redirect: $loginPage.view_onRedirect,\n                    updateDFPUrl: $loginPage.view_onUpdateDFPUrl,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest,\n                    registerDialog: $loginPage.view_onRegisterDialog,\n                    unregisterDialog: $loginPage.view_onUnregisterDialog,\n                    showDialog: $loginPage.view_onShowDialog,\n                    updateAvailableCredsWithoutUsername: $loginPage.view_onUpdateAvailableCreds,\n                    agreementClick: $loginPage.footer_agreementClick } }"},[[12,115]],[[12,126]],[[12,127]],[[12,128]],["DIV",{"class":"row"},[[12,129]],[[12,130]],[[12,131]],[[12,134]],[[12,135]],[[12,136]],["DIV",{"class":"form-group col-md-24"},[[12,137]],[[12,138]],[[12,139]],["DIV",{"class":"placeholderContainer","data-bind":"component: { name: 'placeholder-textbox-field',\n            publicMethods: usernameTextbox.placeholderTextboxMethods,\n            params: {\n                serverData: svr,\n                hintText: svr.fEnableLivePreview ? userIdLabel : tenantBranding.unsafe_userIdLabel || str['STR_SSSU_Username_Hint'] || str['CT_PWD_STR_Email_Example'],\n                hintCss: 'placeholder' + (!svr.fAllowPhoneSignIn ? ' ltr_override' : '') },\n            event: {\n                updateFocus: usernameTextbox.textbox_onUpdateFocus } }"},[[12,140]],[[12,141]],["INPUT",{"__playwright_value_":"","__playwright_target__":"call@37","type":"email","name":"loginfmt","id":"i0116","maxlength":"113","class":"form-control ltr_override input ext-input text-box ext-text-box","aria-required":"true","data-report-event":"Signin_Email_Phone_Skype","data-report-trigger":"click","data-report-value":"Email_Phone_Skype_Entry","data-bind":"\n                    attr: { lang: svr.fApplyAsciiRegexOnInput ? null : 'en',\n                    autocomplete: svr.fEnablePasskeyAutofillUI ? 'username webauthn' : 'username' },\n                    externalCss: {\n                        'input': true,\n                        'text-box': true,\n                        'has-error': usernameTextbox.error },\n                    ariaLabel: tenantBranding.unsafe_userIdLabel || str['CT_PWD_STR_Username_AriaLabel'],\n                    ariaDescribedBy:\n                     svr.fWin10HostAccessibilityFix ?\n                        'winBodyHeader winBodySubHeader loginHeader' + (pageDescription && !svr.fHideLoginDesc ? ' loginDescription usernameError' : ' usernameError') :\n                        'loginHeader' + (pageDescription && !svr.fHideLoginDesc ? ' loginDescription usernameError' : ' usernameError'),\n                    textInput: usernameTextbox.value,\n                    hasFocusEx: usernameTextbox.focused,\n                    placeholder: $placeholderText","autocomplete":"username","aria-label":"Sign in with your KPMG email address","aria-describedby":"loginHeader usernameError","placeholder":"Sign in with your KPMG email address","data-report-attached":"1","style":""}],[[12,143]],[[10,1]],[[12,145]],[[12,146]],[[12,147]]],[[12,149]],[[12,150]]],[[12,152]]],[[12,154]],[[12,180]],[[12,181]],[[12,182]],[[10,10]],[[12,202]],[[12,203]],[[12,213]],[[12,214]]],[[12,216]],[[12,217]],[[12,218]],[[12,219]],[[12,220]],[[12,221]],[[12,222]],[[12,223]],[[12,224]],[[12,225]],[[12,226]],[[12,227]],[[12,228]],[[12,229]],[[12,230]],[[12,231]],[[12,232]],[[12,233]],[[12,234]],[[12,235]],[[12,236]],[[12,237]],[[12,238]],[[12,239]],[[12,240]],[[12,241]],[[12,242]],[[12,243]],[[12,244]],[[12,245]],[[12,246]],[[12,247]],[[12,248]],[[12,249]],[[12,250]],[[12,251]],[[12,252]],[[12,253]],[[12,254]],[[12,255]],[[12,256]],[[12,257]],[[12,258]],[[12,259]],[[12,260]],[[12,261]],[[12,262]],[[12,263]],[[12,264]],[[12,265]],[[12,266]],[[12,267]],[[12,268]],[[12,269]],[[12,270]],[[12,271]],[[12,272]],[[12,273]],[[12,274]],[[12,275]],[[12,276]],[[12,277]],[[12,278]],[[12,279]],[[12,280]],[[12,281]],[[12,282]],[[12,283]],[[12,284]],[[12,285]],[[12,286]],[[12,287]],[[12,288]],[[12,289]],[[12,290]],[[12,291]],[[12,292]],[[12,293]],[[12,294]],[[12,295]],[[12,296]],[[12,297]],[[12,298]],[[12,299]],[[12,300]],[[12,301]],[[12,302]],[[12,303]],[[12,304]]],[[12,306]]]],[[12,309]],[[12,310]],[[10,15]],[[12,312]],[[10,16]],[[12,314]],[[10,17]],[[12,316]],[[10,18]],[[12,318]],[[12,319]],[[10,19]],[[12,321]],[[12,322]],[[12,323]],[[10,20]],[[12,325]],[[10,21]],[[12,327]],[[10,22]],[[12,329]],[[10,23]],[[12,331]],[[10,24]],[[12,333]],[[10,25]],[[12,335]],[[10,26]],[[12,337]],[[10,27]],[[12,339]],[[10,28]],[[12,341]],[[10,29]],[[12,343]],[[10,30]],[[12,345]],[[12,346]],[[12,347]],[[10,31]],[[12,349]],[[12,350]],[[12,351]],[[10,33]],[[12,355]],[[12,356]]],[[12,358]],[[12,359]],[[12,360]],[[12,361]],[[12,362]],[[12,410]],[[12,411]],[[12,412]],[[12,413]],[[12,414]],[[12,415]],[[12,416]],[[12,417]],[[12,418]]],[[12,420]]],[[12,422]]],[[12,424]]],[[12,426]]],[[12,428]],[[12,429]],[[12,457]],[[12,458]],[[12,459]]],[[12,461]]],[[12,463]],[[12,464]],[[12,465]],[[12,466]]],[[12,468]],[[12,469]]],[[12,471]],[[12,472]],[[12,473]],[[12,474]]]]],"viewport":{"width":1280,"height":720},"timestamp":9670.529,"wallTime":1749104559897,"collectionTime":1.099999999627471,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@37","time":9671.53,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@37","endTime":9679.181,"afterSnapshot":"after@call@37"}
{"type":"frame-snapshot","snapshot":{"callId":"call@37","snapshotName":"after@call@37","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":["HTML",{"dir":"ltr","class":"","lang":"en"},[[13,47]],[[13,48]],["BODY",{"data-bind":"defineGlobals: ServerData, bodyCssClass","class":"cb","style":"display: block;"},[[13,49]],[[13,50]],["DIV",{},[[13,51]],[[13,52]],[[13,53]],[[13,54]],["FORM",{"name":"f1","id":"i0281","novalidate":"novalidate","spellcheck":"false","method":"post","target":"_top","autocomplete":"off","data-bind":"visible: !isLoginPageHidden(), autoSubmit: forceSubmit, attr: { action: postUrl }, ariaHidden: !!activeDialog(), css: { 'provide-min-height': svr.fUseMinHeight }","action":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/login","class":"provide-min-height"},[[13,55]],[[13,56]],["DIV",{"class":"login-paginated-page","data-bind":"component: { name: 'master-page',\n        publicMethods: masterPageMethods,\n        params: {\n            serverData: svr,\n            showButtons: svr.fShowButtons,\n            showFooterLinks: true,\n            useWizardBehavior: svr.fUseWizardBehavior,\n            handleWizardButtons: false,\n            password: password,\n            hideFromAria: ariaHidden },\n        event: {\n            footerAgreementClick: footer_agreementClick } }"},[[13,57]],[[13,58]],[[13,59]],[[13,60]],[[13,61]],["DIV",{"id":"lightboxTemplateContainer","data-bind":"component: { name: 'lightbox-template', params: { serverData: svr, showHeader: $page.showHeader(), headerLogo: $page.headerLogo() } }, css: { 'provide-min-height': svr.fUseMinHeight }","class":"provide-min-height"},[[13,62]],[[13,74]],[[13,75]],[[13,76]],[[13,77]],["DIV",{"class":"outer","data-bind":"css: { 'app': $page.backgroundLogoUrl }"},[[13,78]],[[13,79]],["DIV",{"class":"template-section main-section"},[[13,80]],["DIV",{"data-bind":"externalCss: { 'middle': true }","class":"middle ext-middle"},[[13,81]],["DIV",{"class":"full-height","data-bind":"component: { name: 'content-control', params: { serverData: svr, isVerticalSplitTemplate: $page.isVerticalSplitTemplate(), hasHeader: showHeader } }"},[[13,82]],[[13,83]],["DIV",{"class":"flex-column"},[[13,84]],[[13,85]],[[13,86]],["DIV",{"class":"win-scroll"},[[13,87]],["DIV",{"id":"lightbox","data-bind":"\n            animationEnd: $page.paginationControlHelper.animationEnd,\n            externalCss: { 'sign-in-box': true },\n            css: {\n                'inner':  $content.isVerticalSplitTemplate,\n                'vertical-split-content': $content.isVerticalSplitTemplate,\n                'app': $page.backgroundLogoUrl,\n                'wide': $page.paginationControlHelper.useWiderWidth,\n                'fade-in-lightbox': $page.fadeInLightBox,\n                'has-popup': $page.showFedCredAndNewSession && ($page.showFedCredButtons() || $page.newSession()),\n                'transparent-lightbox': $page.backgroundControlMethods() && $page.backgroundControlMethods().useTransparentLightBox,\n                'lightbox-bottom-margin-debug': $page.showDebugDetails,\n                'has-header': $content.hasHeader }","class":"sign-in-box ext-sign-in-box fade-in-lightbox has-popup"},[[13,88]],[[13,89]],[[13,90]],[[13,91]],[[13,92]],[[13,93]],[[13,94]],[[13,104]],[[13,105]],[[13,106]],[[13,107]],[[13,108]],["DIV",{"role":"main","data-bind":"component: { name: 'pagination-control',\n            publicMethods: paginationControlMethods,\n            params: {\n                enableCssAnimation: svr.fEnableCssAnimation,\n                disableAnimationIfAnimationEndUnsupported: svr.fDisableAnimationIfAnimationEndUnsupported,\n                initialViewId: initialViewId,\n                currentViewId: currentViewId,\n                initialSharedData: initialSharedData,\n                initialError: $loginPage.getServerError() },\n            event: {\n                cancel: paginationControl_onCancel,\n                load: paginationControlHelper.onLoad,\n                unload: paginationControlHelper.onUnload,\n                loadView: view_onLoadView,\n                showView: view_onShow,\n                setLightBoxFadeIn: view_onSetLightBoxFadeIn,\n                animationStateChange: paginationControl_onAnimationStateChange } }"},[[13,109]],["DIV",{"data-bind":"css: { 'zero-opacity': hidePaginatedView() }","class":""},[[13,110]],[[13,111]],["DIV",{"class":"pagination-view animate slide-in-next","data-bind":"css: {\n        'has-identity-banner': showIdentityBanner() && (sharedData.displayName || svr.sPOST_Username),\n        'zero-opacity': hidePaginatedView.hideSubView(),\n        'animate': animate(),\n        'slide-out-next': animate.isSlideOutNext(),\n        'slide-in-next': animate.isSlideInNext(),\n        'slide-out-back': animate.isSlideOutBack(),\n        'slide-in-back': animate.isSlideInBack() }"},[[13,112]],[[13,113]],[[13,114]],["DIV",{"data-viewid":"1","data-showfedcredbutton":"true","data-bind":"pageViewComponent: { name: 'login-paginated-username-view',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    isInitialView: isInitialState,\n                    displayName: sharedData.displayName,\n                    otherIdpRedirectUrl: sharedData.otherIdpRedirectUrl,\n                    prefillNames: $loginPage.prefillNames,\n                    flowToken: sharedData.flowToken,\n                    availableSignupCreds: sharedData.availableSignupCreds,\n                    customStrings: $loginPage.stringCustomizationObservables.customStrings(),\n                    isCustomizationFailure: $loginPage.stringCustomizationObservables.isCustomStringsLoadFailure(),\n                    userIdLabel: $loginPage.userIdLabel,\n                    cantAccessYourAccountText: $loginPage.cantAccessYourAccountText,\n                    hideAccountResetCredentials: $loginPage.hideAccountResetCredentials,\n                    accessRecoveryLink: $loginPage.accessRecoveryLink,\n                    boilerPlateText: $loginPage.boilerPlateText },\n                event: {\n                    restoreIsRecoveryAttemptPost: $loginPage.view_onRestoreIsRecoveryAttemptPost,\n                    redirect: $loginPage.view_onRedirect,\n                    updateDFPUrl: $loginPage.view_onUpdateDFPUrl,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest,\n                    registerDialog: $loginPage.view_onRegisterDialog,\n                    unregisterDialog: $loginPage.view_onUnregisterDialog,\n                    showDialog: $loginPage.view_onShowDialog,\n                    updateAvailableCredsWithoutUsername: $loginPage.view_onUpdateAvailableCreds,\n                    agreementClick: $loginPage.footer_agreementClick } }"},[[13,115]],[[13,126]],[[13,127]],[[13,128]],["DIV",{"class":"row"},[[13,129]],[[13,130]],[[13,131]],[[13,134]],[[13,135]],[[13,136]],["DIV",{"class":"form-group col-md-24"},[[13,137]],[[13,138]],[[13,139]],["DIV",{"class":"placeholderContainer","data-bind":"component: { name: 'placeholder-textbox-field',\n            publicMethods: usernameTextbox.placeholderTextboxMethods,\n            params: {\n                serverData: svr,\n                hintText: svr.fEnableLivePreview ? userIdLabel : tenantBranding.unsafe_userIdLabel || str['STR_SSSU_Username_Hint'] || str['CT_PWD_STR_Email_Example'],\n                hintCss: 'placeholder' + (!svr.fAllowPhoneSignIn ? ' ltr_override' : '') },\n            event: {\n                updateFocus: usernameTextbox.textbox_onUpdateFocus } }"},[[13,140]],[[13,141]],["INPUT",{"__playwright_value_":"<EMAIL>","__playwright_target__":"call@37","type":"email","name":"loginfmt","id":"i0116","maxlength":"113","class":"form-control ltr_override input ext-input text-box ext-text-box","aria-required":"true","data-report-event":"Signin_Email_Phone_Skype","data-report-trigger":"click","data-report-value":"Email_Phone_Skype_Entry","data-bind":"\n                    attr: { lang: svr.fApplyAsciiRegexOnInput ? null : 'en',\n                    autocomplete: svr.fEnablePasskeyAutofillUI ? 'username webauthn' : 'username' },\n                    externalCss: {\n                        'input': true,\n                        'text-box': true,\n                        'has-error': usernameTextbox.error },\n                    ariaLabel: tenantBranding.unsafe_userIdLabel || str['CT_PWD_STR_Username_AriaLabel'],\n                    ariaDescribedBy:\n                     svr.fWin10HostAccessibilityFix ?\n                        'winBodyHeader winBodySubHeader loginHeader' + (pageDescription && !svr.fHideLoginDesc ? ' loginDescription usernameError' : ' usernameError') :\n                        'loginHeader' + (pageDescription && !svr.fHideLoginDesc ? ' loginDescription usernameError' : ' usernameError'),\n                    textInput: usernameTextbox.value,\n                    hasFocusEx: usernameTextbox.focused,\n                    placeholder: $placeholderText","autocomplete":"username","aria-label":"Sign in with your KPMG email address","aria-describedby":"loginHeader usernameError","placeholder":"Sign in with your KPMG email address","data-report-attached":"1","style":""}],[[13,143]],[[11,1]],[[13,145]],[[13,146]],[[13,147]]],[[13,149]],[[13,150]]],[[13,152]]],[[13,154]],[[13,180]],[[13,181]],[[13,182]],[[11,10]],[[13,202]],[[13,203]],[[13,213]],[[13,214]]],[[13,216]],[[13,217]],[[13,218]],[[13,219]],[[13,220]],[[13,221]],[[13,222]],[[13,223]],[[13,224]],[[13,225]],[[13,226]],[[13,227]],[[13,228]],[[13,229]],[[13,230]],[[13,231]],[[13,232]],[[13,233]],[[13,234]],[[13,235]],[[13,236]],[[13,237]],[[13,238]],[[13,239]],[[13,240]],[[13,241]],[[13,242]],[[13,243]],[[13,244]],[[13,245]],[[13,246]],[[13,247]],[[13,248]],[[13,249]],[[13,250]],[[13,251]],[[13,252]],[[13,253]],[[13,254]],[[13,255]],[[13,256]],[[13,257]],[[13,258]],[[13,259]],[[13,260]],[[13,261]],[[13,262]],[[13,263]],[[13,264]],[[13,265]],[[13,266]],[[13,267]],[[13,268]],[[13,269]],[[13,270]],[[13,271]],[[13,272]],[[13,273]],[[13,274]],[[13,275]],[[13,276]],[[13,277]],[[13,278]],[[13,279]],[[13,280]],[[13,281]],[[13,282]],[[13,283]],[[13,284]],[[13,285]],[[13,286]],[[13,287]],[[13,288]],[[13,289]],[[13,290]],[[13,291]],[[13,292]],[[13,293]],[[13,294]],[[13,295]],[[13,296]],[[13,297]],[[13,298]],[[13,299]],[[13,300]],[[13,301]],[[13,302]],[[13,303]],[[13,304]]],[[13,306]]]],[[13,309]],[[13,310]],[[11,15]],[[13,312]],[[11,16]],[[13,314]],[[11,17]],[[13,316]],[[11,18]],[[13,318]],[[13,319]],[[11,19]],[[13,321]],[[13,322]],[[13,323]],[[11,20]],[[13,325]],[[11,21]],[[13,327]],[[11,22]],[[13,329]],[[11,23]],[[13,331]],[[11,24]],[[13,333]],[[11,25]],[[13,335]],[[11,26]],[[13,337]],[[11,27]],[[13,339]],[[11,28]],[[13,341]],[[11,29]],[[13,343]],[[11,30]],[[13,345]],[[13,346]],[[13,347]],[[11,31]],[[13,349]],[[13,350]],[[13,351]],[[11,33]],[[13,355]],[[13,356]]],[[13,358]],[[13,359]],[[13,360]],[[13,361]],[[13,362]],[[13,410]],[[13,411]],[[13,412]],[[13,413]],[[13,414]],[[13,415]],[[13,416]],[[13,417]],[[13,418]]],[[13,420]]],[[13,422]]],[[13,424]]],[[13,426]]],[[13,428]],[[13,429]],[[13,457]],[[13,458]],[[13,459]]],[[13,461]]],[[13,463]],[[13,464]],[[13,465]],[[13,466]]],[[13,468]],[[13,469]]],[[13,471]],[[13,472]],[[13,473]],[[13,474]]]]],"viewport":{"width":1280,"height":720},"timestamp":9688.117,"wallTime":1749104559914,"collectionTime":1.300000000745058,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@39","startTime":9689.844,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{"type":"png","fullPage":true},"stepId":"pw:api@22","pageId":"page@c6886d2b5677aac36ea20fab1f024056","beforeSnapshot":"before@call@39"}
{"type":"frame-snapshot","snapshot":{"callId":"call@39","snapshotName":"before@call@39","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":[[1,20]],"viewport":{"width":1280,"height":720},"timestamp":9691.821,"wallTime":1749104559919,"collectionTime":0.7999999988824129,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@39","time":9692.375,"message":"taking page screenshot"}
{"type":"log","callId":"call@39","time":9693.572,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@39","time":9694.828,"message":"fonts loaded"}
{"type":"after","callId":"call@39","endTime":9789.905,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@39"}
{"type":"frame-snapshot","snapshot":{"callId":"call@39","snapshotName":"after@call@39","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":[[2,20]],"viewport":{"width":1280,"height":720},"timestamp":9794.212,"wallTime":1749104560021,"collectionTime":1.200000001117587,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@41","startTime":9800.071,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{"type":"png","fullPage":true},"stepId":"pw:api@24","pageId":"page@c6886d2b5677aac36ea20fab1f024056","beforeSnapshot":"before@call@41"}
{"type":"frame-snapshot","snapshot":{"callId":"call@41","snapshotName":"before@call@41","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":[[3,20]],"viewport":{"width":1280,"height":720},"timestamp":9803.312,"wallTime":1749104560030,"collectionTime":1.5,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@41","time":9803.887,"message":"taking page screenshot"}
{"type":"log","callId":"call@41","time":9810.356,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@41","time":9813.217,"message":"fonts loaded"}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":9816.888,"frameSwapWallTime":1749104560031.327}
{"type":"after","callId":"call@41","endTime":9922.972,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@41"}
{"type":"frame-snapshot","snapshot":{"callId":"call@41","snapshotName":"after@call@41","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":[[4,20]],"viewport":{"width":1280,"height":720},"timestamp":9927.531,"wallTime":1749104560154,"collectionTime":2.199999999254942,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@43","startTime":9935.253,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"internal:role=button[name=\"Next\"i]","strict":true},"stepId":"pw:api@26","pageId":"page@c6886d2b5677aac36ea20fab1f024056","beforeSnapshot":"before@call@43"}
{"type":"frame-snapshot","snapshot":{"callId":"call@43","snapshotName":"before@call@43","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":[[5,20]],"viewport":{"width":1280,"height":720},"timestamp":9943.706,"wallTime":1749104560170,"collectionTime":1.699999999254942,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@43","time":9944.507,"message":"waiting for getByRole('button', { name: 'Next' })"}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":9946.399,"frameSwapWallTime":1749104560164.53}
{"type":"log","callId":"call@43","time":9950.23,"message":"  locator resolved to <input value=\"Next\" type=\"submit\" id=\"idSIButton9\" data-report-attached=\"1\" data-report-value=\"Submit\" data-report-trigger=\"click\" data-report-event=\"Signin_Submit\" class=\"win-button button_primary high-contrast-overrides button ext-button primary ext-primary\" data-bind=\"↵                attr: primaryButtonAttributes,↵                css: { 'high-contrast-overrides': svr.fUseHighContrastDetectionMode },↵                externalCss: {↵                    'button': true,↵                    'primary': …/>"}
{"type":"log","callId":"call@43","time":9954.06,"message":"attempting click action"}
{"type":"log","callId":"call@43","time":9954.582,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@43","time":9969.281,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@43","time":9969.303,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@43","time":9971.037,"message":"  done scrolling"}
{"type":"input","callId":"call@43","point":{"x":762,"y":303.71},"inputSnapshot":"input@call@43"}
{"type":"frame-snapshot","snapshot":{"callId":"call@43","snapshotName":"input@call@43","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":["HTML",{"dir":"ltr","class":"","lang":"en"},[[19,47]],[[19,48]],["BODY",{"data-bind":"defineGlobals: ServerData, bodyCssClass","class":"cb","style":"display: block;"},[[19,49]],[[19,50]],["DIV",{},[[19,51]],[[19,52]],[[19,53]],[[19,54]],["FORM",{"name":"f1","id":"i0281","novalidate":"novalidate","spellcheck":"false","method":"post","target":"_top","autocomplete":"off","data-bind":"visible: !isLoginPageHidden(), autoSubmit: forceSubmit, attr: { action: postUrl }, ariaHidden: !!activeDialog(), css: { 'provide-min-height': svr.fUseMinHeight }","action":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/login","class":"provide-min-height"},[[19,55]],[[19,56]],["DIV",{"class":"login-paginated-page","data-bind":"component: { name: 'master-page',\n        publicMethods: masterPageMethods,\n        params: {\n            serverData: svr,\n            showButtons: svr.fShowButtons,\n            showFooterLinks: true,\n            useWizardBehavior: svr.fUseWizardBehavior,\n            handleWizardButtons: false,\n            password: password,\n            hideFromAria: ariaHidden },\n        event: {\n            footerAgreementClick: footer_agreementClick } }"},[[19,57]],[[19,58]],[[19,59]],[[19,60]],[[19,61]],["DIV",{"id":"lightboxTemplateContainer","data-bind":"component: { name: 'lightbox-template', params: { serverData: svr, showHeader: $page.showHeader(), headerLogo: $page.headerLogo() } }, css: { 'provide-min-height': svr.fUseMinHeight }","class":"provide-min-height"},[[19,62]],[[19,74]],[[19,75]],[[19,76]],[[19,77]],["DIV",{"class":"outer","data-bind":"css: { 'app': $page.backgroundLogoUrl }"},[[19,78]],[[19,79]],["DIV",{"class":"template-section main-section"},[[19,80]],["DIV",{"data-bind":"externalCss: { 'middle': true }","class":"middle ext-middle"},[[19,81]],["DIV",{"class":"full-height","data-bind":"component: { name: 'content-control', params: { serverData: svr, isVerticalSplitTemplate: $page.isVerticalSplitTemplate(), hasHeader: showHeader } }"},[[19,82]],[[19,83]],["DIV",{"class":"flex-column"},[[19,84]],[[19,85]],[[19,86]],["DIV",{"class":"win-scroll"},[[19,87]],["DIV",{"id":"lightbox","data-bind":"\n            animationEnd: $page.paginationControlHelper.animationEnd,\n            externalCss: { 'sign-in-box': true },\n            css: {\n                'inner':  $content.isVerticalSplitTemplate,\n                'vertical-split-content': $content.isVerticalSplitTemplate,\n                'app': $page.backgroundLogoUrl,\n                'wide': $page.paginationControlHelper.useWiderWidth,\n                'fade-in-lightbox': $page.fadeInLightBox,\n                'has-popup': $page.showFedCredAndNewSession && ($page.showFedCredButtons() || $page.newSession()),\n                'transparent-lightbox': $page.backgroundControlMethods() && $page.backgroundControlMethods().useTransparentLightBox,\n                'lightbox-bottom-margin-debug': $page.showDebugDetails,\n                'has-header': $content.hasHeader }","class":"sign-in-box ext-sign-in-box fade-in-lightbox has-popup"},[[19,88]],[[19,89]],[[19,90]],[[19,91]],[[19,92]],[[19,93]],[[19,94]],[[19,104]],[[19,105]],[[19,106]],[[19,107]],[[19,108]],["DIV",{"role":"main","data-bind":"component: { name: 'pagination-control',\n            publicMethods: paginationControlMethods,\n            params: {\n                enableCssAnimation: svr.fEnableCssAnimation,\n                disableAnimationIfAnimationEndUnsupported: svr.fDisableAnimationIfAnimationEndUnsupported,\n                initialViewId: initialViewId,\n                currentViewId: currentViewId,\n                initialSharedData: initialSharedData,\n                initialError: $loginPage.getServerError() },\n            event: {\n                cancel: paginationControl_onCancel,\n                load: paginationControlHelper.onLoad,\n                unload: paginationControlHelper.onUnload,\n                loadView: view_onLoadView,\n                showView: view_onShow,\n                setLightBoxFadeIn: view_onSetLightBoxFadeIn,\n                animationStateChange: paginationControl_onAnimationStateChange } }"},[[19,109]],["DIV",{"data-bind":"css: { 'zero-opacity': hidePaginatedView() }","class":""},[[19,110]],[[19,111]],["DIV",{"class":"pagination-view animate slide-in-next","data-bind":"css: {\n        'has-identity-banner': showIdentityBanner() && (sharedData.displayName || svr.sPOST_Username),\n        'zero-opacity': hidePaginatedView.hideSubView(),\n        'animate': animate(),\n        'slide-out-next': animate.isSlideOutNext(),\n        'slide-in-next': animate.isSlideInNext(),\n        'slide-out-back': animate.isSlideOutBack(),\n        'slide-in-back': animate.isSlideInBack() }"},[[19,112]],[[19,113]],[[19,114]],["DIV",{"data-viewid":"1","data-showfedcredbutton":"true","data-bind":"pageViewComponent: { name: 'login-paginated-username-view',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    isInitialView: isInitialState,\n                    displayName: sharedData.displayName,\n                    otherIdpRedirectUrl: sharedData.otherIdpRedirectUrl,\n                    prefillNames: $loginPage.prefillNames,\n                    flowToken: sharedData.flowToken,\n                    availableSignupCreds: sharedData.availableSignupCreds,\n                    customStrings: $loginPage.stringCustomizationObservables.customStrings(),\n                    isCustomizationFailure: $loginPage.stringCustomizationObservables.isCustomStringsLoadFailure(),\n                    userIdLabel: $loginPage.userIdLabel,\n                    cantAccessYourAccountText: $loginPage.cantAccessYourAccountText,\n                    hideAccountResetCredentials: $loginPage.hideAccountResetCredentials,\n                    accessRecoveryLink: $loginPage.accessRecoveryLink,\n                    boilerPlateText: $loginPage.boilerPlateText },\n                event: {\n                    restoreIsRecoveryAttemptPost: $loginPage.view_onRestoreIsRecoveryAttemptPost,\n                    redirect: $loginPage.view_onRedirect,\n                    updateDFPUrl: $loginPage.view_onUpdateDFPUrl,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest,\n                    registerDialog: $loginPage.view_onRegisterDialog,\n                    unregisterDialog: $loginPage.view_onUnregisterDialog,\n                    showDialog: $loginPage.view_onShowDialog,\n                    updateAvailableCredsWithoutUsername: $loginPage.view_onUpdateAvailableCreds,\n                    agreementClick: $loginPage.footer_agreementClick } }"},[[19,115]],[[19,126]],[[19,127]],[[19,128]],[[6,3]],[[19,154]],[[19,180]],[[19,181]],[[19,182]],["DIV",{"class":"win-button-pin-bottom boilerplate-button-bottom","data-bind":"css : { 'boilerplate-button-bottom': tenantBranding.BoilerPlateText || boilerPlateText }"},[[19,183]],["DIV",{"class":"row move-buttons","data-bind":"css: { 'move-buttons': tenantBranding.BoilerPlateText || boilerPlateText }"},[[19,184]],["DIV",{"data-bind":"component: { name: 'footer-buttons-field',\n            params: {\n                serverData: svr,\n                isPrimaryButtonEnabled: !isRequestPending(),\n                isPrimaryButtonVisible: svr.fShowButtons,\n                isSecondaryButtonEnabled: true,\n                isSecondaryButtonVisible: svr.fShowButtons && isSecondaryButtonVisible(),\n                secondaryButtonText: secondaryButtonText() },\n            event: {\n                primaryButtonClick: primaryButton_onClick,\n                secondaryButtonClick: secondaryButton_onClick } }"},["DIV",{"class":"col-xs-24 no-padding-left-right button-container button-field-container ext-button-field-container","data-bind":"\n    visible: isPrimaryButtonVisible() || isSecondaryButtonVisible(),\n    css: { 'no-margin-bottom': removeBottomMargin },\n    externalCss: { 'button-field-container': true }"},[[19,185]],[[19,186]],["DIV",{"data-bind":"css: { 'inline-block': isPrimaryButtonVisible }, externalCss: { 'button-item': true }","class":"inline-block button-item ext-button-item"},[[19,187]],[[19,188]],[[19,189]],["INPUT",{"__playwright_value_":"Next","__playwright_target__":"call@43","type":"submit","id":"idSIButton9","class":"win-button button_primary high-contrast-overrides button ext-button primary ext-primary","data-report-event":"Signin_Submit","data-report-trigger":"click","data-report-value":"Submit","data-bind":"\n                attr: primaryButtonAttributes,\n                css: { 'high-contrast-overrides': svr.fUseHighContrastDetectionMode },\n                externalCss: {\n                    'button': true,\n                    'primary': true },\n                value: primaryButtonText() || str['CT_PWD_STR_SignIn_Button_Next'],\n                hasFocus: focusOnPrimaryButton,\n                click: svr.fEnableLivePreview ?  function() { } : primaryButton_onClick,\n                clickBubble: !svr.fEnableLivePreview,\n                enable: isPrimaryButtonEnabled,\n                visible: isPrimaryButtonVisible,\n                preventTabbing: primaryButtonPreventTabbing","value":"Next","data-report-attached":"1","style":""}],[[19,191]],[[19,192]],[[19,193]]],[[19,195]]]],[[19,198]]],[[19,200]]],[[19,202]],[[19,203]],[[19,213]],[[19,214]]],[[19,216]],[[19,217]],[[19,218]],[[19,219]],[[19,220]],[[19,221]],[[19,222]],[[19,223]],[[19,224]],[[19,225]],[[19,226]],[[19,227]],[[19,228]],[[19,229]],[[19,230]],[[19,231]],[[19,232]],[[19,233]],[[19,234]],[[19,235]],[[19,236]],[[19,237]],[[19,238]],[[19,239]],[[19,240]],[[19,241]],[[19,242]],[[19,243]],[[19,244]],[[19,245]],[[19,246]],[[19,247]],[[19,248]],[[19,249]],[[19,250]],[[19,251]],[[19,252]],[[19,253]],[[19,254]],[[19,255]],[[19,256]],[[19,257]],[[19,258]],[[19,259]],[[19,260]],[[19,261]],[[19,262]],[[19,263]],[[19,264]],[[19,265]],[[19,266]],[[19,267]],[[19,268]],[[19,269]],[[19,270]],[[19,271]],[[19,272]],[[19,273]],[[19,274]],[[19,275]],[[19,276]],[[19,277]],[[19,278]],[[19,279]],[[19,280]],[[19,281]],[[19,282]],[[19,283]],[[19,284]],[[19,285]],[[19,286]],[[19,287]],[[19,288]],[[19,289]],[[19,290]],[[19,291]],[[19,292]],[[19,293]],[[19,294]],[[19,295]],[[19,296]],[[19,297]],[[19,298]],[[19,299]],[[19,300]],[[19,301]],[[19,302]],[[19,303]],[[19,304]]],[[19,306]]]],[[19,309]],[[19,310]],[[17,15]],[[19,312]],[[17,16]],[[19,314]],[[17,17]],[[19,316]],[[17,18]],[[19,318]],[[19,319]],[[17,19]],[[19,321]],[[19,322]],[[19,323]],[[17,20]],[[19,325]],[[17,21]],[[19,327]],[[17,22]],[[19,329]],[[17,23]],[[19,331]],[[17,24]],[[19,333]],[[17,25]],[[19,335]],[[17,26]],[[19,337]],[[17,27]],[[19,339]],[[17,28]],[[19,341]],[[17,29]],[[19,343]],[[17,30]],[[19,345]],[[19,346]],[[19,347]],[[17,31]],[[19,349]],[[19,350]],[[19,351]],[[17,33]],[[19,355]],[[19,356]]],[[19,358]],[[19,359]],[[19,360]],[[19,361]],[[19,362]],[[19,410]],[[19,411]],[[19,412]],[[19,413]],[[19,414]],[[19,415]],[[19,416]],[[19,417]],[[19,418]]],[[19,420]]],[[19,422]]],[[19,424]]],[[19,426]]],[[19,428]],[[19,429]],[[19,457]],[[19,458]],[[19,459]]],[[19,461]]],[[19,463]],[[19,464]],[[19,465]],[[19,466]]],[[19,468]],[[19,469]]],[[19,471]],[[19,472]],[[19,473]],[[19,474]]]]],"viewport":{"width":1280,"height":720},"timestamp":9977.484,"wallTime":1749104560204,"collectionTime":1,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@43","time":9981.854,"message":"  performing click action"}
{"type":"log","callId":"call@43","time":9996.52,"message":"  click action done"}
{"type":"log","callId":"call@43","time":9996.545,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@43","time":9997.465,"message":"  navigations have finished"}
{"type":"after","callId":"call@43","endTime":9997.709,"point":{"x":762,"y":303.71},"afterSnapshot":"after@call@43"}
{"type":"frame-snapshot","snapshot":{"callId":"call@43","snapshotName":"after@call@43","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":["HTML",{"dir":"ltr","class":"","lang":"en"},[[20,47]],[[20,48]],["BODY",{"data-bind":"defineGlobals: ServerData, bodyCssClass","class":"cb","style":"display: block;"},[[20,49]],[[20,50]],["DIV",{},[[20,51]],[[20,52]],[[20,53]],[[20,54]],["FORM",{"name":"f1","id":"i0281","novalidate":"novalidate","spellcheck":"false","method":"post","target":"_top","autocomplete":"off","data-bind":"visible: !isLoginPageHidden(), autoSubmit: forceSubmit, attr: { action: postUrl }, ariaHidden: !!activeDialog(), css: { 'provide-min-height': svr.fUseMinHeight }","action":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/login","class":"provide-min-height"},[[20,55]],[[20,56]],["DIV",{"class":"login-paginated-page","data-bind":"component: { name: 'master-page',\n        publicMethods: masterPageMethods,\n        params: {\n            serverData: svr,\n            showButtons: svr.fShowButtons,\n            showFooterLinks: true,\n            useWizardBehavior: svr.fUseWizardBehavior,\n            handleWizardButtons: false,\n            password: password,\n            hideFromAria: ariaHidden },\n        event: {\n            footerAgreementClick: footer_agreementClick } }"},[[20,57]],[[20,58]],[[20,59]],[[20,60]],[[20,61]],["DIV",{"id":"lightboxTemplateContainer","data-bind":"component: { name: 'lightbox-template', params: { serverData: svr, showHeader: $page.showHeader(), headerLogo: $page.headerLogo() } }, css: { 'provide-min-height': svr.fUseMinHeight }","class":"provide-min-height"},[[20,62]],[[20,74]],[[20,75]],[[20,76]],[[20,77]],["DIV",{"class":"outer","data-bind":"css: { 'app': $page.backgroundLogoUrl }"},[[20,78]],[[20,79]],["DIV",{"class":"template-section main-section"},[[20,80]],["DIV",{"data-bind":"externalCss: { 'middle': true }","class":"middle ext-middle"},[[20,81]],["DIV",{"class":"full-height","data-bind":"component: { name: 'content-control', params: { serverData: svr, isVerticalSplitTemplate: $page.isVerticalSplitTemplate(), hasHeader: showHeader } }"},[[20,82]],[[20,83]],["DIV",{"class":"flex-column"},[[20,84]],[[20,85]],[[20,86]],["DIV",{"class":"win-scroll"},[[20,87]],["DIV",{"id":"lightbox","data-bind":"\n            animationEnd: $page.paginationControlHelper.animationEnd,\n            externalCss: { 'sign-in-box': true },\n            css: {\n                'inner':  $content.isVerticalSplitTemplate,\n                'vertical-split-content': $content.isVerticalSplitTemplate,\n                'app': $page.backgroundLogoUrl,\n                'wide': $page.paginationControlHelper.useWiderWidth,\n                'fade-in-lightbox': $page.fadeInLightBox,\n                'has-popup': $page.showFedCredAndNewSession && ($page.showFedCredButtons() || $page.newSession()),\n                'transparent-lightbox': $page.backgroundControlMethods() && $page.backgroundControlMethods().useTransparentLightBox,\n                'lightbox-bottom-margin-debug': $page.showDebugDetails,\n                'has-header': $content.hasHeader }","class":"sign-in-box ext-sign-in-box fade-in-lightbox has-popup"},[[20,88]],[[20,89]],[[20,90]],["DIV",{"class":"lightbox-cover disable-lightbox","data-bind":"css: { 'disable-lightbox': svr.fAllowGrayOutLightBox && showLightboxProgress() }"}],[[20,92]],"\n        ",["DIV",{"id":"progressBar","class":"progress","role":"progressbar","data-bind":"component: 'marching-ants-control', ariaLabel: str['WF_STR_ProgressText']","aria-label":"Please wait"},"\n\n","\n","\n    ","\n    ",["DIV"],["DIV"],["DIV"],["DIV"],["DIV"],"\n    ","\n    ","\n"],"\n        ",[[20,93]],[[20,94]],[[20,104]],[[20,105]],[[20,106]],[[20,107]],[[20,108]],["DIV",{"role":"main","data-bind":"component: { name: 'pagination-control',\n            publicMethods: paginationControlMethods,\n            params: {\n                enableCssAnimation: svr.fEnableCssAnimation,\n                disableAnimationIfAnimationEndUnsupported: svr.fDisableAnimationIfAnimationEndUnsupported,\n                initialViewId: initialViewId,\n                currentViewId: currentViewId,\n                initialSharedData: initialSharedData,\n                initialError: $loginPage.getServerError() },\n            event: {\n                cancel: paginationControl_onCancel,\n                load: paginationControlHelper.onLoad,\n                unload: paginationControlHelper.onUnload,\n                loadView: view_onLoadView,\n                showView: view_onShow,\n                setLightBoxFadeIn: view_onSetLightBoxFadeIn,\n                animationStateChange: paginationControl_onAnimationStateChange } }"},[[20,109]],["DIV",{"data-bind":"css: { 'zero-opacity': hidePaginatedView() }","class":""},[[20,110]],[[20,111]],["DIV",{"class":"pagination-view animate slide-in-next","data-bind":"css: {\n        'has-identity-banner': showIdentityBanner() && (sharedData.displayName || svr.sPOST_Username),\n        'zero-opacity': hidePaginatedView.hideSubView(),\n        'animate': animate(),\n        'slide-out-next': animate.isSlideOutNext(),\n        'slide-in-next': animate.isSlideInNext(),\n        'slide-out-back': animate.isSlideOutBack(),\n        'slide-in-back': animate.isSlideInBack() }"},[[20,112]],[[20,113]],[[20,114]],["DIV",{"data-viewid":"1","data-showfedcredbutton":"true","data-bind":"pageViewComponent: { name: 'login-paginated-username-view',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    isInitialView: isInitialState,\n                    displayName: sharedData.displayName,\n                    otherIdpRedirectUrl: sharedData.otherIdpRedirectUrl,\n                    prefillNames: $loginPage.prefillNames,\n                    flowToken: sharedData.flowToken,\n                    availableSignupCreds: sharedData.availableSignupCreds,\n                    customStrings: $loginPage.stringCustomizationObservables.customStrings(),\n                    isCustomizationFailure: $loginPage.stringCustomizationObservables.isCustomStringsLoadFailure(),\n                    userIdLabel: $loginPage.userIdLabel,\n                    cantAccessYourAccountText: $loginPage.cantAccessYourAccountText,\n                    hideAccountResetCredentials: $loginPage.hideAccountResetCredentials,\n                    accessRecoveryLink: $loginPage.accessRecoveryLink,\n                    boilerPlateText: $loginPage.boilerPlateText },\n                event: {\n                    restoreIsRecoveryAttemptPost: $loginPage.view_onRestoreIsRecoveryAttemptPost,\n                    redirect: $loginPage.view_onRedirect,\n                    updateDFPUrl: $loginPage.view_onUpdateDFPUrl,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest,\n                    registerDialog: $loginPage.view_onRegisterDialog,\n                    unregisterDialog: $loginPage.view_onUnregisterDialog,\n                    showDialog: $loginPage.view_onShowDialog,\n                    updateAvailableCredsWithoutUsername: $loginPage.view_onUpdateAvailableCreds,\n                    agreementClick: $loginPage.footer_agreementClick } }"},[[20,115]],[[20,126]],[[20,127]],[[20,128]],[[7,3]],[[20,154]],[[20,180]],[[20,181]],[[20,182]],["DIV",{"class":"win-button-pin-bottom boilerplate-button-bottom","data-bind":"css : { 'boilerplate-button-bottom': tenantBranding.BoilerPlateText || boilerPlateText }"},[[20,183]],["DIV",{"class":"row move-buttons","data-bind":"css: { 'move-buttons': tenantBranding.BoilerPlateText || boilerPlateText }"},[[20,184]],["DIV",{"data-bind":"component: { name: 'footer-buttons-field',\n            params: {\n                serverData: svr,\n                isPrimaryButtonEnabled: !isRequestPending(),\n                isPrimaryButtonVisible: svr.fShowButtons,\n                isSecondaryButtonEnabled: true,\n                isSecondaryButtonVisible: svr.fShowButtons && isSecondaryButtonVisible(),\n                secondaryButtonText: secondaryButtonText() },\n            event: {\n                primaryButtonClick: primaryButton_onClick,\n                secondaryButtonClick: secondaryButton_onClick } }"},["DIV",{"class":"col-xs-24 no-padding-left-right button-container button-field-container ext-button-field-container","data-bind":"\n    visible: isPrimaryButtonVisible() || isSecondaryButtonVisible(),\n    css: { 'no-margin-bottom': removeBottomMargin },\n    externalCss: { 'button-field-container': true }"},"\n\n    ","\n\n    ",["DIV",{"data-bind":"css: { 'inline-block': isPrimaryButtonVisible }, externalCss: { 'button-item': true }","class":"inline-block button-item ext-button-item"},"\n        ","\n        ","\n        ",["INPUT",{"__playwright_value_":"Next","type":"submit","id":"idSIButton9","class":"win-button button_primary high-contrast-overrides button ext-button primary ext-primary","data-report-event":"Signin_Submit","data-report-trigger":"click","data-report-value":"Submit","data-bind":"\n                attr: primaryButtonAttributes,\n                css: { 'high-contrast-overrides': svr.fUseHighContrastDetectionMode },\n                externalCss: {\n                    'button': true,\n                    'primary': true },\n                value: primaryButtonText() || str['CT_PWD_STR_SignIn_Button_Next'],\n                hasFocus: focusOnPrimaryButton,\n                click: svr.fEnableLivePreview ?  function() { } : primaryButton_onClick,\n                clickBubble: !svr.fEnableLivePreview,\n                enable: isPrimaryButtonEnabled,\n                visible: isPrimaryButtonVisible,\n                preventTabbing: primaryButtonPreventTabbing","value":"Next","disabled":""}],"\n        ","\n        ","\n    "],"\n"]],[[20,198]]],[[20,200]]],[[20,202]],[[20,203]],[[20,213]],[[20,214]]],[[20,216]],[[20,217]],[[20,218]],[[20,219]],[[20,220]],[[20,221]],[[20,222]],[[20,223]],[[20,224]],[[20,225]],[[20,226]],[[20,227]],[[20,228]],[[20,229]],[[20,230]],[[20,231]],[[20,232]],[[20,233]],[[20,234]],[[20,235]],[[20,236]],[[20,237]],[[20,238]],[[20,239]],[[20,240]],[[20,241]],[[20,242]],[[20,243]],[[20,244]],[[20,245]],[[20,246]],[[20,247]],[[20,248]],[[20,249]],[[20,250]],[[20,251]],[[20,252]],[[20,253]],[[20,254]],[[20,255]],[[20,256]],[[20,257]],[[20,258]],[[20,259]],[[20,260]],[[20,261]],[[20,262]],[[20,263]],[[20,264]],[[20,265]],[[20,266]],[[20,267]],[[20,268]],[[20,269]],[[20,270]],[[20,271]],[[20,272]],[[20,273]],[[20,274]],[[20,275]],[[20,276]],[[20,277]],[[20,278]],[[20,279]],[[20,280]],[[20,281]],[[20,282]],[[20,283]],[[20,284]],[[20,285]],[[20,286]],[[20,287]],[[20,288]],[[20,289]],[[20,290]],[[20,291]],[[20,292]],[[20,293]],[[20,294]],[[20,295]],[[20,296]],[[20,297]],[[20,298]],[[20,299]],[[20,300]],[[20,301]],[[20,302]],[[20,303]],[[20,304]]],[[20,306]]]],[[20,309]],[[20,310]],[[18,15]],[[20,312]],[[18,16]],[[20,314]],[[18,17]],[[20,316]],[[18,18]],[[20,318]],[[20,319]],[[18,19]],[[20,321]],[[20,322]],[[20,323]],[[18,20]],[[20,325]],[[18,21]],[[20,327]],[[18,22]],[[20,329]],[[18,23]],[[20,331]],[[18,24]],[[20,333]],[[18,25]],[[20,335]],[[18,26]],[[20,337]],[[18,27]],[[20,339]],[[18,28]],[[20,341]],[[18,29]],[[20,343]],[[18,30]],[[20,345]],[[20,346]],[[20,347]],[[18,31]],[[20,349]],[[20,350]],[[20,351]],[[18,33]],[[20,355]],[[20,356]]],[[20,358]],[[20,359]],[[20,360]],[[20,361]],[[20,362]],[[20,410]],[[20,411]],[[20,412]],[[20,413]],[[20,414]],[[20,415]],[[20,416]],[[20,417]],[[20,418]]],[[20,420]]],[[20,422]]],[[20,424]]],[[20,426]]],[[20,428]],[[20,429]],[[20,457]],[[20,458]],[[20,459]]],[[20,461]]],[[20,463]],[[20,464]],[[20,465]],[[20,466]]],[[20,468]],[[20,469]]],[[20,471]],[[20,472]],[[20,473]],[[20,474]]]]],"viewport":{"width":1280,"height":720},"timestamp":10003.258,"wallTime":1749104560229,"collectionTime":2.100000001490116,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@45","startTime":10006.294,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{"type":"png","fullPage":true},"stepId":"pw:api@27","pageId":"page@c6886d2b5677aac36ea20fab1f024056","beforeSnapshot":"before@call@45"}
{"type":"frame-snapshot","snapshot":{"callId":"call@45","snapshotName":"before@call@45","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":[[1,47]],"viewport":{"width":1280,"height":720},"timestamp":10008.581,"wallTime":1749104560236,"collectionTime":0.8000000007450581,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@45","time":10009.198,"message":"taking page screenshot"}
{"type":"log","callId":"call@45","time":10010.877,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@45","time":10012.604,"message":"fonts loaded"}
{"type":"after","callId":"call@45","endTime":10097.399,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@45"}
{"type":"frame-snapshot","snapshot":{"callId":"call@45","snapshotName":"after@call@45","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":["HTML",{"dir":"ltr","class":"","lang":"en"},[[22,47]],[[22,48]],["BODY",{"data-bind":"defineGlobals: ServerData, bodyCssClass","class":"cb","style":"display: block;"},[[22,49]],[[22,50]],["DIV",{},[[22,51]],[[22,52]],[[22,53]],[[22,54]],["FORM",{"name":"f1","id":"i0281","novalidate":"novalidate","spellcheck":"false","method":"post","target":"_top","autocomplete":"off","data-bind":"visible: !isLoginPageHidden(), autoSubmit: forceSubmit, attr: { action: postUrl }, ariaHidden: !!activeDialog(), css: { 'provide-min-height': svr.fUseMinHeight }","action":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/login","class":"provide-min-height"},[[22,55]],[[22,56]],["DIV",{"class":"login-paginated-page","data-bind":"component: { name: 'master-page',\n        publicMethods: masterPageMethods,\n        params: {\n            serverData: svr,\n            showButtons: svr.fShowButtons,\n            showFooterLinks: true,\n            useWizardBehavior: svr.fUseWizardBehavior,\n            handleWizardButtons: false,\n            password: password,\n            hideFromAria: ariaHidden },\n        event: {\n            footerAgreementClick: footer_agreementClick } }"},[[22,57]],[[22,58]],[[22,59]],[[22,60]],[[22,61]],["DIV",{"id":"lightboxTemplateContainer","data-bind":"component: { name: 'lightbox-template', params: { serverData: svr, showHeader: $page.showHeader(), headerLogo: $page.headerLogo() } }, css: { 'provide-min-height': svr.fUseMinHeight }","class":"provide-min-height"},[[22,62]],[[22,74]],[[22,75]],[[22,76]],[[22,77]],["DIV",{"class":"outer","data-bind":"css: { 'app': $page.backgroundLogoUrl }"},[[22,78]],[[22,79]],["DIV",{"class":"template-section main-section"},[[22,80]],["DIV",{"data-bind":"externalCss: { 'middle': true }","class":"middle ext-middle"},[[22,81]],["DIV",{"class":"full-height","data-bind":"component: { name: 'content-control', params: { serverData: svr, isVerticalSplitTemplate: $page.isVerticalSplitTemplate(), hasHeader: showHeader } }"},[[22,82]],[[22,83]],["DIV",{"class":"flex-column"},[[22,84]],[[22,85]],[[22,86]],["DIV",{"class":"win-scroll"},[[22,87]],["DIV",{"id":"lightbox","data-bind":"\n            animationEnd: $page.paginationControlHelper.animationEnd,\n            externalCss: { 'sign-in-box': true },\n            css: {\n                'inner':  $content.isVerticalSplitTemplate,\n                'vertical-split-content': $content.isVerticalSplitTemplate,\n                'app': $page.backgroundLogoUrl,\n                'wide': $page.paginationControlHelper.useWiderWidth,\n                'fade-in-lightbox': $page.fadeInLightBox,\n                'has-popup': $page.showFedCredAndNewSession && ($page.showFedCredButtons() || $page.newSession()),\n                'transparent-lightbox': $page.backgroundControlMethods() && $page.backgroundControlMethods().useTransparentLightBox,\n                'lightbox-bottom-margin-debug': $page.showDebugDetails,\n                'has-header': $content.hasHeader }","class":"sign-in-box ext-sign-in-box fade-in-lightbox has-popup"},[[22,88]],[[22,89]],[[22,90]],[[2,0]],[[22,92]],[[2,1]],[[2,14]],[[2,15]],[[22,93]],[[22,94]],[[22,104]],[[22,105]],[[22,106]],[[22,107]],[[22,108]],["DIV",{"role":"main","data-bind":"component: { name: 'pagination-control',\n            publicMethods: paginationControlMethods,\n            params: {\n                enableCssAnimation: svr.fEnableCssAnimation,\n                disableAnimationIfAnimationEndUnsupported: svr.fDisableAnimationIfAnimationEndUnsupported,\n                initialViewId: initialViewId,\n                currentViewId: currentViewId,\n                initialSharedData: initialSharedData,\n                initialError: $loginPage.getServerError() },\n            event: {\n                cancel: paginationControl_onCancel,\n                load: paginationControlHelper.onLoad,\n                unload: paginationControlHelper.onUnload,\n                loadView: view_onLoadView,\n                showView: view_onShow,\n                setLightBoxFadeIn: view_onSetLightBoxFadeIn,\n                animationStateChange: paginationControl_onAnimationStateChange } }"},[[22,109]],["DIV",{"data-bind":"css: { 'zero-opacity': hidePaginatedView() }","class":""},[[22,110]],[[22,111]],["DIV",{"class":"pagination-view animate slide-in-next","data-bind":"css: {\n        'has-identity-banner': showIdentityBanner() && (sharedData.displayName || svr.sPOST_Username),\n        'zero-opacity': hidePaginatedView.hideSubView(),\n        'animate': animate(),\n        'slide-out-next': animate.isSlideOutNext(),\n        'slide-in-next': animate.isSlideInNext(),\n        'slide-out-back': animate.isSlideOutBack(),\n        'slide-in-back': animate.isSlideInBack() }"},[[22,112]],[[22,113]],[[22,114]],["DIV",{"data-viewid":"1","data-showfedcredbutton":"true","data-bind":"pageViewComponent: { name: 'login-paginated-username-view',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    isInitialView: isInitialState,\n                    displayName: sharedData.displayName,\n                    otherIdpRedirectUrl: sharedData.otherIdpRedirectUrl,\n                    prefillNames: $loginPage.prefillNames,\n                    flowToken: sharedData.flowToken,\n                    availableSignupCreds: sharedData.availableSignupCreds,\n                    customStrings: $loginPage.stringCustomizationObservables.customStrings(),\n                    isCustomizationFailure: $loginPage.stringCustomizationObservables.isCustomStringsLoadFailure(),\n                    userIdLabel: $loginPage.userIdLabel,\n                    cantAccessYourAccountText: $loginPage.cantAccessYourAccountText,\n                    hideAccountResetCredentials: $loginPage.hideAccountResetCredentials,\n                    accessRecoveryLink: $loginPage.accessRecoveryLink,\n                    boilerPlateText: $loginPage.boilerPlateText },\n                event: {\n                    restoreIsRecoveryAttemptPost: $loginPage.view_onRestoreIsRecoveryAttemptPost,\n                    redirect: $loginPage.view_onRedirect,\n                    updateDFPUrl: $loginPage.view_onUpdateDFPUrl,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest,\n                    registerDialog: $loginPage.view_onRegisterDialog,\n                    unregisterDialog: $loginPage.view_onUnregisterDialog,\n                    showDialog: $loginPage.view_onShowDialog,\n                    updateAvailableCredsWithoutUsername: $loginPage.view_onUpdateAvailableCreds,\n                    agreementClick: $loginPage.footer_agreementClick } }"},[[22,115]],[[22,126]],[[22,127]],[[22,128]],[[9,3]],[[22,154]],[[22,180]],[[22,181]],[[22,182]],["DIV",{"class":"win-button-pin-bottom boilerplate-button-bottom","data-bind":"css : { 'boilerplate-button-bottom': tenantBranding.BoilerPlateText || boilerPlateText }"},[[22,183]],["DIV",{"class":"row move-buttons","data-bind":"css: { 'move-buttons': tenantBranding.BoilerPlateText || boilerPlateText }"},[[22,184]],["DIV",{"data-bind":"component: { name: 'footer-buttons-field',\n            params: {\n                serverData: svr,\n                isPrimaryButtonEnabled: !isRequestPending(),\n                isPrimaryButtonVisible: svr.fShowButtons,\n                isSecondaryButtonEnabled: true,\n                isSecondaryButtonVisible: svr.fShowButtons && isSecondaryButtonVisible(),\n                secondaryButtonText: secondaryButtonText() },\n            event: {\n                primaryButtonClick: primaryButton_onClick,\n                secondaryButtonClick: secondaryButton_onClick } }"},["DIV",{"class":"col-xs-24 no-padding-left-right button-container button-field-container ext-button-field-container","data-bind":"\n    visible: isPrimaryButtonVisible() || isSecondaryButtonVisible(),\n    css: { 'no-margin-bottom': removeBottomMargin },\n    externalCss: { 'button-field-container': true }"},[[2,16]],[[2,17]],["DIV",{"data-bind":"css: { 'inline-block': isPrimaryButtonVisible }, externalCss: { 'button-item': true }","class":"inline-block button-item ext-button-item"},[[2,18]],[[2,19]],[[2,20]],["INPUT",{"__playwright_value_":"Next","type":"submit","id":"idSIButton9","class":"win-button button_primary high-contrast-overrides button ext-button primary ext-primary","data-report-event":"Signin_Submit","data-report-trigger":"click","data-report-value":"Submit","data-bind":"\n                attr: primaryButtonAttributes,\n                css: { 'high-contrast-overrides': svr.fUseHighContrastDetectionMode },\n                externalCss: {\n                    'button': true,\n                    'primary': true },\n                value: primaryButtonText() || str['CT_PWD_STR_SignIn_Button_Next'],\n                hasFocus: focusOnPrimaryButton,\n                click: svr.fEnableLivePreview ?  function() { } : primaryButton_onClick,\n                clickBubble: !svr.fEnableLivePreview,\n                enable: isPrimaryButtonEnabled,\n                visible: isPrimaryButtonVisible,\n                preventTabbing: primaryButtonPreventTabbing","value":"Next","disabled":"","style":""}],[[2,22]],[[2,23]],[[2,24]]],[[2,26]]]],[[22,198]]],[[22,200]]],[[22,202]],[[22,203]],[[22,213]],[[22,214]]],[[22,216]],[[22,217]],[[22,218]],[[22,219]],[[22,220]],[[22,221]],[[22,222]],[[22,223]],[[22,224]],[[22,225]],[[22,226]],[[22,227]],[[22,228]],[[22,229]],[[22,230]],[[22,231]],[[22,232]],[[22,233]],[[22,234]],[[22,235]],[[22,236]],[[22,237]],[[22,238]],[[22,239]],[[22,240]],[[22,241]],[[22,242]],[[22,243]],[[22,244]],[[22,245]],[[22,246]],[[22,247]],[[22,248]],[[22,249]],[[22,250]],[[22,251]],[[22,252]],[[22,253]],[[22,254]],[[22,255]],[[22,256]],[[22,257]],[[22,258]],[[22,259]],[[22,260]],[[22,261]],[[22,262]],[[22,263]],[[22,264]],[[22,265]],[[22,266]],[[22,267]],[[22,268]],[[22,269]],[[22,270]],[[22,271]],[[22,272]],[[22,273]],[[22,274]],[[22,275]],[[22,276]],[[22,277]],[[22,278]],[[22,279]],[[22,280]],[[22,281]],[[22,282]],[[22,283]],[[22,284]],[[22,285]],[[22,286]],[[22,287]],[[22,288]],[[22,289]],[[22,290]],[[22,291]],[[22,292]],[[22,293]],[[22,294]],[[22,295]],[[22,296]],[[22,297]],[[22,298]],[[22,299]],[[22,300]],[[22,301]],[[22,302]],[[22,303]],[[22,304]]],[[22,306]]]],[[22,309]],[[22,310]],[[20,15]],[[22,312]],[[20,16]],[[22,314]],[[20,17]],[[22,316]],[[20,18]],[[22,318]],[[22,319]],[[20,19]],[[22,321]],[[22,322]],[[22,323]],[[20,20]],[[22,325]],[[20,21]],[[22,327]],[[20,22]],[[22,329]],[[20,23]],[[22,331]],[[20,24]],[[22,333]],[[20,25]],[[22,335]],[[20,26]],[[22,337]],[[20,27]],[[22,339]],[[20,28]],[[22,341]],[[20,29]],[[22,343]],[[20,30]],[[22,345]],[[22,346]],[[22,347]],[[20,31]],[[22,349]],[[22,350]],[[22,351]],[[20,33]],[[22,355]],[[22,356]]],[[22,358]],[[22,359]],[[22,360]],[[22,361]],[[22,362]],[[22,410]],[[22,411]],[[22,412]],[[22,413]],[[22,414]],[[22,415]],[[22,416]],[[22,417]],[[22,418]]],[[22,420]]],[[22,422]]],[[22,424]]],[[22,426]]],[[22,428]],[[22,429]],[[22,457]],[[22,458]],[[22,459]]],[[22,461]]],[[22,463]],[[22,464]],[[22,465]],[[22,466]]],[[22,468]],[[22,469]]],[[22,471]],[[22,472]],[[22,473]],[[22,474]]]]],"viewport":{"width":1280,"height":720},"timestamp":10102.899,"wallTime":1749104560329,"collectionTime":2.400000000372529,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@47","startTime":10112.353,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{"type":"png","fullPage":true},"stepId":"pw:api@29","pageId":"page@c6886d2b5677aac36ea20fab1f024056","beforeSnapshot":"before@call@47"}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":10113.727,"frameSwapWallTime":1749104560331.734}
{"type":"frame-snapshot","snapshot":{"callId":"call@47","snapshotName":"before@call@47","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":[[1,22]],"viewport":{"width":1280,"height":720},"timestamp":10118.94,"wallTime":1749104560345,"collectionTime":1.5,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@47","time":10120.029,"message":"taking page screenshot"}
{"type":"log","callId":"call@47","time":10123.492,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@47","time":10126.401,"message":"fonts loaded"}
{"type":"after","callId":"call@47","endTime":10223.084,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@47"}
{"type":"frame-snapshot","snapshot":{"callId":"call@47","snapshotName":"after@call@47","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":[[2,22]],"viewport":{"width":1280,"height":720},"timestamp":10226.966,"wallTime":1749104560453,"collectionTime":1.799999998882413,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@49","startTime":10236.076,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"internal:role=link[name=\"OI Development\"s]","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@31","pageId":"page@c6886d2b5677aac36ea20fab1f024056","beforeSnapshot":"before@call@49"}
{"type":"frame-snapshot","snapshot":{"callId":"call@49","snapshotName":"before@call@49","pageId":"page@c6886d2b5677aac36ea20fab1f024056","frameId":"frame@35422057ea692e6f658028abf980fb0f","frameUrl":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/authorize?client%5Fid=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&response%5Fmode=form%5Fpost&response%5Ftype=code%20id%5Ftoken&resource=00000003%2D0000%2D0ff1%2Dce00%2D000000000000&scope=openid&nonce=54DC7BB6C97F7F4656872B5C3925BA180740A2838FCBBF1E%2DE9E8FF3ED473F6060DF2C850FF3B10889FCC9CAB94585DFD47D9CCAF398C35C6&redirect%5Furi=https%3A%2F%2Fspo%2Dglobal%2Ekpmg%2Ecom%2F%5Fforms%2Fdefault%2Easpx&state=OD0w&claims=%7B%22id%5Ftoken%22%3A%7B%22xms%5Fcc%22%3A%7B%22values%22%3A%5B%22CP1%22%5D%7D%7D%7D&wsucxt=1&cobrandid=11bd8083%2D87e0%2D41b5%2Dbb78%2D0bc43c8a8e8a&client%2Drequest%2Did=e50ea5a1%2D5019%2Dc000%2Deed0%2D40ee9922fbfb&sso_reload=true","doctype":"html","html":[[3,22]],"viewport":{"width":1280,"height":720},"timestamp":10243.011,"wallTime":1749104560470,"collectionTime":1.5,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@49","time":10243.977,"message":"waiting for getByRole('link', { name: 'OI Development', exact: true }) to be visible"}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":10246.504,"frameSwapWallTime":1749104560464.69}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":10521.954,"frameSwapWallTime":1749104560731.364}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":10529.459,"frameSwapWallTime":1749104560744.094}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":10550.235,"frameSwapWallTime":1749104560764.0989}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":10562.529,"frameSwapWallTime":1749104560778.423}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":10586.131,"frameSwapWallTime":1749104560798.187}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":10611.153,"frameSwapWallTime":1749104560830.19}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":10664.883,"frameSwapWallTime":1749104560881.1409}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":10682.624,"frameSwapWallTime":1749104560898.61}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":10728.994,"frameSwapWallTime":1749104560947.341}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":10774.485,"frameSwapWallTime":1749104560991.15}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":10806.713,"frameSwapWallTime":1749104561024.7012}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":10854.842,"frameSwapWallTime":1749104561071.759}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":11088.351,"frameSwapWallTime":1749104561304.98}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":11263.759,"frameSwapWallTime":1749104561480.3481}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":11474.965,"frameSwapWallTime":1749104561688.222}
{"type":"screencast-frame","pageId":"page@c6886d2b5677aac36ea20fab1f024056","sha1":"<EMAIL>","width":1280,"height":713,"timestamp":11677.394,"frameSwapWallTime":1749104561897.238}
