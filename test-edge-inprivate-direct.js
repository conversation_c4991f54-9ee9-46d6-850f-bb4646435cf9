const { spawn } = require('child_process');
const { chromium } = require('playwright');

async function testEdgeInPrivateDirect() {
  console.log('Testing Edge InPrivate Direct Launch...');
  
  try {
    // Method 1: Try launching Edge directly with InPrivate
    console.log('Method 1: Direct Edge InPrivate launch...');
    
    // Launch Edge directly with InPrivate flag
    const edgeProcess = spawn('msedge', ['--inprivate', 'https://www.google.com'], {
      detached: true,
      stdio: 'ignore'
    });
    
    console.log('✅ Edge launched directly with --inprivate flag');
    console.log('Check if Edge opened in InPrivate mode manually');
    
    // Wait a bit then kill the process
    setTimeout(() => {
      try {
        process.kill(edgeProcess.pid);
        console.log('✅ Direct Edge process terminated');
      } catch (e) {
        console.log('Edge process already terminated');
      }
    }, 5000);
    
  } catch (error) {
    console.error('❌ Direct Edge launch failed:', error.message);
  }
  
  // Method 2: Try with Playwright but different approach
  try {
    console.log('\nMethod 2: Playwright with custom context...');
    
    const browser = await chromium.launch({
      channel: 'msedge',
      headless: false,
      args: [
        '--incognito',
        '--disable-extensions',
        '--disable-web-security',
        '--no-first-run',
        '--no-default-browser-check'
      ]
    });

    // Create a completely isolated context
    const context = await browser.newContext({
      storageState: undefined,
      acceptDownloads: false,
      ignoreHTTPSErrors: true,
      bypassCSP: true,
      // Clear all storage
      clearCookies: true,
      clearLocalStorage: true
    });

    const page = await context.newPage();
    
    // Clear any existing storage
    await page.evaluate(() => {
      // Clear all storage
      if (window.localStorage) {
        window.localStorage.clear();
      }
      if (window.sessionStorage) {
        window.sessionStorage.clear();
      }
      // Clear cookies
      document.cookie.split(";").forEach(function(c) { 
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
      });
    });
    
    await page.goto('https://www.google.com');
    console.log('✅ Navigated to Google with cleared storage');
    
    // Test if storage is actually cleared/restricted
    const storageTest = await page.evaluate(() => {
      try {
        localStorage.setItem('test', 'test');
        const value = localStorage.getItem('test');
        localStorage.removeItem('test');
        return value === 'test' ? 'Storage works (not private)' : 'Storage restricted (private)';
      } catch (e) {
        return 'Storage blocked (private mode)';
      }
    });
    
    console.log(`Storage test result: ${storageTest}`);
    
    await page.screenshot({ path: 'edge-method2-test.png' });
    console.log('✅ Screenshot taken: edge-method2-test.png');
    
    await browser.close();
    console.log('✅ Method 2 completed');
    
  } catch (error) {
    console.error('❌ Method 2 failed:', error.message);
  }
}

testEdgeInPrivateDirect();
