 <!DOCTYPE html>
<html lang="en-US">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=1"/>
        <meta http-equiv="content-type" content="text/html;charset=UTF-8" />
        <meta http-equiv="cache-control" content="no-cache,no-store"/>
        <meta http-equiv="pragma" content="no-cache"/>
        <meta http-equiv="expires" content="-1"/>
        <meta name='mswebdialog-title' content='Connecting to KPMG Login'/>

        <title>Browser SSO</title>
        <script type='text/javascript'>
//<![CDATA[
/* 
 * Copyright (c) Microsoft Corporation.  All rights reserved.
 *
 * Description: Functions to perform browser SSO (aka pull cookie) for Windows 10 and later.
 * 
 */

SSOUtil = {
    /*
     * Get a cookie value.
     *
     * Args:
     *   cookie_name: the name of the cookie.
     *
     * Returns:
     *   A string value from the cookie.
     */
    getCookie: function (cookie_name) {
        var i, x, y, cookie_list = document.cookie.split(";");
        for (i = 0; i < cookie_list.length; i++) {
            x = cookie_list[i].substr(0, cookie_list[i].indexOf("="));
            y = cookie_list[i].substr(cookie_list[i].indexOf("=") + 1);
            x = x.replace(/^\s+|\s+$/g, "");
            if (x == cookie_name) {
                return unescape(y);
            }
        }
    },

    /*
     * Erase a cookie.
     *
     * Args:
     *   cookie_name: the name of the cookie to set.
     */
    eraseCookie: function (cookie_name) {
        var host_parts = document.location.hostname.split('.');
        var part_count = host_parts.length;
        var cookie_str = cookie_name + "= ;domain=." +
            host_parts[part_count - 2] + '.' + host_parts[part_count - 1] +
            ";path=/;expires=Thu, 30-Oct-1980 00:00:01 GMT;";
        console.log(cookie_str);
        document.cookie = cookie_str;
        cookie_str = cookie_name + "= ;domain=." +
            document.location.hostname +
            ";path=/;expires=Thu, 30-Oct-1980 00:00:01 GMT;";
        console.log(cookie_str);
        document.cookie = cookie_str;

    },


    /*
     * Helper to extract a query parameter. Taken from Main_Browser.js
     *
     * param: Name of the parameter to extract.
     * returns: The query parameter value if it SSOUtil.exists otherwise it returns
     *     an empty string.
     */
    ExtractQSParam: function (param) {
        var sQS = document.location.search.toLowerCase();
        if (sQS)
            sQS = sQS.substr(1);

        return SSOUtil.ExtractToken(sQS, param.toLowerCase(), "&", "=", "");

    },

    /* 
     *  Extract a specific token from a string.    
     *
     *  Args:
     *      sString - the full string
     *      sName   - the name of the token
     *      sDelim  - the token delimiter (default '&')
     *      sSep    - the name=value pair separator (default '=')
     *
     *  Returns: string
     *      the value of the token if it SSOUtil.exists
     *      null otherwise
     */
    ExtractToken: function (sString, sName, sDelim, sSep, sDefault) {
        sDelim = SSOUtil.valOrDefault(sDelim, "&");
        sSep = SSOUtil.valOrDefault(sSep, "=");
        var value = SSOUtil.valOrDefault(sDefault, null);

        if (!sString)
            return value;

        var start = sString.indexOf(sName + sSep);

        if (0 == start) {
            start += sName.length + 1;
        }
        else if (0 < start) {
            start = sString.indexOf(sDelim + sName + sSep);
            if (0 < start) { start += sDelim.length + sName.length + 1; }
        }

        if (-1 != start) {
            var end = sString.indexOf(sDelim, start);
            if (-1 == end) { end = sString.length; }
            value = sString.substring(start, end);
        }

        return value;
    },

    // Helper to handle supporting default values.
    valOrDefault: function (oVal, oDefault) {
        return ((SSOUtil.exists(oVal)) ? oVal : oDefault);
    },

    // Helper for testing existence.
    exists: function (v) {
        return ((v) ? true : (v == 0 || v == false || v == ""));
    },

    // Helper for determining if an object is a function object
    isFunction: function(f) {
        return (typeof f === "function");
    },

    // Helper to create a random RFC4122.v4 GUID 
    createGuid: function () {
        var guidTemplate = "aaaaaaaa-aaaa-4aaa-baaa-aaaaaaaaaaaa";
        
        guidTemplate.replace(/[ab]/g, function(currChar) {
            var replace = Math.random() * 16 | 0, fullGuid = currChar === "a" ? replace : (replace & 0x3 | 0x8);
            return fullGuid.toString(16);
        });
    },

};

WindowsBrowserSso = {
    /*
     * Enum values for status state
     */
    STATE: {
        START: "start",                 // starting state
        INPROGRESS: "in-progress",      // in progress state
        END: "end",                     // end state (unsuccessful or sso not possible)
        END_SSO: "end-sso",             // end state (successful)
        END_USERS: "end-users"          // end state (successful - list of users shown)
    },

    /*
     * Initialize state
     * 'options' is an object with extra options to use. An example:
     * {
     *    forceTiles: false,                            // required
     *    statusCallback: null,                         // optional (but required for page continuation) 
     *    overallTimeoutMs: 4000,                       // required
     *    initiatePullTimeoutMs: 1000,                  // optional (by default set to overallTimeoutMs)
     *    initiatePullTimeoutAction: 'continue'         // optional (by default 'abort')
     * }
     */
    Initialize: function (options) {
        this.options = options || { forceTiles: false, overallTimeoutMs: 4000 };
        this.options.initiatePullTimeoutMs = this.options.initiatePullTimeoutMs || this.options.overallTimeoutMs;
        this.options.initiatePullTimeoutAction = this.options.initiatePullTimeoutAction || 'abort';

        //
        // Attach a processing state to the global window.
        // Note: we will need this to ensure successful processing.
        //
        this.updateStatus(this.STATE.START, 'Initiating cookie pull');

        this.debugMode = SSOUtil.ExtractQSParam('debugMode').toLowerCase();
    },


    /*
     * Performs the call to Token Broker framework using the msLaunchUri function
     *
     */
    PerformTbAuthRequest: function () {

        var _self = this;
        var baseUri = 'tbauth://login.windows.net?context=' + encodeURIComponent(window.location.href);

        _self.updateStatus(_self.STATE.START, 'Starting cookie pull');

        //
        // Modify the tbauth request if we are trying to request all users 
        //
        var uri = baseUri;
        if (SSOUtil.getCookie('ESTSSSOTILES') || _self.options.forceTiles) {
            // Need to force tiles UX - request the list of users to display tiles
            uri = uri + '&user_id=*';
            SSOUtil.eraseCookie('ESTSSSOTILES');
        }

        //
        // Add requestID so that client logs are generated during tbauth request 
        //
        var currentRequestId = SSOUtil.ExtractQSParam("client-request-id");
        var requestIdParam;
        if (currentRequestId) {
            requestIdParam = currentRequestId;
        } else {
            requestIdParam = SSOUtil.createGuid();
        }
        uri = uri + '&rid=' + requestIdParam;

        var pullStartTime = new Date().getTime();
        var msLaunchUriTimeoutId;
        
        //
        // Launch the tbauth request to the token broker framework.
        // Note: msLaunchUri(Request, SuccessCallBack, FailureCallBack)
        //
        _self.updateStatus(_self.STATE.INPROGRESS, 'Attempting to launch request URI: ' + uri);
        window.navigator.msLaunchUri(
            uri,
            function () {
                clearTimeout(msLaunchUriTimeoutId);
                var elapsedMs = (new Date().getTime()) - pullStartTime;
                _self.updateStatus(_self.STATE.INPROGRESS, 'Cookie pull initiated successfully (took ' + elapsedMs + ' ms)');
            },
            function () {
                clearTimeout(msLaunchUriTimeoutId);
                var elapsedMs = (new Date().getTime()) - pullStartTime;
                _self.updateStatus(_self.STATE.END, 'Cookie pull was NOT initiated successfully (took ' + elapsedMs + ' ms)');
            });

        //
        // Timebox waiting for msLaunchUri response
        // 
        msLaunchUriTimeoutId = setTimeout(function () {
            clearInterval(msLaunchUriTimeoutId);
            if (_self.options.initiatePullTimeoutAction == 'abort') {
                _self.updateStatus(_self.STATE.END, 'Initiating cookie pull timed out.');
            } else {
                _self.updateStatus(_self.STATE.INPROGRESS, 'Initiating cookie pull timed out but starting polling anyway in case cookie was pulled.');
            }
        }, _self.options.initiatePullTimeoutMs);

    },

    /*
     * Attempt to read pushed cookies.
     *
     */
    ProcessPushedCookie: function() {
        
        var _self = this;
        _self.updateStatus(_self.STATE.INPROGRESS, 'Attempting to locate SSO or User List cookie');

        //
        // Set up checking for response (users list or SSO cookie)
        // 
        var timeoutId;

        //
        // Use setInterval function to repeatedly check for new cookies
        // every 250 ms. This repeated check is timeboxed by the supplied
        // timeout. 
        //
        var intervalId = setInterval(function () {
            var usersList = SSOUtil.getCookie('ESTSUSERLIST');
            if (usersList) {
                clearInterval(intervalId);
                clearTimeout(timeoutId);
                SSOUtil.eraseCookie('ESTSUSERLIST');
                // The EVO version has logic to display tiles and handle user selection, but for us, if we have a  
                //  list of users (meaning we have 0, or 2+ PRTs found), we want to reload the request
                //  and continue processing without the PRT cookie.

                // Future versions may contain processing for the user list.

                var userCount = 0;

                if (JSON.parse(usersList).users && JSON.parse(usersList).users.length) {
                    userCount = JSON.parse(usersList).users.length;
                }
                _self.updateStatus(_self.STATE.END_USERS, { message: 'Cookie pull complete. Found user list cookie.', count: userCount });

            } else if (SSOUtil.getCookie('ESTSSSO')) {
                clearInterval(intervalId);
                clearTimeout(timeoutId);
                _self.updateStatus(_self.STATE.END_SSO, 'Cookie pull complete. Found SSO cookie.');
            }
        }, 250);

        //
        // Timebox the checking
        //
        timeoutId = setTimeout(function () {
            clearInterval(intervalId);
            _self.updateStatus(_self.STATE.END, 'Pull cookie timed out. Could not locate UserList or SSO cookie.');
        }, _self.options.overallTimeoutMs);
    },

    /*
     * Initiate pull of browser SSO cookie
     */
    PullBrowserSsoCookie: function () {

        var _self = this;

        //
        // Check if the browser supports the msLaunchUri functionality.
        // Note: Currently IE and Edge are the only browsers that support the 
        // Explorer function 'msLaunchUri'
        //
        if (!window.navigator || !SSOUtil.isFunction(window.navigator.msLaunchUri)) {
            this.updateStatus(this.STATE.START, null);
            this.updateStatus(this.STATE.END, 'window.navigator.msLaunchUri is not available');
            return;
        }

        //
        // Send pull cookie request 
        //
        _self.PerformTbAuthRequest();

        //
        // Process the response if request was successful
        // Note: The way our status callback is defined, we should never get 
        // to this point if we failed to perform the tb auth request. 
        //
        // The check is left to keep the status callback functionality
        // separate from this code. 
        //
        if (window.CurrentProcessingState == this.STATE.INPROGRESS) {
            _self.ProcessPushedCookie();
        }
    },

    /*
     * Function to update the processing status 
     */
    updateStatus: function (_state, _message) {

        //
        // Update the current state
        //
        window.CurrentProcessingState = _state;

        //
        // Perform any non-local callback functionality
        //
        if (this.options.statusCallback && SSOUtil.isFunction(this.options.statusCallback)) {
            this.options.statusCallback({ state: _state, message: _message, debugMode: this.debugMode });
        }
    }
};

var BrowserSso = WindowsBrowserSso;


//]]>
</script>


        
        <link rel="stylesheet" type="text/css" href="/adfs/portal/css/style.css?id=CF568805A6378B0508FE2ED7A52DD13B2137BF02CDA10C4630A144C0B511F38F" /><style>.illustrationClass {background-image:url(/adfs/portal/illustration/illustration.png?id=426354F6B97B8F13D54E4CDEDDC2450DAC54BD6A57ED75B728C29DFBD2824D85);}</style>

    </head>
    <body dir="ltr" class="body">
    <div id="noScript" style="position:static; width:100%; height:100%; z-index:100">
        <h1>JavaScript required</h1>
        <p>JavaScript is required. This web browser does not support JavaScript or JavaScript in this web browser is not enabled.</p>
        <p>To find out if your web browser supports JavaScript or to enable JavaScript, see web browser help.</p>
    </div>
    <script type="text/javascript" language="JavaScript">
         document.getElementById("noScript").style.display = "none";
    </script>
    <div id="fullPage">
        <div id="brandingWrapper" class="float">
            <div id="branding"></div>
        </div>
        <div id="contentWrapper" class="float">
            <div id="content">
                <div id="header">
                    KPMG Login
                </div>
                <div id="workArea">
                    <div id="cookiePullPage">
    <!-- Display the original text if page is viewed in IE9-, as animation is not supported -->
    <div id="cookiePullPageText" class="groupMargin" style="display: none;">Attempting Single Sign-On...</div>
    
    <div id="cookiePullWaitingWheel">
        <!-- CSS for small "waiting" wheel -->
        <style>
            #floatingCirclesG {
                position: relative;
                width: 125px;
                height: 125px;
                margin: auto;
                transform: scale(0.4);
                -o-transform: scale(0.4);
                -ms-transform: scale(0.4);
                -webkit-transform: scale(0.4);
                -moz-transform: scale(0.4);
            }

            .f_circleG {
                position: absolute;
                height: 22px;
                width: 22px;
                border-radius: 12px;
                -o-border-radius: 12px;
                -ms-border-radius: 12px;
                -webkit-border-radius: 12px;
                -moz-border-radius: 12px;
                animation-name: f_fadeG;
                -o-animation-name: f_fadeG;
                -ms-animation-name: f_fadeG;
                -webkit-animation-name: f_fadeG;
                -moz-animation-name: f_fadeG;
                animation-duration: 1.2s;
                -o-animation-duration: 1.2s;
                -ms-animation-duration: 1.2s;
                -webkit-animation-duration: 1.2s;
                -moz-animation-duration: 1.2s;
                animation-iteration-count: infinite;
                -o-animation-iteration-count: infinite;
                -ms-animation-iteration-count: infinite;
                -webkit-animation-iteration-count: infinite;
                -moz-animation-iteration-count: infinite;
                animation-direction: normal;
                -o-animation-direction: normal;
                -ms-animation-direction: normal;
                -webkit-animation-direction: normal;
                -moz-animation-direction: normal;
            }

            #frotateG_01 {
                left: 0;
                top: 51px;
                animation-delay: 0.45s;
                -o-animation-delay: 0.45s;
                -ms-animation-delay: 0.45s;
                -webkit-animation-delay: 0.45s;
                -moz-animation-delay: 0.45s;
            }

            #frotateG_02 {
                left: 15px;
                top: 15px;
                animation-delay: 0.6s;
                -o-animation-delay: 0.6s;
                -ms-animation-delay: 0.6s;
                -webkit-animation-delay: 0.6s;
                -moz-animation-delay: 0.6s;
            }

            #frotateG_03 {
                left: 51px;
                top: 0;
                animation-delay: 0.75s;
                -o-animation-delay: 0.75s;
                -ms-animation-delay: 0.75s;
                -webkit-animation-delay: 0.75s;
                -moz-animation-delay: 0.75s;
            }

            #frotateG_04 {
                right: 15px;
                top: 15px;
                animation-delay: 0.9s;
                -o-animation-delay: 0.9s;
                -ms-animation-delay: 0.9s;
                -webkit-animation-delay: 0.9s;
                -moz-animation-delay: 0.9s;
            }

            #frotateG_05 {
                right: 0;
                top: 51px;
                animation-delay: 1.05s;
                -o-animation-delay: 1.05s;
                -ms-animation-delay: 1.05s;
                -webkit-animation-delay: 1.05s;
                -moz-animation-delay: 1.05s;
            }

            #frotateG_06 {
                right: 15px;
                bottom: 15px;
                animation-delay: 1.2s;
                -o-animation-delay: 1.2s;
                -ms-animation-delay: 1.2s;
                -webkit-animation-delay: 1.2s;
                -moz-animation-delay: 1.2s;
            }

            #frotateG_07 {
                left: 51px;
                bottom: 0;
                animation-delay: 1.35s;
                -o-animation-delay: 1.35s;
                -ms-animation-delay: 1.35s;
                -webkit-animation-delay: 1.35s;
                -moz-animation-delay: 1.35s;
            }

            #frotateG_08 {
                left: 15px;
                bottom: 15px;
                animation-delay: 1.5s;
                -o-animation-delay: 1.5s;
                -ms-animation-delay: 1.5s;
                -webkit-animation-delay: 1.5s;
                -moz-animation-delay: 1.5s;
            }

            @keyframes f_fadeG {
                0% {
                    background-color: rgb(47,146,212);
                }

                100% {
                    background-color: rgb(255,255,255);
                }
            }

            @-o-keyframes f_fadeG {
                0% {
                    background-color: rgb(47,146,212);
                }

                100% {
                    background-color: rgb(255,255,255);
                }
            }

            @-ms-keyframes f_fadeG {
                0% {
                    background-color: rgb(47,146,212);
                }

                100% {
                    background-color: rgb(255,255,255);
                }
            }

            @-webkit-keyframes f_fadeG {
                0% {
                    background-color: rgb(47,146,212);
                }

                100% {
                    background-color: rgb(255,255,255);
                }
            }

            @-moz-keyframes f_fadeG {
                0% {
                    background-color: rgb(47,146,212);
                }

                100% {
                    background-color: rgb(255,255,255);
                }
            }
        </style>

        <!-- Div containing small "waiting" wheel -->
        <div id="floatingCirclesG">
            <div class="f_circleG" id="frotateG_01"></div>
            <div class="f_circleG" id="frotateG_02"></div>
            <div class="f_circleG" id="frotateG_03"></div>
            <div class="f_circleG" id="frotateG_04"></div>
            <div class="f_circleG" id="frotateG_05"></div>
            <div class="f_circleG" id="frotateG_06"></div>
            <div class="f_circleG" id="frotateG_07"></div>
            <div class="f_circleG" id="frotateG_08"></div>
        </div>
    </div>

    <script type="text/javascript">

        //
        // Hide spinning "wait" wheel, and display original wait text if browser doesn't support animation
        //
        var browserSupportsAnimation = false;
        var domPrefixes = 'Webkit,Moz,O,ms'.split(',');
        var waitingWheelDiv = document.getElementById("cookiePullWaitingWheel");

        // Check if animation is supported without a DOM prefix
        if (waitingWheelDiv.style.animationName !== undefined) { browserSupportsAnimation = true; }
        
        if (browserSupportsAnimation === false) {
            // Check all the DOM prefixes to see if keyframes are supported with a prefix
            for (var i = 0; i < domPrefixes.length; i++) {
                if (waitingWheelDiv.style[domPrefixes[i] + 'AnimationName'] !== undefined) {
                    browserSupportsAnimation = true;
                    break;
                }
            }
        }

        if (browserSupportsAnimation === false) {
            document.getElementById("cookiePullPageText").style.display = '';
            waitingWheelDiv.style.display = 'none';
        }

        var bssoOptions = {
            forceTiles: false,
            overallTimeoutMs: 4000,
            statusCallback: function (status) {

                if (status.message && status.debugMode) {
                    console.log(status.message);
                }

                //
                // Note: We append the cookie pull query parameter to the PageActionUrl, then we set the parameter value below
                //
                if (status.state == WindowsBrowserSso.STATE.END) {
                    window.location.replace("https://gfs2.glb.kworld.kpmg.com/adfs/ls/?client-request-id=2112a5a1-e0dd-d000-0235-ff5d7e271b73&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%26estsrequest%3drQQIARAAlVFNaNNgGE6aLq51c13xIHjRMBA203xf8iX5UhiYpJ0dUzelrlMZJb9r1zTpmnTuh4l48iRDPHkQFESZCOJJdtLrBOm5IAxB1B1EPchu2iKCx_nw8vAc3vfleZ93lIIZmB0BfyCwPWaB60LWcnrqHzTTydTDx-mT73eJdkz8NX_j1PFwi-QqUdQIsxwXNgJ2wQtMw8vUGvWFjBXUubIbNOshZzuu0fKijBE2Vl6RZJskP5PkVmwJ5QRZ0QCvSWJOVnSZF3QZanksSSKE6gRW8qoqIQw1Den6hI5YSVBVALtjkiCLUNFxXhQRDxUZKBpWIdQkLOq8ystAx5jnNVFDkpRHggQlyCNR7cSGptVWVOF7FDSra86PWKLnsdwIwug-FZ_Ogetb1IHieEmNdO9yeWSaLA-wwiLU7cAWdlhXho6EgSBjk9-h6KDh-FX7A3V00VgwfN-IKkbtzN-M2nFyLz4AqGx_fzJFHCNOEPtx8lFfN-tP39-gd8_fag_u3ln8-fEssdPH2Ze8YAoLHCiG0WwB54SVxTFRKgY1szRrectgdW5yqcRd9S6vBeN8Fm7S5CZNb9OJfipFMJQ-A7_R5O1DxHbif7_WPkx2BmAyYQVm0_Dtqp0egdC0McACi2UHsAiaImuaMmaBaSHBwgZ2sNEZQEna8oxqPUyPrjNVuxwFNcdnsuvMSj0sW1ZPLRteywmZ7DWma5CZ39jYuDV4oO2vB4n9I0-f7Ty5d_PL18Le0Olzni5cLE3lKuCCUixeCQtVVCqMnV_z52qyLblN1RwL_dmotTozOf4iRex2a5jYH96M_QY1&cbcxt=&username=jagannathak%40kpmg.com&mkt=&lc=&pullStatus=0");
                } else if (status.state == WindowsBrowserSso.STATE.END_SSO) {
                    window.location.replace("https://gfs2.glb.kworld.kpmg.com/adfs/ls/?client-request-id=2112a5a1-e0dd-d000-0235-ff5d7e271b73&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%26estsrequest%3drQQIARAAlVFNaNNgGE6aLq51c13xIHjRMBA203xf8iX5UhiYpJ0dUzelrlMZJb9r1zTpmnTuh4l48iRDPHkQFESZCOJJdtLrBOm5IAxB1B1EPchu2iKCx_nw8vAc3vfleZ93lIIZmB0BfyCwPWaB60LWcnrqHzTTydTDx-mT73eJdkz8NX_j1PFwi-QqUdQIsxwXNgJ2wQtMw8vUGvWFjBXUubIbNOshZzuu0fKijBE2Vl6RZJskP5PkVmwJ5QRZ0QCvSWJOVnSZF3QZanksSSKE6gRW8qoqIQw1Den6hI5YSVBVALtjkiCLUNFxXhQRDxUZKBpWIdQkLOq8ystAx5jnNVFDkpRHggQlyCNR7cSGptVWVOF7FDSra86PWKLnsdwIwug-FZ_Ogetb1IHieEmNdO9yeWSaLA-wwiLU7cAWdlhXho6EgSBjk9-h6KDh-FX7A3V00VgwfN-IKkbtzN-M2nFyLz4AqGx_fzJFHCNOEPtx8lFfN-tP39-gd8_fag_u3ln8-fEssdPH2Ze8YAoLHCiG0WwB54SVxTFRKgY1szRrectgdW5yqcRd9S6vBeN8Fm7S5CZNb9OJfipFMJQ-A7_R5O1DxHbif7_WPkx2BmAyYQVm0_Dtqp0egdC0McACi2UHsAiaImuaMmaBaSHBwgZ2sNEZQEna8oxqPUyPrjNVuxwFNcdnsuvMSj0sW1ZPLRteywmZ7DWma5CZ39jYuDV4oO2vB4n9I0-f7Ty5d_PL18Le0Olzni5cLE3lKuCCUixeCQtVVCqMnV_z52qyLblN1RwL_dmotTozOf4iRex2a5jYH96M_QY1&cbcxt=&username=jagannathak%40kpmg.com&mkt=&lc=&pullStatus=1");
                } else if (status.state == WindowsBrowserSso.STATE.END_USERS) {
                    window.location.replace("https://gfs2.glb.kworld.kpmg.com/adfs/ls/?client-request-id=2112a5a1-e0dd-d000-0235-ff5d7e271b73&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%26estsrequest%3drQQIARAAlVFNaNNgGE6aLq51c13xIHjRMBA203xf8iX5UhiYpJ0dUzelrlMZJb9r1zTpmnTuh4l48iRDPHkQFESZCOJJdtLrBOm5IAxB1B1EPchu2iKCx_nw8vAc3vfleZ93lIIZmB0BfyCwPWaB60LWcnrqHzTTydTDx-mT73eJdkz8NX_j1PFwi-QqUdQIsxwXNgJ2wQtMw8vUGvWFjBXUubIbNOshZzuu0fKijBE2Vl6RZJskP5PkVmwJ5QRZ0QCvSWJOVnSZF3QZanksSSKE6gRW8qoqIQw1Den6hI5YSVBVALtjkiCLUNFxXhQRDxUZKBpWIdQkLOq8ystAx5jnNVFDkpRHggQlyCNR7cSGptVWVOF7FDSra86PWKLnsdwIwug-FZ_Ogetb1IHieEmNdO9yeWSaLA-wwiLU7cAWdlhXho6EgSBjk9-h6KDh-FX7A3V00VgwfN-IKkbtzN-M2nFyLz4AqGx_fzJFHCNOEPtx8lFfN-tP39-gd8_fag_u3ln8-fEssdPH2Ze8YAoLHCiG0WwB54SVxTFRKgY1szRrectgdW5yqcRd9S6vBeN8Fm7S5CZNb9OJfipFMJQ-A7_R5O1DxHbif7_WPkx2BmAyYQVm0_Dtqp0egdC0McACi2UHsAiaImuaMmaBaSHBwgZ2sNEZQEna8oxqPUyPrjNVuxwFNcdnsuvMSj0sW1ZPLRteywmZ7DWma5CZ39jYuDV4oO2vB4n9I0-f7Ty5d_PL18Le0Olzni5cLE3lKuCCUixeCQtVVCqMnV_z52qyLblN1RwL_dmotTozOf4iRex2a5jYH96M_QY1&cbcxt=&username=jagannathak%40kpmg.com&mkt=&lc=&pullStatus=" + status.message.count.toString());
                }
            }
        };

        BrowserSso.Initialize(bssoOptions);
        BrowserSso.PullBrowserSsoCookie();

    </script>
</div>

                </div>
                <div id="footerPlaceholder"></div>
            </div>
            <div id="footer">
                <div id="footerLinks" class="floatReverse">
                     <div><span id="copyright">&#169; 2016 Microsoft</span></div>
                </div>
            </div>
        </div> 
    </div>
    <script type='text/javascript'>
//<![CDATA[
﻿// Copyright (c) Microsoft Corporation.  All rights reserved.

// This file contains several workarounds on inconsistent browser behaviors that administrators may customize.
"use strict";

// iPhone email friendly keyboard does not include "\" key, use regular keyboard instead.
// Note change input type does not work on all versions of all browsers.
if (navigator.userAgent.match(/iPhone/i) != null) {
    var emails = document.querySelectorAll("input[type='email']");
    if (emails) {
        for (var i = 0; i < emails.length; i++) {
            emails[i].type = 'text';
        }
    }
}

// In the CSS file we set the ms-viewport to be consistent with the device dimensions, 
// which is necessary for correct functionality of immersive IE. 
// However, for Windows 8 phone we need to reset the ms-viewport's dimension to its original
// values (auto), otherwise the viewport dimensions will be wrong for Windows 8 phone.
// Windows 8 phone has agent string 'IEMobile 10.0'
if (navigator.userAgent.match(/IEMobile\/10\.0/)) {
    var msViewportStyle = document.createElement("style");
    msViewportStyle.appendChild(
        document.createTextNode(
            "@-ms-viewport{width:auto!important}"
        )
    );
    msViewportStyle.appendChild(
        document.createTextNode(
            "@-ms-viewport{height:auto!important}"
        )
    );
    document.getElementsByTagName("head")[0].appendChild(msViewportStyle);
}

// If the innerWidth is defined, use it as the viewport width.
if (window.innerWidth && window.outerWidth && window.innerWidth !== window.outerWidth) {
    var viewport = document.querySelector("meta[name=viewport]");
    viewport.setAttribute('content', 'width=' + window.innerWidth + ', initial-scale=1.0, user-scalable=1');
}

// Gets the current style of a specific property for a specific element.
function getStyle(element, styleProp) {
    var propStyle = null;

    if (element && element.currentStyle) {
        propStyle = element.currentStyle[styleProp];
    }
    else if (element && window.getComputedStyle) {
        propStyle = document.defaultView.getComputedStyle(element, null).getPropertyValue(styleProp);
    }

    return propStyle;
}

// The script below is used for downloading the illustration image 
// only when the branding is displaying. This script work together
// with the code in PageBase.cs that sets the html inline style
// containing the class 'illustrationClass' with the background image.
var computeLoadIllustration = function () {
    var branding = document.getElementById("branding");
    var brandingDisplay = getStyle(branding, "display");
    var brandingWrapperDisplay = getStyle(document.getElementById("brandingWrapper"), "display");

    if (brandingDisplay && brandingDisplay !== "none" &&
        brandingWrapperDisplay && brandingWrapperDisplay !== "none") {
        var newClass = "illustrationClass";

        if (branding.classList && branding.classList.add) {
            branding.classList.add(newClass);
        } else if (branding.className !== undefined) {
            branding.className += " " + newClass;
        }
        if (window.removeEventListener) {
            window.removeEventListener('load', computeLoadIllustration, false);
            window.removeEventListener('resize', computeLoadIllustration, false);
        }
        else if (window.detachEvent) {
            window.detachEvent('onload', computeLoadIllustration);
            window.detachEvent('onresize', computeLoadIllustration);
        }
    }
};

if (window.addEventListener) {
    window.addEventListener('resize', computeLoadIllustration, false);
    window.addEventListener('load', computeLoadIllustration, false);
}
else if (window.attachEvent) {
    window.attachEvent('onresize', computeLoadIllustration);
    window.attachEvent('onload', computeLoadIllustration);
}

// Function to change illustration image. Usage example below.
function SetIllustrationImage(imageUri) {
    var illustrationImageClass = '.illustrationClass {background-image:url(' + imageUri + ');}';

    var css = document.createElement('style');
    css.type = 'text/css';

    if (css.styleSheet) css.styleSheet.cssText = illustrationImageClass;
    else css.appendChild(document.createTextNode(illustrationImageClass));

    document.getElementsByTagName("head")[0].appendChild(css);
}

// Example to change illustration image on HRD page after adding the image to active theme:
// PSH> Set-AdfsWebTheme -TargetName <activeTheme> -AdditionalFileResource @{uri='/adfs/portal/images/hrd.jpg';path='.\hrd.jpg'}
//
//if (typeof HRD != 'undefined') {
//    SetIllustrationImage('/adfs/portal/images/hrd.jpg');
//}


// Replace “Sign in with organizational account” string.
// Check whether the loginMessage element is present on this page.
var loginMessage = document.getElementById('loginMessage');
if (loginMessage)
{
	// loginMessage element is present, modify its properties.
	loginMessage.innerHTML = 'Sign in with your KPMG email address';
}


// Add KPMG footer links
var kpmgFooterLinks = document.createElement('div');
kpmgFooterLinks.id = 'kpmgFooterLinks';
kpmgFooterLinks.innerHTML = '<a style="float: left; color:#B2B2B2;" href="https://home.kpmg.com/xx/en/home/<USER>/legal.html">Legal</a> &nbsp|&nbsp <a style="float: none; color:#B2B2B2;" href="https://home.kpmg.com/xx/en/home/<USER>/privacy.html">Privacy</a></br></br>';

var footerDiv = document.getElementById('footerLinks');
footerDiv.appendChild(kpmgFooterLinks);


// Add KPMG footer text and buttons
var date = new Date();
var year = date.getFullYear();

var kpmgFooter = document.createElement('div');
kpmgFooter.id = 'kpmgFooter';
kpmgFooter.innerHTML = '&copy' + year + ' Copyright owned by one or more of the KPMG International entities. KPMG International entities provide no services to clients. All rights reserved. KPMG refers to the global organization or to one or more of the member firms of KPMG International Limited (“KPMG International”), each of which is a separate legal entity. KPMG International Limited is a private English company limited by guarantee and does not provide services to clients. <br/><a style="float: right;PADDING-RIGHT: 10px; PADDING-TOP: 10px" </a>';

var footerDiv = document.getElementById('footer');
footerDiv.appendChild(kpmgFooter);


// Code to change "<EMAIL>" placeholder in userName input text box.
var userNameInputTextBox = document.getElementById('userNameInput');
if (userNameInputTextBox) {
   var placeholderText = 'Username';
   if (userNameInputTextBox.placeholder)
       userNameInputTextBox.placeholder = placeholderText;
}

//]]>
</script>


    </body>
</html> 

