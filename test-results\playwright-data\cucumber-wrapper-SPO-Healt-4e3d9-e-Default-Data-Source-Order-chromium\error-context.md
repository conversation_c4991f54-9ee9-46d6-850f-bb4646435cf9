# Test info

- Name: SPO Health Check >> TC0101- <PERSON>gin and Check the Default Data Source Order
- Location: C:\workspace\Playwright_Automation_Framework\tests\cucumber-wrapper.spec.ts:34:11

# Error details

```
Error: page.screenshot: Target page, context or browser has been closed
Call log:
  - taking page screenshot
  - waiting for fonts to load...

    at PlaywrightCucumberRunner.executeScenario (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:270:18)
    at C:\workspace\Playwright_Automation_Framework\tests\cucumber-wrapper.spec.ts:45:9
```

# Test source

```ts
  170 |             tags: scenarioTags,
  171 |             steps,
  172 |             feature: featureName
  173 |           });
  174 |         }
  175 |       } else {
  176 |         currentLine++;
  177 |       }
  178 |     }
  179 |
  180 |     return {
  181 |       name: featureName,
  182 |       description: description.trim(),
  183 |       tags: featureTags,
  184 |       scenarios,
  185 |       file
  186 |     };
  187 |   }
  188 |
  189 |
  190 |
  191 |   /**
  192 |    * Execute a Cucumber step using the step definitions
  193 |    */
  194 |   private async executeStep(world: PlaywrightWorld, stepText: string, testInfo: any): Promise<void> {
  195 |     try {
  196 |       await this.stepRegistry.executeStep(world, stepText);
  197 |     } catch (error) {
  198 |       this.logger.error(`❌ Step failed: ${stepText}`, error);
  199 |       throw error;
  200 |     }
  201 |   }
  202 |
  203 |
  204 |
  205 |   /**
  206 |    * Execute a single scenario
  207 |    */
  208 |   async executeScenario(scenario: CucumberScenario, page: Page, context: BrowserContext, testInfo: any): Promise<void> {
  209 |     // Initialize Playwright world
  210 |     const world = new PlaywrightWorld();
  211 |     await world.init(page, context);
  212 |
  213 |     try {
  214 |       // Execute each step
  215 |       for (let i = 0; i < scenario.steps.length; i++) {
  216 |         const step = scenario.steps[i];
  217 |         const stepText = `${step.keyword} ${step.text}`;
  218 |
  219 |         console.log(`📋 Step ${i + 1}/${scenario.steps.length}: ${stepText}`);
  220 |
  221 |         // Take screenshot before step
  222 |         const beforeScreenshot = path.join(testInfo.outputDir, `step-${i + 1}-before.png`);
  223 |         await page.screenshot({ path: beforeScreenshot, fullPage: true });
  224 |         await testInfo.attach(`Step ${i + 1} - Before: ${stepText}`, {
  225 |           path: beforeScreenshot,
  226 |           contentType: 'image/png'
  227 |         });
  228 |
  229 |         // Execute the step
  230 |         const startTime = Date.now();
  231 |         await this.executeStep(world, stepText, testInfo);
  232 |         const duration = Date.now() - startTime;
  233 |
  234 |         // Take screenshot after step
  235 |         const afterScreenshot = path.join(testInfo.outputDir, `step-${i + 1}-after.png`);
  236 |         await page.screenshot({ path: afterScreenshot, fullPage: true });
  237 |         await testInfo.attach(`Step ${i + 1} - After: ${stepText} (${duration}ms)`, {
  238 |           path: afterScreenshot,
  239 |           contentType: 'image/png'
  240 |         });
  241 |
  242 |         console.log(`✅ Step completed in ${duration}ms`);
  243 |       }
  244 |
  245 |       console.log(`🎉 Scenario completed successfully: ${scenario.name}`);
  246 |
  247 |       // Attach video if available
  248 |       if (testInfo.attachments) {
  249 |         const videoAttachment = testInfo.attachments.find((a: any) => a.name === 'video');
  250 |         if (videoAttachment) {
  251 |           console.log(`🎬 Video recorded: ${videoAttachment.path}`);
  252 |         }
  253 |       }
  254 |
  255 |       // Attach trace if available
  256 |       const tracePath = path.join(testInfo.outputDir, 'trace.zip');
  257 |       if (await this.fileExists(tracePath)) {
  258 |         await testInfo.attach('Trace', {
  259 |           path: tracePath,
  260 |           contentType: 'application/zip'
  261 |         });
  262 |         console.log(`🔍 Trace recorded: ${tracePath}`);
  263 |       }
  264 |
  265 |     } catch (error) {
  266 |       console.error(`❌ Scenario failed: ${scenario.name}`, error);
  267 |
  268 |       // Take failure screenshot
  269 |       const failureScreenshot = path.join(testInfo.outputDir, 'failure-screenshot.png');
> 270 |       await page.screenshot({ path: failureScreenshot, fullPage: true });
      |                  ^ Error: page.screenshot: Target page, context or browser has been closed
  271 |       await testInfo.attach('Failure Screenshot', {
  272 |         path: failureScreenshot,
  273 |         contentType: 'image/png'
  274 |       });
  275 |
  276 |       throw error;
  277 |     } finally {
  278 |       await world.cleanup();
  279 |     }
  280 |   }
  281 |
  282 |   /**
  283 |    * Check if scenario tags match the filter
  284 |    */
  285 |   matchesTags(tags: string[], filter: string): boolean {
  286 |     if (!filter) return true;
  287 |
  288 |     // Simple tag matching - supports @tag format
  289 |     const filterTags = filter.split(/\s+/).map(tag => tag.trim());
  290 |
  291 |     return filterTags.some(filterTag =>
  292 |       tags.some(tag => tag === filterTag)
  293 |     );
  294 |   }
  295 |
  296 |   /**
  297 |    * Check if file exists
  298 |    */
  299 |   private async fileExists(filePath: string): Promise<boolean> {
  300 |     try {
  301 |       await fs.promises.access(filePath);
  302 |       return true;
  303 |     } catch {
  304 |       return false;
  305 |     }
  306 |   }
  307 | }
  308 |
  309 | // Export for use in Playwright tests
  310 | export { CucumberScenario, CucumberStep, CucumberFeature };
  311 |
```