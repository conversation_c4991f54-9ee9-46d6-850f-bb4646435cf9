

<!-- Copyright (C) Microsoft Corporation. All rights reserved. -->
<!DOCTYPE html>
<html dir="ltr" class="" lang="en">
<head>
    <title>Sign in to your account</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0, user-scalable=yes">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="-1">
    <link rel="preconnect" href="https://aadcdn.msftauth.net" crossorigin>
<meta http-equiv="x-dns-prefetch-control" content="on">
<link rel="dns-prefetch" href="//aadcdn.msftauth.net">
<link rel="dns-prefetch" href="//aadcdn.msauth.net">

    <meta name="PageID" content="ConvergedSignIn" />
    <meta name="SiteID" content="" />
    <meta name="ReqLC" content="1033" />
    <meta name="LocLC" content="en-US" />


        <meta name="format-detection" content="telephone=no" />

    <noscript>
        <meta http-equiv="Refresh" content="0; URL=https://login.microsoftonline.com/jsdisabled" />
    </noscript>

    
    
<meta name="robots" content="none" />

<script type="text/javascript" nonce='glI8Yd4q81tJsJGY-JWlcQ'>//<![CDATA[
$Config={"fShowPersistentCookiesWarning":false,"urlMsaSignUp":"https://login.live.com/oauth20_authorize.srf?scope=openid+profile+email+offline_access\u0026response_type=code\u0026client_id=********-085c-4d86-bf88-cf50c7252078\u0026response_mode=form_post\u0026redirect_uri=https%3a%2f%2flogin.microsoftonline.com%2fcommon%2ffederation%2foauth2msa\u0026state=rQQIARAAlVE7bNNQFLXj1DQhpWknRmR1ocLxe_az_RypEo4dtymfIiRUPkKRP89pGjt24yT0oy6wwICoGCsxgGDJVDGhLkWMnTIxlIWxqgRCIKEsCBKxMJajq6Mj3aurc8-dZWABFmfAX0j8iHng-5B3yUj9g9Z0Nv_yzdSTj5epfkr-efDhpHuwS2eqQb1LCm4U9mhhpd2Ok6IgJHHE14LIsYNCIw5ro65Q9aNWmAge8e1O0C7YSbz-jqb7NH1M073UWhkAqBoIli0RWKaBLF3WTFwSTVVDumVoKpIVAA3TUERNVGSD1zRNLkPTKJWNkqUoimQihFSgKQDoOpKApciwZEEDAFPHCCMZlTVU0qEOylCDpnqUmlzSO-0VcURRq75JvqcyI4_VOErau0x6yQQPesyponnLzAzv8kXkOLwIsMYjNJzALia8r0KiYCCp2BEPGTaKSbPu9dP0SToHmOL4eDZPnacuUIM0_WpsGDDxt14_GlQWXlwTfzx7fpE6HBM2NNJcvXll8WpDEQxSX26USncad91El1eVWK84G83m6vLGbXnRqc0pRbjD0jssu89mxpk8xTHGDfiNpR-fofYz__ue_ll6L0cf5WA240ZOy256dW96BkLHwwBLPFYJ4BF0ZN5xVMwDx0WSi21MsH2UQ1nWDex6mEzPbnF1r9qOGqTJFbe49TCpuu5Ide2gQxKueI8beuTub29vP5w41fb3E9Tg3Jfep6e_fh9_XTiZvNQBEnGu20HQxbKlxGtOsuJ6FdvvqrfCyvwddXO9FsrteSJsNub28tTnYU1Rg6md1B81\u0026estsfed=1\u0026uaid=8911a5a140d6d0000235f6bfc2ea76bf\u0026signup=1\u0026lw=1\u0026fl=easi2\u0026cobrandid=11bd8083-87e0-41b5-bb78-0bc43c8a8e8a\u0026fci=********-0000-0ff1-ce00-************\u0026wsucxt=1\u0026skipkmsi=1","urlMsaLogout":"https://login.live.com/logout.srf?iframed_by=https%3a%2f%2flogin.microsoftonline.com","urlOtherIdpForget":"https://login.live.com/forgetme.srf?iframed_by=https%3a%2f%2flogin.microsoftonline.com","showCantAccessAccountLink":true,"urlGitHubFed":"https://login.live.com/oauth20_authorize.srf?scope=openid+profile+email+offline_access\u0026response_type=code\u0026client_id=********-085c-4d86-bf88-cf50c7252078\u0026response_mode=form_post\u0026redirect_uri=https%3a%2f%2flogin.microsoftonline.com%2fcommon%2ffederation%2foauth2msa\u0026state=rQQIARAAlVE7bNNQFLXj1DQhpWknRmR1ocLxe_az_RypEo4dtymfIiRUPkKRP89pGjt24yT0oy6wwICoGCsxgGDJVDGhLkWMnTIxlIWxqgRCIKEsCBKxMJajq6Mj3aurc8-dZWABFmfAX0j8iHng-5B3yUj9g9Z0Nv_yzdSTj5epfkr-efDhpHuwS2eqQb1LCm4U9mhhpd2Ok6IgJHHE14LIsYNCIw5ro65Q9aNWmAge8e1O0C7YSbz-jqb7NH1M073UWhkAqBoIli0RWKaBLF3WTFwSTVVDumVoKpIVAA3TUERNVGSD1zRNLkPTKJWNkqUoimQihFSgKQDoOpKApciwZEEDAFPHCCMZlTVU0qEOylCDpnqUmlzSO-0VcURRq75JvqcyI4_VOErau0x6yQQPesyponnLzAzv8kXkOLwIsMYjNJzALia8r0KiYCCp2BEPGTaKSbPu9dP0SToHmOL4eDZPnacuUIM0_WpsGDDxt14_GlQWXlwTfzx7fpE6HBM2NNJcvXll8WpDEQxSX26USncad91El1eVWK84G83m6vLGbXnRqc0pRbjD0jssu89mxpk8xTHGDfiNpR-fofYz__ue_ll6L0cf5WA240ZOy256dW96BkLHwwBLPFYJ4BF0ZN5xVMwDx0WSi21MsH2UQ1nWDex6mEzPbnF1r9qOGqTJFbe49TCpuu5Ide2gQxKueI8beuTub29vP5w41fb3E9Tg3Jfep6e_fh9_XTiZvNQBEnGu20HQxbKlxGtOsuJ6FdvvqrfCyvwddXO9FsrteSJsNub28tTnYU1Rg6md1B81\u0026estsfed=1\u0026uaid=8911a5a140d6d0000235f6bfc2ea76bf\u0026cobrandid=11bd8083-87e0-41b5-bb78-0bc43c8a8e8a\u0026fci=********-0000-0ff1-ce00-************\u0026wsucxt=1\u0026skipkmsi=1\u0026idp_hint=github.com","arrExternalTrustedRealmFederatedIdps":[],"fShowSignInWithGitHubOnlyOnCredPicker":true,"fEnableShowResendCode":true,"iShowResendCodeDelay":90000,"sSMSCtryPhoneData":"AF~Afghanistan~93!!!AX~Åland Islands~358!!!AL~Albania~355!!!DZ~Algeria~213!!!AS~American Samoa~1!!!AD~Andorra~376!!!AO~Angola~244!!!AI~Anguilla~1!!!AG~Antigua and Barbuda~1!!!AR~Argentina~54!!!AM~Armenia~374!!!AW~Aruba~297!!!AC~Ascension Island~247!!!AU~Australia~61!!!AT~Austria~43!!!AZ~Azerbaijan~994!!!BS~Bahamas~1!!!BH~Bahrain~973!!!BD~Bangladesh~880!!!BB~Barbados~1!!!BY~Belarus~375!!!BE~Belgium~32!!!BZ~Belize~501!!!BJ~Benin~229!!!BM~Bermuda~1!!!BT~Bhutan~975!!!BO~Bolivia~591!!!BQ~Bonaire~599!!!BA~Bosnia and Herzegovina~387!!!BW~Botswana~267!!!BR~Brazil~55!!!IO~British Indian Ocean Territory~246!!!VG~British Virgin Islands~1!!!BN~Brunei~673!!!BG~Bulgaria~359!!!BF~Burkina Faso~226!!!BI~Burundi~257!!!CV~Cabo Verde~238!!!KH~Cambodia~855!!!CM~Cameroon~237!!!CA~Canada~1!!!KY~Cayman Islands~1!!!CF~Central African Republic~236!!!TD~Chad~235!!!CL~Chile~56!!!CN~China~86!!!CX~Christmas Island~61!!!CC~Cocos (Keeling) Islands~61!!!CO~Colombia~57!!!KM~Comoros~269!!!CG~Congo~242!!!CD~Congo (DRC)~243!!!CK~Cook Islands~682!!!CR~Costa Rica~506!!!CI~Côte d\u0027Ivoire~225!!!HR~Croatia~385!!!CU~Cuba~53!!!CW~Curaçao~599!!!CY~Cyprus~357!!!CZ~Czechia~420!!!DK~Denmark~45!!!DJ~Djibouti~253!!!DM~Dominica~1!!!DO~Dominican Republic~1!!!EC~Ecuador~593!!!EG~Egypt~20!!!SV~El Salvador~503!!!GQ~Equatorial Guinea~240!!!ER~Eritrea~291!!!EE~Estonia~372!!!ET~Ethiopia~251!!!FK~Falkland Islands~500!!!FO~Faroe Islands~298!!!FJ~Fiji~679!!!FI~Finland~358!!!FR~France~33!!!GF~French Guiana~594!!!PF~French Polynesia~689!!!GA~Gabon~241!!!GM~Gambia~220!!!GE~Georgia~995!!!DE~Germany~49!!!GH~Ghana~233!!!GI~Gibraltar~350!!!GR~Greece~30!!!GL~Greenland~299!!!GD~Grenada~1!!!GP~Guadeloupe~590!!!GU~Guam~1!!!GT~Guatemala~502!!!GG~Guernsey~44!!!GN~Guinea~224!!!GW~Guinea-Bissau~245!!!GY~Guyana~592!!!HT~Haiti~509!!!HN~Honduras~504!!!HK~Hong Kong SAR~852!!!HU~Hungary~36!!!IS~Iceland~354!!!IN~India~91!!!ID~Indonesia~62!!!IR~Iran~98!!!IQ~Iraq~964!!!IE~Ireland~353!!!IM~Isle of Man~44!!!IL~Israel~972!!!IT~Italy~39!!!JM~Jamaica~1!!!JP~Japan~81!!!JE~Jersey~44!!!JO~Jordan~962!!!KZ~Kazakhstan~7!!!KE~Kenya~254!!!KI~Kiribati~686!!!KR~Korea~82!!!KW~Kuwait~965!!!KG~Kyrgyzstan~996!!!LA~Laos~856!!!LV~Latvia~371!!!LB~Lebanon~961!!!LS~Lesotho~266!!!LR~Liberia~231!!!LY~Libya~218!!!LI~Liechtenstein~423!!!LT~Lithuania~370!!!LU~Luxembourg~352!!!MO~Macao SAR~853!!!MG~Madagascar~261!!!MW~Malawi~265!!!MY~Malaysia~60!!!MV~Maldives~960!!!ML~Mali~223!!!MT~Malta~356!!!MH~Marshall Islands~692!!!MQ~Martinique~596!!!MR~Mauritania~222!!!MU~Mauritius~230!!!YT~Mayotte~262!!!MX~Mexico~52!!!FM~Micronesia~691!!!MD~Moldova~373!!!MC~Monaco~377!!!MN~Mongolia~976!!!ME~Montenegro~382!!!MS~Montserrat~1!!!MA~Morocco~212!!!MZ~Mozambique~258!!!MM~Myanmar~95!!!NA~Namibia~264!!!NR~Nauru~674!!!NP~Nepal~977!!!NL~Netherlands~31!!!NC~New Caledonia~687!!!NZ~New Zealand~64!!!NI~Nicaragua~505!!!NE~Niger~227!!!NG~Nigeria~234!!!NU~Niue~683!!!NF~Norfolk Island~672!!!KP~North Korea~850!!!MK~North Macedonia~389!!!MP~Northern Mariana Islands~1!!!NO~Norway~47!!!OM~Oman~968!!!PK~Pakistan~92!!!PW~Palau~680!!!PS~Palestinian Authority~970!!!PA~Panama~507!!!PG~Papua New Guinea~675!!!PY~Paraguay~595!!!PE~Peru~51!!!PH~Philippines~63!!!PL~Poland~48!!!PT~Portugal~351!!!PR~Puerto Rico~1!!!QA~Qatar~974!!!RE~Réunion~262!!!RO~Romania~40!!!RU~Russia~7!!!RW~Rwanda~250!!!BL~Saint Barthélemy~590!!!KN~Saint Kitts and Nevis~1!!!LC~Saint Lucia~1!!!MF~Saint Martin~590!!!PM~Saint Pierre and Miquelon~508!!!VC~Saint Vincent and the Grenadines~1!!!WS~Samoa~685!!!SM~San Marino~378!!!ST~São Tomé and Príncipe~239!!!SA~Saudi Arabia~966!!!SN~Senegal~221!!!RS~Serbia~381!!!SC~Seychelles~248!!!SL~Sierra Leone~232!!!SG~Singapore~65!!!SX~Sint Maarten~1!!!SK~Slovakia~421!!!SI~Slovenia~386!!!SB~Solomon Islands~677!!!SO~Somalia~252!!!ZA~South Africa~27!!!SS~South Sudan~211!!!ES~Spain~34!!!LK~Sri Lanka~94!!!SH~St Helena, Ascension, and Tristan da Cunha~290!!!SD~Sudan~249!!!SR~Suriname~597!!!SJ~Svalbard~47!!!SZ~Swaziland~268!!!SE~Sweden~46!!!CH~Switzerland~41!!!SY~Syria~963!!!TW~Taiwan~886!!!TJ~Tajikistan~992!!!TZ~Tanzania~255!!!TH~Thailand~66!!!TL~Timor-Leste~670!!!TG~Togo~228!!!TK~Tokelau~690!!!TO~Tonga~676!!!TT~Trinidad and Tobago~1!!!TA~Tristan da Cunha~290!!!TN~Tunisia~216!!!TR~Turkey~90!!!TM~Turkmenistan~993!!!TC~Turks and Caicos Islands~1!!!TV~Tuvalu~688!!!VI~U.S. Virgin Islands~1!!!UG~Uganda~256!!!UA~Ukraine~380!!!AE~United Arab Emirates~971!!!GB~United Kingdom~44!!!US~United States~1!!!UY~Uruguay~598!!!UZ~Uzbekistan~998!!!VU~Vanuatu~678!!!VA~Vatican City~39!!!VE~Venezuela~58!!!VN~Vietnam~84!!!WF~Wallis and Futuna~681!!!YE~Yemen~967!!!ZM~Zambia~260!!!ZW~Zimbabwe~263","fUseInlinePhoneNumber":true,"fDetectBrowserCapabilities":true,"fUseMinHeight":true,"fShouldSupportTargetCredentialForRecovery":true,"fAvoidNewOtcGenerationWhenAlreadySent":true,"fUsePromotedFedCredTypesArray":true,"fUseCertificateInterstitialView":true,"fIsPasskeySupportEnabled":true,"arrPromotedFedCredTypes":[],"fShowUserAlreadyExistErrorHandling":true,"fBlockOnAppleEmailClaimError":true,"fIsVerifiableCredentialsSupportEnabled":true,"iVerifiableCredentialPresentationPollingIntervalSeconds":0.5,"iVerifiableCredentialPresentationPollingTimeoutSeconds":300,"fIsQrPinEnabled":true,"fPasskeyAssertionRedirect":true,"fSamlAndOidcCompatibleUx":true,"fFixUrlExternalIdpFederation":true,"fUpdateFacebookIcon":true,"fEnableBackButtonBugFix":true,"fEnableTotalLossRecovery":true,"urlSessionState":"https://login.microsoftonline.com/common/DeviceCodeStatus","urlResetPassword":"https://passwordreset.microsoftonline.com/?ru=https%3a%2f%2flogin.microsoftonline.com%2fdeff24bb-2089-4400-8c8e-f71e680378b2%2freprocess%3fctx%3drQQIARAAlVE7bNNQFLXjNDShpWknRmR1ocLxe_az_RypEv7EbcqnCAmVj1Dkz3Oaxo7d2An9qAssMCAqRiQGECyZEBPqUsTYKRNDWRirSiAEEsqCIBELYzm6OjrD1dU5584xsATLs-AvRG7EHPB9yLlkpP5Be6ZQfPF6-tHHi1Q_I_3c_3Dc3e_R_GqaxkmZ55M44upB5NhBqRmH9ZIbhXzNj9phwnvEtztBWrKTeOMdTfdp-oime5n1CgBQMRCsWAKwTANZmqSaWBdMRUWaZagKkmQADdOQBVWQJYNTVVWqQNPQK4ZuybIsmgghBagyAJqGRGDJEtQtaABgahhhJKGKinQNaqACVWgqh5mpZa2TrgojitqNLfI9kx95rMVRkj5jsssmuNdjTlTHW2Z2mMsXkONwAsAqh9BwA7uYcL4CiYyBqGBHOGByUUxaDa-fpY-zE4Apj48XitRZ6hw1yNIvx4alEn_71YNBdfH5FeHHk6fnqYMxflMlrbXrl5YuN2XeII2Vpq7fat52E01ak2Ot6my2WmsrmzelJac-L5fhbo7ezeX2cvlxpkixjHENfsvRD09Re_n_fU__NH04AQt5N3LadstreDOzEDoeBljksEIAh6AjcY6jYA44LhJdbGOC7cMJVMi5gd0Ik5m5bbbh1dKoSVpseZvdCJOa645U1w46JGHLd9ihQfbuzs7O_ckTXX8_SQ3OfOl9evzr99HXxeOpCx0gEueqHQRdLFlyvO4kq65Xtf2uciOsLtxStjbqoZQuEH6rOf-mSH0ezjQ1mN7N_AE1\u0026mkt=en-US\u0026hosted=0\u0026device_platform=Windows+10","urlMsaResetPassword":"https://account.live.com/password/reset?wreply=https%3a%2f%2flogin.microsoftonline.com%2fdeff24bb-2089-4400-8c8e-f71e680378b2%2freprocess%3fctx%3drQQIARAAlVE7bNNQFLXjNDShpWknRmR1ocLxe_az_RypEv7EbcqnCAmVj1Dkz3Oaxo7d2An9qAssMCAqRiQGECyZEBPqUsTYKRNDWRirSiAEEsqCIBELYzm6OjrD1dU5584xsATLs-AvRG7EHPB9yLlkpP5Be6ZQfPF6-tHHi1Q_I_3c_3Dc3e_R_GqaxkmZ55M44upB5NhBqRmH9ZIbhXzNj9phwnvEtztBWrKTeOMdTfdp-oime5n1CgBQMRCsWAKwTANZmqSaWBdMRUWaZagKkmQADdOQBVWQJYNTVVWqQNPQK4ZuybIsmgghBagyAJqGRGDJEtQtaABgahhhJKGKinQNaqACVWgqh5mpZa2TrgojitqNLfI9kx95rMVRkj5jsssmuNdjTlTHW2Z2mMsXkONwAsAqh9BwA7uYcL4CiYyBqGBHOGByUUxaDa-fpY-zE4Apj48XitRZ6hw1yNIvx4alEn_71YNBdfH5FeHHk6fnqYMxflMlrbXrl5YuN2XeII2Vpq7fat52E01ak2Ot6my2WmsrmzelJac-L5fhbo7ezeX2cvlxpkixjHENfsvRD09Re_n_fU__NH04AQt5N3LadstreDOzEDoeBljksEIAh6AjcY6jYA44LhJdbGOC7cMJVMi5gd0Ik5m5bbbh1dKoSVpseZvdCJOa645U1w46JGHLd9ihQfbuzs7O_ckTXX8_SQ3OfOl9evzr99HXxeOpCx0gEueqHQRdLFlyvO4kq65Xtf2uciOsLtxStjbqoZQuEH6rOf-mSH0ezjQ1mN7N_AE1\u0026mkt=en-US","fFixUrlResetPassword":true,"urlGetCredentialType":"https://login.microsoftonline.com/common/GetCredentialType?mkt=en-US","urlGetRecoveryCredentialType":"https://login.microsoftonline.com/common/getrecoverycredentialtype?mkt=en-US","urlGetOneTimeCode":"https://login.microsoftonline.com/common/GetOneTimeCode","urlLogout":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/uxlogout","urlForget":"https://login.microsoftonline.com/forgetuser","urlDisambigRename":"https://go.microsoft.com/fwlink/p/?LinkID=733247","urlGoToAADError":"https://login.live.com/oauth20_authorize.srf?scope=openid+profile+email+offline_access\u0026response_type=code\u0026client_id=********-085c-4d86-bf88-cf50c7252078\u0026response_mode=form_post\u0026redirect_uri=https%3a%2f%2flogin.microsoftonline.com%2fcommon%2ffederation%2foauth2msa\u0026state=rQQIARAAlVE7bNNQFLXj1DQhpWknRmR1ocLxe_az_RypEo4dtymfIiRUPkKRP89pGjt24yT0oy6wwICoGCsxgGDJVDGhLkWMnTIxlIWxqgRCIKEsCBKxMJajq6Mj3aurc8-dZWABFmfAX0j8iHng-5B3yUj9g9Z0Nv_yzdSTj5epfkr-efDhpHuwS2eqQb1LCm4U9mhhpd2Ok6IgJHHE14LIsYNCIw5ro65Q9aNWmAge8e1O0C7YSbz-jqb7NH1M073UWhkAqBoIli0RWKaBLF3WTFwSTVVDumVoKpIVAA3TUERNVGSD1zRNLkPTKJWNkqUoimQihFSgKQDoOpKApciwZEEDAFPHCCMZlTVU0qEOylCDpnqUmlzSO-0VcURRq75JvqcyI4_VOErau0x6yQQPesyponnLzAzv8kXkOLwIsMYjNJzALia8r0KiYCCp2BEPGTaKSbPu9dP0SToHmOL4eDZPnacuUIM0_WpsGDDxt14_GlQWXlwTfzx7fpE6HBM2NNJcvXll8WpDEQxSX26USncad91El1eVWK84G83m6vLGbXnRqc0pRbjD0jssu89mxpk8xTHGDfiNpR-fofYz__ue_ll6L0cf5WA240ZOy256dW96BkLHwwBLPFYJ4BF0ZN5xVMwDx0WSi21MsH2UQ1nWDex6mEzPbnF1r9qOGqTJFbe49TCpuu5Ide2gQxKueI8beuTub29vP5w41fb3E9Tg3Jfep6e_fh9_XTiZvNQBEnGu20HQxbKlxGtOsuJ6FdvvqrfCyvwddXO9FsrteSJsNub28tTnYU1Rg6md1B81\u0026estsfed=1\u0026uaid=8911a5a140d6d0000235f6bfc2ea76bf\u0026cobrandid=11bd8083-87e0-41b5-bb78-0bc43c8a8e8a\u0026fci=********-0000-0ff1-ce00-************\u0026wsucxt=1\u0026skipkmsi=1","urlDeviceFingerprinting":"","urlPIAEndAuth":"https://login.microsoftonline.com/common/PIA/EndAuth","urlStartTlr":"https://login.microsoftonline.com/common/tlr/start#continuationToken=%7b0%7d\u0026undirectedRecoveryUrl=%7b1%7d","fKMSIEnabled":false,"iLoginMode":1,"fAllowPhoneSignIn":true,"fAllowPhoneInput":true,"fAllowSkypeNameLogin":true,"iMaxPollErrors":5,"iPollingTimeout":120,"srsSuccess":true,"fShowSwitchUser":true,"arrValErrs":["50058"],"sErrorCode":"50058","sWAMExtension":"ppnbnpeolgkicgegkbkbjmhlideopiji","sWAMChannel":"53ee284d-920a-4b59-9d30-a60315b26836","sErrTxt":"","sResetPasswordPrefillParam":"username","onPremPasswordValidationConfig":{"isUserRealmPrecheckEnabled":true},"fSwitchDisambig":true,"oCancelPostParams":{"error":"access_denied","error_subcode":"cancel","state":"OD0w","canary":"y9enjRKJLk6/CeiWkBBYkZcsA5j6pAIbynnjWyX5Jbg=6:1:CANARY:u03ebNallv85F6pqbshcdIafv7UmIGY7zxgm5tGe/zk="},"iRemoteNgcPollingType":2,"fUseNewNoPasswordTypes":true,"urlAadSignup":"https://signup.microsoft.com/signup?sku=teams_commercial_trial\u0026origin=ests\u0026culture=en-US","urlTenantedEndpointFormat":"https://login.microsoftonline.com/{0}/oauth2/authorize?client_id=********-0000-0ff1-ce00-************\u0026response_mode=form_post\u0026response_type=code+id_token\u0026resource=********-0000-0ff1-ce00-************\u0026scope=openid\u0026nonce=E0017C41EF20FDC4FA59D8B2D794AFC9745601CDC629265C-9995E1DCBECBF6663D444709600AA430F651BF1C00DA848454E94BA1A0E191D7\u0026redirect_uri=https%3a%2f%2fspo-global.kpmg.com%2f_forms%2fdefault.aspx\u0026state=OD0w\u0026claims=%7b%22id_token%22%3a%7b%22xms_cc%22%3a%7b%22values%22%3a%5b%22CP1%22%5d%7d%7d%7d\u0026wsucxt=1\u0026cobrandid=11bd8083-87e0-41b5-bb78-0bc43c8a8e8a\u0026client-request-id=8911a5a1-40d6-d000-0235-f6bfc2ea76bf\u0026allowbacktocommon=True","sCloudInstanceName":"microsoftonline.com","fShowSignInOptionsAsButton":true,"fUseNewPhoneSignInError":true,"fImprovePhoneDisambig":true,"fIsUpdatedAutocompleteEnabled":true,"fActivateFocusOnApprovalNumberRemoteNGC":true,"fIsPasskey":true,"fEnableDFPIntegration":true,"fEnableCenterFocusedApprovalNumber":true,"fShowPassKeyErrorUCP":true,"fFixPhoneDisambigSignupRedirect":true,"fEnableFIDOBluetoothError":true,"fEnableQrCodeA11YFixes":true,"fEnablePasskeyAwpError":true,"fEnableAuthenticatorTimeoutFix":true,"iMaxStackForKnockoutAsyncComponents":10000,"fShowButtons":true,"urlCdn":"https://aadcdn.msftauth.net/shared/1.0/","urlDefaultFavicon":"https://aadcdn.msftauth.net/shared/1.0/content/images/favicon_a_eupayfgghqiai7k9sol6lg2.ico","urlFooterTOU":"https://www.microsoft.com/en-US/servicesagreement/","urlFooterPrivacy":"https://privacy.microsoft.com/en-US/privacystatement","urlPost":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/login","urlPostAad":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/login","urlPostMsa":"https://login.live.com/ppsecure/partnerpost.srf?scope=openid+profile+email+offline_access\u0026response_type=code\u0026client_id=********-085c-4d86-bf88-cf50c7252078\u0026response_mode=form_post\u0026redirect_uri=https%3a%2f%2flogin.microsoftonline.com%2fcommon%2ffederation%2foauth2msa\u0026state=rQQIARAAlVE7bNNQFLXj1DQhpWknRmR1ocLxe_az_RypEo4dtymfIiRUPkKRP89pGjt24yT0oy6wwICoGCsxgGDJVDGhLkWMnTIxlIWxqgRCIKEsCBKxMJajq6Mj3aurc8-dZWABFmfAX0j8iHng-5B3yUj9g9Z0Nv_yzdSTj5epfkr-efDhpHuwS2eqQb1LCm4U9mhhpd2Ok6IgJHHE14LIsYNCIw5ro65Q9aNWmAge8e1O0C7YSbz-jqb7NH1M073UWhkAqBoIli0RWKaBLF3WTFwSTVVDumVoKpIVAA3TUERNVGSD1zRNLkPTKJWNkqUoimQihFSgKQDoOpKApciwZEEDAFPHCCMZlTVU0qEOylCDpnqUmlzSO-0VcURRq75JvqcyI4_VOErau0x6yQQPesyponnLzAzv8kXkOLwIsMYjNJzALia8r0KiYCCp2BEPGTaKSbPu9dP0SToHmOL4eDZPnacuUIM0_WpsGDDxt14_GlQWXlwTfzx7fpE6HBM2NNJcvXll8WpDEQxSX26USncad91El1eVWK84G83m6vLGbXnRqc0pRbjD0jssu89mxpk8xTHGDfiNpR-fofYz__ue_ll6L0cf5WA240ZOy256dW96BkLHwwBLPFYJ4BF0ZN5xVMwDx0WSi21MsH2UQ1nWDex6mEzPbnF1r9qOGqTJFbe49TCpuu5Ide2gQxKueI8beuTub29vP5w41fb3E9Tg3Jfep6e_fh9_XTiZvNQBEnGu20HQxbKlxGtOsuJ6FdvvqrfCyvwddXO9FsrteSJsNub28tTnYU1Rg6md1B81\u0026flow=fido\u0026estsfed=1\u0026uaid=8911a5a140d6d0000235f6bfc2ea76bf\u0026cobrandid=11bd8083-87e0-41b5-bb78-0bc43c8a8e8a\u0026fci=********-0000-0ff1-ce00-************\u0026wsucxt=1\u0026skipkmsi=1","urlRefresh":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/reprocess?ctx=rQQIARAAlVE7bNNQFLXjNDShpWknRmR1ocLxe_az_RypEv7EbcqnCAmVj1Dkz3Oaxo7d2An9qAssMCAqRiQGECyZEBPqUsTYKRNDWRirSiAEEsqCIBELYzm6OjrD1dU5584xsATLs-AvRG7EHPB9yLlkpP5Be6ZQfPF6-tHHi1Q_I_3c_3Dc3e_R_GqaxkmZ55M44upB5NhBqRmH9ZIbhXzNj9phwnvEtztBWrKTeOMdTfdp-oime5n1CgBQMRCsWAKwTANZmqSaWBdMRUWaZagKkmQADdOQBVWQJYNTVVWqQNPQK4ZuybIsmgghBagyAJqGRGDJEtQtaABgahhhJKGKinQNaqACVWgqh5mpZa2TrgojitqNLfI9kx95rMVRkj5jsssmuNdjTlTHW2Z2mMsXkONwAsAqh9BwA7uYcL4CiYyBqGBHOGByUUxaDa-fpY-zE4Apj48XitRZ6hw1yNIvx4alEn_71YNBdfH5FeHHk6fnqYMxflMlrbXrl5YuN2XeII2Vpq7fat52E01ak2Ot6my2WmsrmzelJac-L5fhbo7ezeX2cvlxpkixjHENfsvRD09Re_n_fU__NH04AQt5N3LadstreDOzEDoeBljksEIAh6AjcY6jYA44LhJdbGOC7cMJVMi5gd0Ik5m5bbbh1dKoSVpseZvdCJOa645U1w46JGHLd9ihQfbuzs7O_ckTXX8_SQ3OfOl9evzr99HXxeOpCx0gEueqHQRdLFlyvO4kq65Xtf2uciOsLtxStjbqoZQuEH6rOf-mSH0ezjQ1mN7N_AE1","urlCancel":"https://spo-global.kpmg.com/_forms/default.aspx","urlResume":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/resume?ctx=rQQIARAAlVE7bNNQFLXjNDShpWknRmR1ocLxe_az_RypEv7EbcqnCAmVj1Dkz3Oaxo7d2An9qAssMCAqRiQGECyZEBPqUsTYKRNDWRirSiAEEsqCIBELYzm6OjrD1dU5584xsATLs-AvRG7EHPB9yLlkpP5Be6ZQfPF6-tHHi1Q_I_3c_3Dc3e_R_GqaxkmZ55M44upB5NhBqRmH9ZIbhXzNj9phwnvEtztBWrKTeOMdTfdp-oime5n1CgBQMRCsWAKwTANZmqSaWBdMRUWaZagKkmQADdOQBVWQJYNTVVWqQNPQK4ZuybIsmgghBagyAJqGRGDJEtQtaABgahhhJKGKinQNaqACVWgqh5mpZa2TrgojitqNLfI9kx95rMVRkj5jsssmuNdjTlTHW2Z2mMsXkONwAsAqh9BwA7uYcL4CiYyBqGBHOGByUUxaDa-fpY-zE4Apj48XitRZ6hw1yNIvx4alEn_71YNBdfH5FeHHk6fnqYMxflMlrbXrl5YuN2XeII2Vpq7fat52E01ak2Ot6my2WmsrmzelJac-L5fhbo7ezeX2cvlxpkixjHENfsvRD09Re_n_fU__NH04AQt5N3LadstreDOzEDoeBljksEIAh6AjcY6jYA44LhJdbGOC7cMJVMi5gd0Ik5m5bbbh1dKoSVpseZvdCJOa645U1w46JGHLd9ihQfbuzs7O_ckTXX8_SQ3OfOl9evzr99HXxeOpCx0gEueqHQRdLFlyvO4kq65Xtf2uciOsLtxStjbqoZQuEH6rOf-mSH0ezjQ1mN7N_AE1","iPawnIcon":0,"iPollingInterval":1,"sPOST_Username":"","sFT":"AQABIQEAAABVrSpeuWamRam2jAF1XRQE8Bh_qCKzAHUP0u67KmCmcpDGzNbfk1sLZZMY_CzzWkmMYCv1IG0SPcGLziAuFBZoPHGjq8zIZRb6J0-MMmjWi8qXc1_nPggF-OR42qNC5JXaE8CN4RqO-QhLYvuHtO77sZsECEGSWruOMwMORT0_7DG5pF6dN7X_7-VLIHvumLdxgx_w8MjzJV14FUpSGLYYSidTqlC5aQZ3slBZUUjJHtC0gMFYhPCIyQOyyomerPyr10pRmLrSdiAd_IxgF2QZz-gYbdeuWjtT5cEYraS6ngUq6Nnun-f1CVE5-5lGTTOdbR5oJY7DMld_mkjnZonyrZLPcuX-2qHQGD2fSIyvj9p6VOc_T5enCHRbKpcR7lXMClNtpoDS-goiNGvDuEBS3J2ljQ4nwMX0lot4BdJeQiB6ttc4HL95Hb4_AGBM_PU8X-gSpPCEjRih4b3dBKXWat1bAFlbKTfuYC40Pa6pMCZvePMD6xhYVPtP8_0H7rEqillQI5nh7Y1yVDLz8P2XIAA","sFTName":"flowToken","sFTCookieName":"ESTSWCTXFLOWTOKEN","sSessionIdentifierName":"code","sCtx":"rQQIARAAlVE7bNNQFLXjNDShpWknRmR1ocLxe_az_RypEv7EbcqnCAmVj1Dkz3Oaxo7d2An9qAssMCAqRiQGECyZEBPqUsTYKRNDWRirSiAEEsqCIBELYzm6OjrD1dU5584xsATLs-AvRG7EHPB9yLlkpP5Be6ZQfPF6-tHHi1Q_I_3c_3Dc3e_R_GqaxkmZ55M44upB5NhBqRmH9ZIbhXzNj9phwnvEtztBWrKTeOMdTfdp-oime5n1CgBQMRCsWAKwTANZmqSaWBdMRUWaZagKkmQADdOQBVWQJYNTVVWqQNPQK4ZuybIsmgghBagyAJqGRGDJEtQtaABgahhhJKGKinQNaqACVWgqh5mpZa2TrgojitqNLfI9kx95rMVRkj5jsssmuNdjTlTHW2Z2mMsXkONwAsAqh9BwA7uYcL4CiYyBqGBHOGByUUxaDa-fpY-zE4Apj48XitRZ6hw1yNIvx4alEn_71YNBdfH5FeHHk6fnqYMxflMlrbXrl5YuN2XeII2Vpq7fat52E01ak2Ot6my2WmsrmzelJac-L5fhbo7ezeX2cvlxpkixjHENfsvRD09Re_n_fU__NH04AQt5N3LadstreDOzEDoeBljksEIAh6AjcY6jYA44LhJdbGOC7cMJVMi5gd0Ik5m5bbbh1dKoSVpseZvdCJOa645U1w46JGHLd9ihQfbuzs7O_ckTXX8_SQ3OfOl9evzr99HXxeOpCx0gEueqHQRdLFlyvO4kq65Xtf2uciOsLtxStjbqoZQuEH6rOf-mSH0ezjQ1mN7N_AE1","iProductIcon":-1,"fEnableOneDSClientTelemetry":true,"urlReportPageLoad":"https://login.microsoftonline.com/common/instrumentation/reportpageload?mkt=en-US","staticTenantBranding":[{"Locale":0,"BannerLogo":"https://aadcdn.msftauthimages.net/c1c6b6c8-mwlevwhpqhztl4daar5f31lrkbmawuowudsskp3-s8c/logintenantbranding/0/bannerlogo?ts=636344395095152708","Illustration":"https://aadcdn.msftauthimages.net/c1c6b6c8-mwlevwhpqhztl4daar5f31lrkbmawuowudsskp3-s8c/logintenantbranding/0/illustration?ts=636344395043619803","BackgroundColor":"#FFFFFF","BoilerPlateText":"<p>Access to this service is restricted to authorized users only. Unauthorized use may subject you to prosecution or other legal actions. Activity by any user of this system may be monitored.</p>\n<p><em>For more information about KPMG’s privacy policies and practices, please visit: <a href=\"https://landing.kpmg.com/privacy.html\" rel=\"noopener noreferrer\" target=\"_blank\">KPMG Privacy</a></em></p>\n","UserIdLabel":"Sign in with your KPMG email address","KeepMeSignedInDisabled":true,"UseTransparentLightBox":false}],"oAppCobranding":{},"iBackgroundImage":2,"arrSessions":[],"fApplicationInsightsEnabled":false,"iApplicationInsightsEnabledPercentage":0,"urlSetDebugMode":"https://login.microsoftonline.com/common/debugmode","fEnableCssAnimation":true,"fAllowGrayOutLightBox":true,"fUseMsaSessionState":true,"fIsRemoteNGCSupported":true,"desktopSsoConfig":{"isEdgeAnaheimAllowed":true,"iwaEndpointUrlFormat":"https://autologon.microsoftazuread-sso.com/{0}/winauth/sso?client-request-id=8911a5a1-40d6-d000-0235-f6bfc2ea76bf","iwaSsoProbeUrlFormat":"https://autologon.microsoftazuread-sso.com/{0}/winauth/ssoprobe?client-request-id=8911a5a1-40d6-d000-0235-f6bfc2ea76bf","iwaIFrameUrlFormat":"https://autologon.microsoftazuread-sso.com/{0}/winauth/iframe?client-request-id=8911a5a1-40d6-d000-0235-f6bfc2ea76bf\u0026isAdalRequest=False","iwaRequestTimeoutInMs":10000,"startDesktopSsoOnPageLoad":false,"progressAnimationTimeout":10000,"isEdgeAllowed":false,"minDssoEdgeVersion":"17","isSafariAllowed":true,"redirectUri":"https://spo-global.kpmg.com/_forms/default.aspx","redirectDssoErrorPostParams":{"error":"interaction_required","error_description":"Seamless single sign on failed for the user. This can happen if the user is unable to access on premises AD or intranet zone is not configured correctly Trace ID: a47b6665-f883-4849-9d4d-32f38f922900 Correlation ID: 8911a5a1-40d6-d000-0235-f6bfc2ea76bf Timestamp: 2025-06-05 07:08:48Z","state":"OD0w","canary":"y9enjRKJLk6/CeiWkBBYkZcsA5j6pAIbynnjWyX5Jbg=6:1:CANARY:u03ebNallv85F6pqbshcdIafv7UmIGY7zxgm5tGe/zk="},"isIEAllowedForSsoProbe":true,"edgeRedirectUri":"https://autologon.microsoftazuread-sso.com/deff24bb-2089-4400-8c8e-f71e680378b2/winauth/sso/edgeredirect?client-request-id=8911a5a1-40d6-d000-0235-f6bfc2ea76bf\u0026origin=login.microsoftonline.com\u0026is_redirected=1","isFlowTokenPassedInEdge":true},"urlLogin":"https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/reprocess?ctx=rQQIARAAlVE7bNNQFLXjNDShpWknRmR1ocLxe_az_RypEv7EbcqnCAmVj1Dkz3Oaxo7d2An9qAssMCAqRiQGECyZEBPqUsTYKRNDWRirSiAEEsqCIBELYzm6OjrD1dU5584xsATLs-AvRG7EHPB9yLlkpP5Be6ZQfPF6-tHHi1Q_I_3c_3Dc3e_R_GqaxkmZ55M44upB5NhBqRmH9ZIbhXzNj9phwnvEtztBWrKTeOMdTfdp-oime5n1CgBQMRCsWAKwTANZmqSaWBdMRUWaZagKkmQADdOQBVWQJYNTVVWqQNPQK4ZuybIsmgghBagyAJqGRGDJEtQtaABgahhhJKGKinQNaqACVWgqh5mpZa2TrgojitqNLfI9kx95rMVRkj5jsssmuNdjTlTHW2Z2mMsXkONwAsAqh9BwA7uYcL4CiYyBqGBHOGByUUxaDa-fpY-zE4Apj48XitRZ6hw1yNIvx4alEn_71YNBdfH5FeHHk6fnqYMxflMlrbXrl5YuN2XeII2Vpq7fat52E01ak2Ot6my2WmsrmzelJac-L5fhbo7ezeX2cvlxpkixjHENfsvRD09Re_n_fU__NH04AQt5N3LadstreDOzEDoeBljksEIAh6AjcY6jYA44LhJdbGOC7cMJVMi5gd0Ik5m5bbbh1dKoSVpseZvdCJOa645U1w46JGHLd9ihQfbuzs7O_ckTXX8_SQ3OfOl9evzr99HXxeOpCx0gEueqHQRdLFlyvO4kq65Xtf2uciOsLtxStjbqoZQuEH6rOf-mSH0ezjQ1mN7N_AE1","urlDssoStatus":"https://login.microsoftonline.com/common/instrumentation/dssostatus","iSessionPullType":2,"fUseSameSite":true,"iAllowedIdentities":2,"uiflavor":1001,"urlFidoHelp":"https://go.microsoft.com/fwlink/?linkid=2013738","urlFidoLogin":"https://login.microsoft.com/deff24bb-2089-4400-8c8e-f71e680378b2/fido/get?uiflavor=Web","fIsFidoSupported":true,"fLoadStringCustomizationPromises":true,"fOfflineAccountVisible":false,"fEnableUserStateFix":true,"fAccessPassSupported":true,"fShowAccessPassPeek":true,"fUpdateSessionPollingLogic":true,"fEnableShowPickerCredObservable":true,"fFetchSessionsSkipDsso":true,"fIsCiamUserFlowUxNewLogicEnabled":true,"fUseNonMicrosoftDefaultBrandingForCiam":true,"sCompanyDisplayName":"KPMG","fRemoveCustomCss":true,"fFixUICrashForApiRequestHandler":true,"fShowUpdatedKoreanPrivacyFooter":true,"fUsePostCssHotfix":true,"fUseHighContrastDetectionMode":true,"fFixUserFlowBranding":true,"fIsQrCodePinSupported":true,"fEnablePasskeyNullFix":true,"urlAcmaServerPath":"https://login.microsoftonline.com","sTenantId":"deff24bb-2089-4400-8c8e-f71e680378b2","sMkt":"en-US","scid":1013,"hpgact":1800,"hpgid":1104,"pgid":"ConvergedSignIn","apiCanary":"PAQABDgEAAABVrSpeuWamRam2jAF1XRQEc9zJMGNPR4vENqcyjDGH5XvJPsVXZb0GocURjUOaeXOm3UIy1SRTkWoc6FiE6gh9frHAkItyNzyiIEQ8PShlvAHarUMgXeMp2M_PdZDm_m5aPEiVo0BaBvLzLuBzm5xu1CPOKPWmDS9tKEloWZyJSSDEccSaoHmkjAzf-aDg4cL4STKTX_LCPec78HZXoUdhdMf4jU7GgAeDT2WUOHOcLiAA","canary":"y9enjRKJLk6/CeiWkBBYkZcsA5j6pAIbynnjWyX5Jbg=6:1:CANARY:u03ebNallv85F6pqbshcdIafv7UmIGY7zxgm5tGe/zk=","sCanaryTokenName":"canary","fSkipRenderingNewCanaryToken":false,"fEnableNewCsrfProtection":true,"correlationId":"8911a5a1-40d6-d000-0235-f6bfc2ea76bf","sessionId":"a47b6665-f883-4849-9d4d-32f38f922900","sRingId":"R6","locale":{"mkt":"en-US","lcid":1033},"slMaxRetry":2,"slReportFailure":true,"strings":{"desktopsso":{"authenticatingmessage":"Trying to sign you in"}},"enums":{"ClientMetricsModes":{"None":0,"SubmitOnPost":1,"SubmitOnRedirect":2,"InstrumentPlt":4}},"urls":{"instr":{"pageload":"https://login.microsoftonline.com/common/instrumentation/reportpageload","dssostatus":"https://login.microsoftonline.com/common/instrumentation/dssostatus"}},"browser":{"ltr":1,"Edge":1,"_Win":1,"_M137":1,"_D0":1,"Full":1,"Win81":1,"RE_WebKit":1,"b":{"name":"Edge","major":137,"minor":0},"os":{"name":"Windows","version":"10.0"},"V":"137.0"},"watson":{"url":"/common/handlers/watson","bundle":"https://aadcdn.msftauth.net/ests/2.1/content/cdnbundles/watson.min_q5ptmu8aniymd4ftuqdkda2.js","sbundle":"https://aadcdn.msftauth.net/ests/2.1/content/cdnbundles/watsonsupportwithjquery.3.5.min_dc940oomzau4rsu8qesnvg2.js","fbundle":"https://aadcdn.msftauth.net/ests/2.1/content/cdnbundles/frameworksupport.min_oadrnc13magb009k4d20lg2.js","resetErrorPeriod":5,"maxCorsErrors":-1,"maxInjectErrors":5,"maxErrors":10,"maxTotalErrors":3,"expSrcs":["https://login.microsoftonline.com","https://aadcdn.msauth.net/","https://aadcdn.msftauth.net/",".login.microsoftonline.com"],"envErrorRedirect":true,"envErrorUrl":"/common/handlers/enverror"},"loader":{"cdnRoots":["https://aadcdn.msauth.net/","https://aadcdn.msftauth.net/"],"logByThrowing":true,"tenantBrandingCdnRoots":["aadcdn.msauthimages.net","aadcdn.msftauthimages.net"]},"serverDetails":{"slc":"ProdSlices","dc":"NEULR1","ri":"DU2XXXX","ver":{"v":[2,1,20899,4]},"rt":"2025-06-05T07:08:48","et":25},"clientEvents":{"enabled":true,"telemetryEnabled":true,"useOneDSEventApi":true,"flush":60000,"autoPost":true,"autoPostDelay":1000,"minEvents":1,"maxEvents":1,"pltDelay":500,"appInsightsConfig":{"instrumentationKey":"b0c252808e614e949086e019ae1cb300-e0c02060-e3b3-4965-bd7c-415e1a7a9fde-6951","webAnalyticsConfiguration":{"autoCapture":{"jsError":true}}},"defaultEventName":"IDUX_ESTSClientTelemetryEvent_WebWatson","serviceID":3,"endpointUrl":"https://eu-mobile.events.data.microsoft.com/OneCollector/1.0/"},"fApplyAsciiRegexOnInput":true,"country":"IN","fBreakBrandingSigninString":true,"bsso":{"initiatePullTimeoutMs":100,"initiatePullTimeoutAction":"continue","rid":"a47b6665-f883-4849-9d4d-32f38f922900","states":{"START":"start","INPROGRESS":"in-progress","END":"end","END_SSO":"end-sso","END_USERS":"end-users"},"nonce":"AwABEgEAAAADAOz_BQD0_5hKku_QOb21GnrFyzBEuu-bcN1iYa6GUn_uNDBR0TJS5e0zPiuaNSVJsWoIc-6um1CSVLoIaMzWw9_OcHWk9OogAA","overallTimeoutMs":4000,"telemetry":{"type":"TBAuthTelemetry","nonce":"AwABDwEAAAADAOz_BQD0_xATEpCQ8Ou0-vKEcrqLOcN2Fk1RmmYyP_zYxDpNePukfL1iubCaN1Qq1KhbfebduLc2z4bSenxuqNr-vifCFLZ6harOct5aQBZc81So68WWIAA","reportStates":["end","end-sso","end-users"]},"redirectEndStates":["end","end-users"],"cookieNames":{"aadSso":"AADSSO","winSso":"ESTSSSO","ssoTiles":"ESTSSSOTILES","ssoPulled":"SSOCOOKIEPULLED","userList":"ESTSUSERLIST"},"enabled":true,"type":"windows","reason":"Pull is needed"},"urlNoCookies":"https://login.microsoftonline.com/cookiesdisabled","fTrimChromeBssoUrl":true,"inlineMode":5,"fShowCopyDebugDetailsLink":true,"fTenantBrandingCdnAddEventHandlers":true,"fAddTryCatchForIFrameRedirects":true};
//]]></script> 
<script type="text/javascript" nonce='glI8Yd4q81tJsJGY-JWlcQ'>//<![CDATA[
!function(){var e=window,r=e.$Debug=e.$Debug||{},t=e.$Config||{};if(!r.appendLog){var n=[],o=0;r.appendLog=function(e){var r=t.maxDebugLog||25,i=(new Date).toUTCString()+":"+e;n.push(o+":"+i),n.length>r&&n.shift(),o++},r.getLogs=function(){return n}}}(),function(){function e(e,r){function t(i){var a=e[i];if(i<n-1){return void(o.r[a]?t(i+1):o.when(a,function(){t(i+1)}))}r(a)}var n=e.length;t(0)}function r(e,r,i){function a(){var e=!!s.method,o=e?s.method:i[0],a=s.extraArgs||[],u=n.$WebWatson;try{
var c=t(i,!e);if(a&&a.length>0){for(var d=a.length,l=0;l<d;l++){c.push(a[l])}}o.apply(r,c)}catch(e){return void(u&&u.submitFromException&&u.submitFromException(e))}}var s=o.r&&o.r[e];return r=r||this,s&&(s.skipTimeout?a():n.setTimeout(a,0)),s}function t(e,r){return Array.prototype.slice.call(e,r?1:0)}var n=window;n.$Do||(n.$Do={"q":[],"r":[],"removeItems":[],"lock":0,"o":[]});var o=n.$Do;o.when=function(t,n){function i(e){r(e,a,s)||o.q.push({"id":e,"c":a,"a":s})}var a=0,s=[],u=1;"function"==typeof n||(a=n,
u=2);for(var c=u;c<arguments.length;c++){s.push(arguments[c])}t instanceof Array?e(t,i):i(t)},o.register=function(e,t,n){if(!o.r[e]){o.o.push(e);var i={};if(t&&(i.method=t),n&&(i.skipTimeout=n),arguments&&arguments.length>3){i.extraArgs=[];for(var a=3;a<arguments.length;a++){i.extraArgs.push(arguments[a])}}o.r[e]=i,o.lock++;try{for(var s=0;s<o.q.length;s++){var u=o.q[s];u.id==e&&r(e,u.c,u.a)&&o.removeItems.push(u)}}catch(e){throw e}finally{if(0===--o.lock){for(var c=0;c<o.removeItems.length;c++){
for(var d=o.removeItems[c],l=0;l<o.q.length;l++){if(o.q[l]===d){o.q.splice(l,1);break}}}o.removeItems=[]}}}},o.unregister=function(e){o.r[e]&&delete o.r[e]}}(),function(e,r){function t(){if(!a){if(!r.body){return void setTimeout(t)}a=!0,e.$Do.register("doc.ready",0,!0)}}function n(){if(!s){if(!r.body){return void setTimeout(n)}t(),s=!0,e.$Do.register("doc.load",0,!0),i()}}function o(e){(r.addEventListener||"load"===e.type||"complete"===r.readyState)&&t()}function i(){
r.addEventListener?(r.removeEventListener("DOMContentLoaded",o,!1),e.removeEventListener("load",n,!1)):r.attachEvent&&(r.detachEvent("onreadystatechange",o),e.detachEvent("onload",n))}var a=!1,s=!1;if("complete"===r.readyState){return void setTimeout(n)}!function(){r.addEventListener?(r.addEventListener("DOMContentLoaded",o,!1),e.addEventListener("load",n,!1)):r.attachEvent&&(r.attachEvent("onreadystatechange",o),e.attachEvent("onload",n))}()}(window,document),function(){function e(){
return f.$Config||f.ServerData||{}}function r(e,r){var t=f.$Debug;t&&t.appendLog&&(r&&(e+=" '"+(r.src||r.href||"")+"'",e+=", id:"+(r.id||""),e+=", async:"+(r.async||""),e+=", defer:"+(r.defer||"")),t.appendLog(e))}function t(){var e=f.$B;if(void 0===d){if(e){d=e.IE}else{var r=f.navigator.userAgent;d=-1!==r.indexOf("MSIE ")||-1!==r.indexOf("Trident/")}}return d}function n(){var e=f.$B;if(void 0===l){if(e){l=e.RE_Edge}else{var r=f.navigator.userAgent;l=-1!==r.indexOf("Edge")}}return l}function o(e){
var r=e.indexOf("?"),t=r>-1?r:e.length,n=e.lastIndexOf(".",t);return e.substring(n,n+v.length).toLowerCase()===v}function i(){var r=e();return(r.loader||{}).slReportFailure||r.slReportFailure||!1}function a(){return(e().loader||{}).redirectToErrorPageOnLoadFailure||!1}function s(){return(e().loader||{}).logByThrowing||!1}function u(e){if(!t()&&!n()){return!1}var r=e.src||e.href||"";if(!r){return!0}if(o(r)){var i,a,s;try{i=e.sheet,a=i&&i.cssRules,s=!1}catch(e){s=!0}if(i&&!a&&s){return!0}
if(i&&a&&0===a.length){return!0}}return!1}function c(){function t(e){g.getElementsByTagName("head")[0].appendChild(e)}function n(e,r,t,n){var u=null;return u=o(e)?i(e):"script"===n.toLowerCase()?a(e):s(e,n),r&&(u.id=r),"function"==typeof u.setAttribute&&(u.setAttribute("crossorigin","anonymous"),t&&"string"==typeof t&&u.setAttribute("integrity",t)),u}function i(e){var r=g.createElement("link");return r.rel="stylesheet",r.type="text/css",r.href=e,r}function a(e){
var r=g.createElement("script"),t=g.querySelector("script[nonce]");if(r.type="text/javascript",r.src=e,r.defer=!1,r.async=!1,t){var n=t.nonce||t.getAttribute("nonce");r.setAttribute("nonce",n)}return r}function s(e,r){var t=g.createElement(r);return t.src=e,t}function d(e,r){if(e&&e.length>0&&r){for(var t=0;t<e.length;t++){if(-1!==r.indexOf(e[t])){return!0}}}return!1}function l(r){if(e().fTenantBrandingCdnAddEventHandlers){var t=d(E,r)?E:b;if(!(t&&t.length>1)){return r}for(var n=0;n<t.length;n++){
if(-1!==r.indexOf(t[n])){var o=t[n+1<t.length?n+1:0],i=r.substring(t[n].length);return"https://"!==t[n].substring(0,"https://".length)&&(o="https://"+o,i=i.substring("https://".length)),o+i}}return r}if(!(b&&b.length>1)){return r}for(var a=0;a<b.length;a++){if(0===r.indexOf(b[a])){return b[a+1<b.length?a+1:0]+r.substring(b[a].length)}}return r}function f(e,t,n,o){if(r("[$Loader]: "+(L.failMessage||"Failed"),o),w[e].retry<y){return w[e].retry++,h(e,t,n),void c._ReportFailure(w[e].retry,w[e].srcPath)}n&&n()}
function v(e,t,n,o){if(u(o)){return f(e,t,n,o)}r("[$Loader]: "+(L.successMessage||"Loaded"),o),h(e+1,t,n);var i=w[e].onSuccess;"function"==typeof i&&i(w[e].srcPath)}function h(e,o,i){if(e<w.length){var a=w[e];if(!a||!a.srcPath){return void h(e+1,o,i)}a.retry>0&&(a.srcPath=l(a.srcPath),a.origId||(a.origId=a.id),a.id=a.origId+"_Retry_"+a.retry);var s=n(a.srcPath,a.id,a.integrity,a.tagName);s.onload=function(){v(e,o,i,s)},s.onerror=function(){f(e,o,i,s)},s.onreadystatechange=function(){
"loaded"===s.readyState?setTimeout(function(){v(e,o,i,s)},500):"complete"===s.readyState&&v(e,o,i,s)},t(s),r("[$Loader]: Loading '"+(a.srcPath||"")+"', id:"+(a.id||""))}else{o&&o()}}var p=e(),y=p.slMaxRetry||2,m=p.loader||{},b=m.cdnRoots||[],E=m.tenantBrandingCdnRoots||[],L=this,w=[];L.retryOnError=!0,L.successMessage="Loaded",L.failMessage="Error",L.Add=function(e,r,t,n,o,i){e&&w.push({"srcPath":e,"id":r,"retry":n||0,"integrity":t,"tagName":o||"script","onSuccess":i})},L.AddForReload=function(e,r){
var t=e.src||e.href||"";L.Add(t,"AddForReload",e.integrity,1,e.tagName,r)},L.AddIf=function(e,r,t){e&&L.Add(r,t)},L.Load=function(e,r){h(0,e,r)}}var d,l,f=window,g=f.document,v=".css";c.On=function(e,r,t){if(!e){throw"The target element must be provided and cannot be null."}r?c.OnError(e,t):c.OnSuccess(e,t)},c.OnSuccess=function(e,t){if(!e){throw"The target element must be provided and cannot be null."}if(u(e)){return c.OnError(e,t)}var n=e.src||e.href||"",o=i(),s=a();r("[$Loader]: Loaded",e);var d=new c
;d.failMessage="Reload Failed",d.successMessage="Reload Success",d.Load(null,function(){if(o){throw"Unexpected state. ResourceLoader.Load() failed despite initial load success. ['"+n+"']"}s&&(document.location.href="/error.aspx?err=504")})},c.OnError=function(e,t){var n=e.src||e.href||"",o=i(),s=a();if(!e){throw"The target element must be provided and cannot be null."}r("[$Loader]: Failed",e);var u=new c;u.failMessage="Reload Failed",u.successMessage="Reload Success",u.AddForReload(e,t),
u.Load(null,function(){if(o){throw"Failed to load external resource ['"+n+"']"}s&&(document.location.href="/error.aspx?err=504")}),c._ReportFailure(0,n)},c._ReportFailure=function(e,r){if(s()&&!t()){throw"[Retry "+e+"] Failed to load external resource ['"+r+"'], reloading from fallback CDN endpoint"}},f.$Loader=c}(),function(){function e(){if(!E){var e=new h.$Loader;e.AddIf(!h.jQuery,y.sbundle,"WebWatson_DemandSupport"),y.sbundle=null,delete y.sbundle,e.AddIf(!h.$Api,y.fbundle,"WebWatson_DemandFramework"),
y.fbundle=null,delete y.fbundle,e.Add(y.bundle,"WebWatson_DemandLoaded"),e.Load(r,t),E=!0}}function r(){if(h.$WebWatson){if(h.$WebWatson.isProxy){return void t()}m.when("$WebWatson.full",function(){for(;b.length>0;){var e=b.shift();e&&h.$WebWatson[e.cmdName].apply(h.$WebWatson,e.args)}})}}function t(){if(!h.$WebWatson||h.$WebWatson.isProxy){if(!L&&JSON){try{var e=new XMLHttpRequest;e.open("POST",y.url),e.setRequestHeader("Accept","application/json"),
e.setRequestHeader("Content-Type","application/json; charset=UTF-8"),e.setRequestHeader("canary",p.apiCanary),e.setRequestHeader("client-request-id",p.correlationId),e.setRequestHeader("hpgid",p.hpgid||0),e.setRequestHeader("hpgact",p.hpgact||0);for(var r=-1,t=0;t<b.length;t++){if("submit"===b[t].cmdName){r=t;break}}var o=b[r]?b[r].args||[]:[],i={"sr":y.sr,"ec":"Failed to load external resource [Core Watson files]","wec":55,"idx":1,"pn":p.pgid||"","sc":p.scid||0,"hpg":p.hpgid||0,
"msg":"Failed to load external resource [Core Watson files]","url":o[1]||"","ln":0,"ad":0,"an":!1,"cs":"","sd":p.serverDetails,"ls":null,"diag":v(y)};e.send(JSON.stringify(i))}catch(e){}L=!0}y.loadErrorUrl&&window.location.assign(y.loadErrorUrl)}n()}function n(){b=[],h.$WebWatson=null}function o(r){return function(){var t=arguments;b.push({"cmdName":r,"args":t}),e()}}function i(){var e=["foundException","resetException","submit"],r=this;r.isProxy=!0;for(var t=e.length,n=0;n<t;n++){var i=e[n];i&&(r[i]=o(i))}
}function a(e,r,t,n,o,i,a){var s=h.event;return i||(i=l(o||s,a?a+2:2)),h.$Debug&&h.$Debug.appendLog&&h.$Debug.appendLog("[WebWatson]:"+(e||"")+" in "+(r||"")+" @ "+(t||"??")),$.submit(e,r,t,n,o||s,i,a)}function s(e,r){return{"signature":e,"args":r,"toString":function(){return this.signature}}}function u(e){for(var r=[],t=e.split("\n"),n=0;n<t.length;n++){r.push(s(t[n],[]))}return r}function c(e){for(var r=[],t=e.split("\n"),n=0;n<t.length;n++){var o=s(t[n],[]);t[n+1]&&(o.signature+="@"+t[n+1],n++),r.push(o)
}return r}function d(e){if(!e){return null}try{if(e.stack){return u(e.stack)}if(e.error){if(e.error.stack){return u(e.error.stack)}}else if(window.opera&&e.message){return c(e.message)}}catch(e){}return null}function l(e,r){var t=[];try{for(var n=arguments.callee;r>0;){n=n?n.caller:n,r--}for(var o=0;n&&o<w;){var i="InvalidMethod()";try{i=n.toString()}catch(e){}var a=[],u=n.args||n.arguments;if(u){for(var c=0;c<u.length;c++){a[c]=u[c]}}t.push(s(i,a)),n=n.caller,o++}}catch(e){t.push(s(e.toString(),[]))}
var l=d(e);return l&&(t.push(s("--- Error Event Stack -----------------",[])),t=t.concat(l)),t}function f(e){if(e){try{var r=/function (.{1,})\(/,t=r.exec(e.constructor.toString());return t&&t.length>1?t[1]:""}catch(e){}}return""}function g(e){if(e){try{if("string"!=typeof e&&JSON&&JSON.stringify){var r=f(e),t=JSON.stringify(e);return t&&"{}"!==t||(e.error&&(e=e.error,r=f(e)),(t=JSON.stringify(e))&&"{}"!==t||(t=e.toString())),r+":"+t}}catch(e){}}return""+(e||"")}function v(e){var r=[];try{
if(jQuery?(r.push("jQuery v:"+jQuery().jquery),jQuery.easing?r.push("jQuery.easing:"+JSON.stringify(jQuery.easing)):r.push("jQuery.easing is not defined")):r.push("jQuery is not defined"),e&&e.expectedVersion&&r.push("Expected jQuery v:"+e.expectedVersion),m){var t,n="";for(t=0;t<m.o.length;t++){n+=m.o[t]+";"}for(r.push("$Do.o["+n+"]"),n="",t=0;t<m.q.length;t++){n+=m.q[t].id+";"}r.push("$Do.q["+n+"]")}if(h.$Debug&&h.$Debug.getLogs){var o=h.$Debug.getLogs();o&&o.length>0&&(r=r.concat(o))}if(b){
for(var i=0;i<b.length;i++){var a=b[i];if(a&&"submit"===a.cmdName){try{if(JSON&&JSON.stringify){var s=JSON.stringify(a);s&&r.push(s)}}catch(e){r.push(g(e))}}}}}catch(e){r.push(g(e))}return r}var h=window,p=h.$Config||{},y=p.watson,m=h.$Do;if(!h.$WebWatson&&y){var b=[],E=!1,L=!1,w=10,$=h.$WebWatson=new i;$.CB={},$._orgErrorHandler=h.onerror,h.onerror=a,$.errorHooked=!0,m.when("jQuery.version",function(e){y.expectedVersion=e}),m.register("$WebWatson")}}(),function(){function e(e,r){
for(var t=r.split("."),n=t.length,o=0;o<n&&null!==e&&void 0!==e;){e=e[t[o++]]}return e}function r(r){var t=null;return null===u&&(u=e(i,"Constants")),null!==u&&r&&(t=e(u,r)),null===t||void 0===t?"":t.toString()}function t(t){var n=null;return null===a&&(a=e(i,"$Config.strings")),null!==a&&t&&(n=e(a,t.toLowerCase())),null!==n&&void 0!==n||(n=r(t)),null===n||void 0===n?"":n.toString()}function n(e,r){var n=null;return e&&r&&r[e]&&(n=t("errors."+r[e])),n||(n=t("errors."+e)),n||(n=t("errors."+c)),n||(n=t(c)),n}
function o(t){var n=null;return null===s&&(s=e(i,"$Config.urls")),null!==s&&t&&(n=e(s,t.toLowerCase())),null!==n&&void 0!==n||(n=r(t)),null===n||void 0===n?"":n.toString()}var i=window,a=null,s=null,u=null,c="GENERIC_ERROR";i.GetString=t,i.GetErrorString=n,i.GetUrl=o}(),function(){var e=window,r=e.$Config||{};e.$B=r.browser||{}}(),function(){function e(e,r,t){e&&e.addEventListener?e.addEventListener(r,t):e&&e.attachEvent&&e.attachEvent("on"+r,t)}function r(r,t){e(document.getElementById(r),"click",t)}
function t(r,t){var n=document.getElementsByName(r);n&&n.length>0&&e(n[0],"click",t)}var n=window;n.AddListener=e,n.ClickEventListenerById=r,n.ClickEventListenerByName=t}();
//]]></script> 
<script type="text/javascript" nonce='glI8Yd4q81tJsJGY-JWlcQ'>//<![CDATA[
!function(t,e){!function(){var n=e.getElementsByTagName("head")[0];n&&n.addEventListener&&(n.addEventListener("error",function(e){null!==e.target&&"cdn"===e.target.getAttribute("data-loader")&&t.$Loader.OnError(e.target)},!0),n.addEventListener("load",function(e){null!==e.target&&"cdn"===e.target.getAttribute("data-loader")&&t.$Loader.OnSuccess(e.target)},!0))}()}(window,document);
//]]></script>

    
        <link rel="prefetch" href="https://login.live.com/Me.htm?v=3" />
                <link rel="shortcut icon" href="https://aadcdn.msftauth.net/shared/1.0/content/images/favicon_a_eupayfgghqiai7k9sol6lg2.ico" />

    <script type="text/javascript" nonce='glI8Yd4q81tJsJGY-JWlcQ'>
        ServerData = $Config;
    </script>


    
    <link data-loader="cdn" crossorigin="anonymous" href="https://aadcdn.msftauth.net/ests/2.1/content/cdnbundles/converged.v2.login.min_81imvbluez-v5hbzpkxfcg2.css" rel="stylesheet" />


    <script data-loader="cdn" crossorigin="anonymous" src="https://aadcdn.msftauth.net/shared/1.0/content/js/ConvergedLogin_PCore_k4QdCmHtAxG2-1HsSIy8zw2.js" integrity='sha384-waXNagSCWptSSezzSOtDYvBKbVFk5DVSuX4a9uV3TeZR77sEuaAHaBzjCqT9vO+4' nonce='glI8Yd4q81tJsJGY-JWlcQ'></script>

    <script data-loader="cdn" crossorigin="anonymous" src="https://aadcdn.msftauth.net/ests/2.1/content/cdnbundles/ux.converged.login.strings-en.min_l8i1wwom7wbodda4l9b6dw2.js" nonce='glI8Yd4q81tJsJGY-JWlcQ'></script>



</head>

<body data-bind="defineGlobals: ServerData, bodyCssClass" class="cb" style="display: none">
    <script type="text/javascript" nonce='glI8Yd4q81tJsJGY-JWlcQ'>//<![CDATA[
!function(){var e=window,s=e.document,i=e.$Config||{};if(e.self===e.top){s&&s.body&&(s.body.style.display="block")}else if(!i.allowFrame){var o,t,r,f,n,d;if(i.fAddTryCatchForIFrameRedirects){try{o=e.self.location.href,t=o.indexOf("#"),r=-1!==t,f=o.indexOf("?"),n=r?t:o.length,d=-1===f||r&&f>t?"?":"&",o=o.substr(0,n)+d+"iframe-request-id="+i.sessionId+o.substr(n),e.top.location=o}catch(e){}}else{o=e.self.location.href,t=o.indexOf("#"),r=-1!==t,f=o.indexOf("?"),n=r?t:o.length,d=-1===f||r&&f>t?"?":"&",
o=o.substr(0,n)+d+"iframe-request-id="+i.sessionId+o.substr(n),e.top.location=o}}}();
//]]></script>
    
</body>
</html>