import { <PERSON><PERSON><PERSON>, Browser<PERSON>ontext, Page, chromium, firefox, webkit, devices } from '@playwright/test';
import { EnvConfig } from '../config/env.config';
import { Logger } from '../utils/logger';
import { LocatorManager } from './locator.manager';

/**
 * Browser Manager class
 * Manages browser instances, contexts, and pages
 */
export class BrowserManager {
  private static instance: BrowserManager;
  private browser: Browser | null = null;
  private context: BrowserContext | null = null;
  private page: Page | null = null;
  private logger = new Logger('BrowserManager');

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {}

  /**
   * Get the singleton instance of BrowserManager
   * @returns The BrowserManager instance
   */
  public static getInstance(): BrowserManager {
    if (!BrowserManager.instance) {
      BrowserManager.instance = new BrowserManager();
    }
    return BrowserManager.instance;
  }

  /**
   * Initialize the browser
   * @returns The initialized browser instance
   */
  public async initBrowser(): Promise<Browser> {
    if (!this.browser) {
      this.logger.info(`Initializing ${EnvConfig.BROWSER} browser`);

      try {
        // Simplified launch options to avoid conflicts
        const launchOptions = {
          headless: EnvConfig.HEADLESS,
          slowMo: EnvConfig.SLOW_MO,
          channel: EnvConfig.BROWSER_CHANNEL || undefined,
          timeout: 60000, // Increase timeout to 60 seconds
          // Private session arguments (browser-specific)
          ...(EnvConfig.PRIVATE_SESSION && {
            args: this.getPrivateSessionArgs()
          }),
          // For Edge, ensure we're using the right executable
          ...(EnvConfig.BROWSER.toLowerCase() === 'edge' && {
            executablePath: undefined // Let Playwright find Edge automatically
          })
        };

        // Log the browser configuration for debugging
        this.logger.info(`Launching ${EnvConfig.BROWSER} browser with channel: ${EnvConfig.BROWSER_CHANNEL || 'default'}`);
        if (EnvConfig.PRIVATE_SESSION) {
          const privateArgs = this.getPrivateSessionArgs();
          this.logger.info(`Private session enabled with args: ${JSON.stringify(privateArgs)}`);
          console.log(`🔒 Private session args for ${EnvConfig.BROWSER}:`, privateArgs);
        }

        this.logger.info(`Browser launch options: ${JSON.stringify(launchOptions)}`);

        switch (EnvConfig.BROWSER.toLowerCase()) {
          case 'firefox':
            this.logger.info('Launching Firefox browser');
            this.browser = await firefox.launch(launchOptions);
            break;
          case 'webkit':
            this.logger.info('Launching WebKit browser');
            this.browser = await webkit.launch(launchOptions);
            break;
          case 'edge':
            this.logger.info('Launching Edge browser');
            try {
              // Try launching Edge with explicit executable path if available
              if (process.env.EDGE_PATH) {
                this.logger.info(`Using custom Edge path: ${process.env.EDGE_PATH}`);
                this.browser = await chromium.launch({
                  ...launchOptions,
                  executablePath: process.env.EDGE_PATH
                });
              } else {
                // Edge is a channel of Chromium with specific configuration
                this.browser = await chromium.launch({
                  ...launchOptions,
                  channel: 'msedge',
                  ignoreDefaultArgs: ['--disable-extensions']
                });
              }
            } catch (edgeError) {
              this.logger.error(`Failed to launch Edge: ${edgeError}`);
              // Try alternative approach for Edge
              this.logger.info('Trying alternative approach for Edge');
              this.browser = await chromium.launch({
                headless: EnvConfig.HEADLESS,
                channel: 'msedge',
                ignoreDefaultArgs: true,
                args: [
                  '--no-sandbox',
                  '--disable-setuid-sandbox',
                  '--disable-dev-shm-usage',
                  '--disable-accelerated-2d-canvas',
                  '--no-first-run',
                  '--no-zygote',
                  '--disable-gpu',
                  '--mute-audio',
                  '--disable-background-networking',
                  '--disable-background-timer-throttling',
                  '--disable-backgrounding-occluded-windows',
                  '--disable-breakpad',
                  '--disable-component-extensions-with-background-pages',
                  '--disable-extensions',
                  '--disable-features=TranslateUI',
                  '--disable-ipc-flooding-protection',
                  '--disable-renderer-backgrounding',
                  '--enable-automation',
                  '--password-store=basic',
                  '--use-mock-keychain',
                ]
              });
            }
            break;
          case 'chrome':
            this.logger.info('Launching Chrome browser');
            // Chrome is a channel of Chromium
            this.browser = await chromium.launch({
              ...launchOptions,
              channel: 'chrome'
            });
            break;
          case 'chromium':
          default:
            this.logger.info('Launching Chromium browser');
            this.browser = await chromium.launch(launchOptions);
            break;
        }

        this.logger.info('Browser initialized successfully');
      } catch (error) {
        this.logger.error(`Failed to initialize browser: ${error}`);

        // Fallback to Chromium if the specified browser fails
        if (EnvConfig.BROWSER.toLowerCase() !== 'chromium') {
          this.logger.info('Falling back to Chromium browser');
          try {
            this.browser = await chromium.launch({
              headless: EnvConfig.HEADLESS,
              slowMo: EnvConfig.SLOW_MO,
              timeout: 60000
            });
            this.logger.info('Fallback to Chromium successful');
          } catch (fallbackError) {
            this.logger.error(`Fallback to Chromium also failed: ${fallbackError}`);
            throw fallbackError;
          }
        } else {
          throw error;
        }
      }
    }

    return this.browser;
  }

  /**
   * Get browser-specific private session arguments
   */
  private getPrivateSessionArgs(): string[] {
    const baseArgs = [
      '--no-first-run',
      '--no-default-browser-check',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor'
    ];

    switch (EnvConfig.BROWSER.toLowerCase()) {
      case 'edge':
        return [
          '--inprivate',              // Edge InPrivate mode - correct flag for Edge
          '--new-window',             // Force new window
          '--disable-extensions',     // Disable extensions in private mode
          '--disable-web-security',   // Allow cross-origin requests
          '--no-first-run',
          '--no-default-browser-check',
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding',
          '--disable-features=VizDisplayCompositor',
          '--disable-blink-features=AutomationControlled',
          '--disable-dev-shm-usage',
          '--force-new-instance'      // Force new Edge instance
        ];

      case 'chrome':
      case 'chromium':
        return [
          '--incognito',              // Chrome incognito mode
          '--disable-extensions',     // Disable extensions in incognito
          ...baseArgs
        ];

      case 'firefox':
        return [
          '--private-window',         // Firefox private browsing
          '--new-instance',           // New Firefox instance
          ...baseArgs
        ];

      case 'webkit':
        return [
          '--private-browsing',       // WebKit private browsing
          ...baseArgs
        ];

      default:
        // Fallback to Chrome-style arguments
        return [
          '--incognito',
          ...baseArgs
        ];
    }
  }

  /**
   * Initialize the browser context
   * @returns The initialized browser context
   */
  public async initContext(): Promise<BrowserContext> {
    if (!this.browser) {
      await this.initBrowser();
    }

    if (!this.context) {
      this.logger.info('Initializing browser context');

      // Context options
      const contextOptions: any = {};

      // Device emulation
      if (EnvConfig.DEVICE_NAME && devices[EnvConfig.DEVICE_NAME]) {
        Object.assign(contextOptions, devices[EnvConfig.DEVICE_NAME]);
      } else {
        // Custom viewport
        contextOptions.viewport = {
          width: EnvConfig.VIEWPORT_WIDTH,
          height: EnvConfig.VIEWPORT_HEIGHT
        };
      }

      // Video recording
      if (EnvConfig.VIDEO_RECORDING) {
        contextOptions.recordVideo = {
          dir: EnvConfig.VIDEO_DIR || './test-results/videos/',
          size: {
            width: EnvConfig.VIDEO_WIDTH || 1280,
            height: EnvConfig.VIDEO_HEIGHT || 720
          }
        };
      }

      // Private session configuration
      if (EnvConfig.PRIVATE_SESSION) {
        // Ensure no storage state is used for private sessions
        contextOptions.storageState = undefined;
        // Disable persistent storage completely
        contextOptions.acceptDownloads = false;
        // Clear any existing data
        contextOptions.clearCookies = true;
        contextOptions.clearLocalStorage = true;
        // Force incognito-like behavior
        contextOptions.ignoreHTTPSErrors = true;
        contextOptions.bypassCSP = true;
        this.logger.info('Private session mode enabled - no persistent storage');
      }

      // Geolocation
      if (EnvConfig.GEO_LATITUDE && EnvConfig.GEO_LONGITUDE) {
        contextOptions.geolocation = {
          latitude: parseFloat(EnvConfig.GEO_LATITUDE),
          longitude: parseFloat(EnvConfig.GEO_LONGITUDE)
        };
      }

      // Permissions
      if (EnvConfig.PERMISSIONS) {
        contextOptions.permissions = EnvConfig.PERMISSIONS.split(',');
      }

      // Locale and timezone
      if (EnvConfig.LOCALE) {
        contextOptions.locale = EnvConfig.LOCALE;
      }

      if (EnvConfig.TIMEZONE_ID) {
        contextOptions.timezoneId = EnvConfig.TIMEZONE_ID;
      }

      // Create the context
      this.context = await this.browser!.newContext(contextOptions);

      // Enable tracing if configured
      if (EnvConfig.TRACE_ENABLED) {
        await this.context.tracing.start({
          screenshots: EnvConfig.TRACE_SCREENSHOTS,
          snapshots: EnvConfig.TRACE_SNAPSHOTS,
          sources: EnvConfig.TRACE_SOURCES
        });
      }

      // Set HTTP credentials if configured
      if (EnvConfig.HTTP_CREDENTIALS_USERNAME && EnvConfig.HTTP_CREDENTIALS_PASSWORD) {
        await this.context.setHTTPCredentials({
          username: EnvConfig.HTTP_CREDENTIALS_USERNAME,
          password: EnvConfig.HTTP_CREDENTIALS_PASSWORD
        });
      }

      this.logger.info('Browser context initialized successfully');
    }

    return this.context;
  }

  /**
   * Initialize the page
   * @returns The initialized page
   */
  public async initPage(): Promise<Page> {
    if (!this.context) {
      await this.initContext();
    }

    if (!this.page) {
      this.logger.info('Initializing page');

      this.page = await this.context!.newPage();

      // Set default navigation timeout
      this.page.setDefaultNavigationTimeout(EnvConfig.NAVIGATION_TIMEOUT);
      this.page.setDefaultTimeout(EnvConfig.DEFAULT_TIMEOUT);

      this.logger.info('Page initialized successfully');
    }

    return this.page;
  }

  /**
   * Get the current page
   * @returns The current page
   */
  public getPage(): Page {
    if (!this.page) {
      throw new Error('Page not initialized. Call initPage() first.');
    }
    return this.page;
  }

  /**
   * Get the LocatorManager instance
   * @returns The LocatorManager instance
   */
  public getLocatorManager(): LocatorManager {
    return LocatorManager.getInstance();
  }

  /**
   * Close the browser and all associated resources
   */
  public async closeBrowser(): Promise<void> {
    this.logger.info('Closing browser and all resources');

    if (this.context && EnvConfig.TRACE_ENABLED) {
      await this.context.tracing.stop({
        path: './test-results/trace.zip'
      });
    }

    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.context = null;
      this.page = null;
    }

    this.logger.info('Browser closed successfully');
  }
}
