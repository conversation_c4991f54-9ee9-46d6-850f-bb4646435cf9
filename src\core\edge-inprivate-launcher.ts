import { spawn, ChildProcess } from 'child_process';
import { chromium, <PERSON><PERSON><PERSON>, <PERSON>rowser<PERSON>ontext } from 'playwright';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Edge InPrivate Launcher
 * 
 * This class provides a workaround for launching Edge in true InPrivate mode
 * when <PERSON><PERSON>'s standard approach doesn't work properly.
 */
export class EdgeInPrivateLauncher {
  private edgeProcess: ChildProcess | null = null;
  private browser: Browser | null = null;

  /**
   * Find Edge executable path on Windows
   */
  private findEdgeExecutable(): string | null {
    const edgePaths = [
      'C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe',
      'C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe',
      process.env.PROGRAMFILES + '\\Microsoft\\Edge\\Application\\msedge.exe',
      process.env['PROGRAMFILES(X86)'] + '\\Microsoft\\Edge\\Application\\msedge.exe'
    ];

    for (const edgePath of edgePaths) {
      if (fs.existsSync(edgePath)) {
        return edgePath;
      }
    }

    return null;
  }

  /**
   * Launch Edge in InPrivate mode directly
   */
  async launchEdgeInPrivateDirect(url?: string): Promise<boolean> {
    const edgeExePath = this.findEdgeExecutable();
    
    if (!edgeExePath) {
      console.error('Edge executable not found');
      return false;
    }

    try {
      const args = [
        '--inprivate',
        '--new-window',
        '--disable-extensions',
        '--no-first-run',
        '--no-default-browser-check'
      ];

      if (url) {
        args.push(url);
      }

      this.edgeProcess = spawn(edgeExePath, args, {
        detached: false,
        stdio: 'pipe'
      });

      console.log('✅ Edge launched directly in InPrivate mode');
      return true;

    } catch (error) {
      console.error('❌ Failed to launch Edge directly:', error);
      return false;
    }
  }

  /**
   * Launch Edge with Playwright but force InPrivate-like behavior
   */
  async launchEdgeWithPlaywright(headless: boolean = false): Promise<Browser | null> {
    try {
      // First, try to close any existing Edge instances
      await this.closeExistingEdgeInstances();

      // Launch Edge with Playwright using enhanced arguments
      this.browser = await chromium.launch({
        channel: 'msedge',
        headless,
        args: [
          '--inprivate',
          '--new-window',
          '--disable-extensions',
          '--disable-web-security',
          '--no-first-run',
          '--no-default-browser-check',
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding',
          '--disable-features=VizDisplayCompositor',
          '--disable-blink-features=AutomationControlled',
          '--disable-dev-shm-usage',
          '--force-new-instance',
          '--user-data-dir=' + path.join(process.cwd(), 'temp-edge-profile-' + Date.now())
        ]
      });

      console.log('✅ Edge launched with Playwright (InPrivate mode)');
      return this.browser;

    } catch (error) {
      console.error('❌ Failed to launch Edge with Playwright:', error);
      return null;
    }
  }

  /**
   * Create InPrivate-like context
   */
  async createInPrivateContext(browser: Browser): Promise<BrowserContext> {
    const context = await browser.newContext({
      // Force clean state
      storageState: { cookies: [], origins: [] },
      acceptDownloads: false,
      ignoreHTTPSErrors: true,
      bypassCSP: true,
      serviceWorkers: 'block'
    });

    // Clear any existing storage on each page
    context.on('page', async (page) => {
      await page.addInitScript(() => {
        // Clear all storage
        if (window.localStorage) {
          window.localStorage.clear();
        }
        if (window.sessionStorage) {
          window.sessionStorage.clear();
        }
        // Clear cookies
        document.cookie.split(";").forEach(function(c) { 
          document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
        });
      });
    });

    return context;
  }

  /**
   * Close existing Edge instances
   */
  private async closeExistingEdgeInstances(): Promise<void> {
    try {
      // On Windows, try to close Edge processes
      if (process.platform === 'win32') {
        spawn('taskkill', ['/F', '/IM', 'msedge.exe'], { stdio: 'ignore' });
        // Wait a bit for processes to close
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    } catch (error) {
      // Ignore errors - Edge might not be running
    }
  }

  /**
   * Cleanup
   */
  async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }

    if (this.edgeProcess) {
      this.edgeProcess.kill();
      this.edgeProcess = null;
    }
  }
}
