import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

// Load environment variables from .env file
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

/**
 * Environment Configuration
 *
 * A comprehensive configuration system that centralizes all environment-specific settings.
 * This class provides:
 * - Browser configuration
 * - Test execution settings
 * - Authentication options
 * - Reporting configuration
 * - Visual settings
 * - Network settings
 * - Locale and timezone
 * - And much more
 *
 * All settings can be configured via environment variables or .env file.
 */
export class EnvConfig {
  // Browser configuration
  static readonly BROWSER = process.env.BROWSER || 'chromium';
  static readonly BROWSER_CHANNEL = process.env.BROWSER_CHANNEL;
  static readonly HEADLESS = process.env.HEADLESS === 'true';
  static readonly SLOW_MO = parseInt(process.env.SLOW_MO || '0', 10);
  static readonly VIEWPORT_WIDTH = parseInt(process.env.VIEWPORT_WIDTH || '1280', 10);
  static readonly VIEWPORT_HEIGHT = parseInt(process.env.VIEWPORT_HEIGHT || '720', 10);
  static readonly DEVICE_NAME = process.env.DEVICE_NAME;
  static readonly PRIVATE_SESSION = process.env.PRIVATE_SESSION !== 'false';

  // Environment URLs
  static readonly BASE_URL = process.env.BASE_URL || 'https://example.com';
  static readonly API_URL = process.env.API_URL || 'https://api.example.com';
  static readonly SPO_URL = process.env.SPO_URL || 'https://spo.example.com';

  // Authentication
  static readonly USERNAME = process.env.USERNAME || 'test_user';
  static readonly PASSWORD = process.env.PASSWORD || 'test_password';

  // Timeouts
  static readonly DEFAULT_TIMEOUT = parseInt(process.env.DEFAULT_TIMEOUT || '30000', 10);
  static readonly NAVIGATION_TIMEOUT = parseInt(process.env.NAVIGATION_TIMEOUT || '60000', 10);

  // Test execution configuration
  static readonly RETRY_COUNT = parseInt(process.env.RETRY_COUNT || '1', 10);
  static readonly PARALLEL_INSTANCES = parseInt(process.env.PARALLEL_INSTANCES || '1', 10);
  static readonly RETRY_MAX_ATTEMPTS = parseInt(process.env.RETRY_MAX_ATTEMPTS || '3', 10);
  static readonly RETRY_DELAY_MS = parseInt(process.env.RETRY_DELAY_MS || '1000', 10);
  static readonly SMART_INTERACTION = process.env.SMART_INTERACTION !== 'false';
  static readonly ELEMENT_STABILITY_TIMEOUT = parseInt(process.env.ELEMENT_STABILITY_TIMEOUT || '300', 10);
  static readonly ELEMENT_STABILITY_TOLERANCE = parseInt(process.env.ELEMENT_STABILITY_TOLERANCE || '5', 10);
  static readonly ELEMENT_STABILITY_MAX_ATTEMPTS = parseInt(process.env.ELEMENT_STABILITY_MAX_ATTEMPTS || '8', 10);

  // Reporting configuration
  static readonly SCREENSHOT_ON_FAILURE = process.env.SCREENSHOT_ON_FAILURE !== 'false';
  static readonly SCREENSHOT_ALWAYS = process.env.SCREENSHOT_ALWAYS === 'true';
  static readonly SCREENSHOT_DIR = process.env.SCREENSHOT_DIR || './test-results/screenshots';
  static readonly REPORT_DIR = process.env.REPORT_DIR || './test-results/reports';
  static readonly REPORT_TITLE = process.env.REPORT_TITLE || 'Playwright Automation Test Report';
  static readonly REPORT_NAME = process.env.REPORT_NAME || 'Playwright Automation Test Report';
  static readonly OPEN_REPORT_AFTER_RUN = process.env.OPEN_REPORT_AFTER_RUN !== 'false';
  static readonly GENERATE_HTML_REPORT = process.env.GENERATE_HTML_REPORT !== 'false';
  static readonly REPORT_HTML_DIR = process.env.REPORT_HTML_DIR || './test-results/html-report';

  // Element highlighting
  static readonly HIGHLIGHT_ELEMENTS = process.env.HIGHLIGHT_ELEMENTS !== 'false';
  static readonly HIGHLIGHT_COLOR = process.env.HIGHLIGHT_COLOR || 'red';
  static readonly HIGHLIGHT_DURATION = parseInt(process.env.HIGHLIGHT_DURATION || '1000', 10);

  // Video recording
  static readonly VIDEO_RECORDING = process.env.VIDEO_RECORDING !== 'false';
  static readonly VIDEO_DIR = process.env.VIDEO_DIR || './test-results/videos';
  static readonly VIDEO_WIDTH = parseInt(process.env.VIDEO_WIDTH || '1280', 10);
  static readonly VIDEO_HEIGHT = parseInt(process.env.VIDEO_HEIGHT || '720', 10);
  static readonly VIDEO_MODE = process.env.VIDEO_MODE || 'retain-on-failure';

  // Trace viewer
  static readonly TRACE_ENABLED = process.env.TRACE_ENABLED !== 'false';
  static readonly TRACE_DIR = process.env.TRACE_DIR || './test-results/traces';
  static readonly TRACE_SCREENSHOTS = process.env.TRACE_SCREENSHOTS !== 'false';
  static readonly TRACE_SNAPSHOTS = process.env.TRACE_SNAPSHOTS !== 'false';
  static readonly TRACE_SOURCES = process.env.TRACE_SOURCES !== 'false';
  static readonly TRACE_MODE = process.env.TRACE_MODE || 'retain-on-failure';

  // Geolocation
  static readonly GEO_LATITUDE = process.env.GEO_LATITUDE;
  static readonly GEO_LONGITUDE = process.env.GEO_LONGITUDE;

  // Permissions
  static readonly PERMISSIONS = process.env.PERMISSIONS;

  // Locale and timezone
  static readonly LOCALE = process.env.LOCALE;
  static readonly TIMEZONE_ID = process.env.TIMEZONE_ID;

  // HTTP Authentication
  static readonly HTTP_CREDENTIALS_USERNAME = process.env.HTTP_CREDENTIALS_USERNAME;
  static readonly HTTP_CREDENTIALS_PASSWORD = process.env.HTTP_CREDENTIALS_PASSWORD;

  // Test data
  static readonly TEST_DATA_DIR = process.env.TEST_DATA_DIR || 'src/test-data';

  // Downloads
  static readonly DOWNLOAD_DIR = process.env.DOWNLOAD_DIR || './test-results/downloads';

  // Project information
  static readonly PROJECT_NAME = process.env.PROJECT_NAME || 'Playwright Automation Framework';

  // Session management
  static readonly REUSE_AUTH_SESSION = process.env.REUSE_AUTH_SESSION !== 'false';
  static readonly SESSION_DIR = process.env.SESSION_DIR || './test-results/sessions';
  static readonly SESSION_EXPIRY_MINUTES = parseInt(process.env.SESSION_EXPIRY_MINUTES || '30', 10);

  // Browser lifecycle management
  static readonly SINGLE_BROWSER_PER_FEATURE = process.env.SINGLE_BROWSER_PER_FEATURE !== 'false';
  static readonly BROWSER_REUSE_STRATEGY = process.env.BROWSER_REUSE_STRATEGY || 'feature';
  static readonly CLOSE_BROWSER_ON_FAILURE = process.env.CLOSE_BROWSER_ON_FAILURE === 'true';

  // Debugging
  static readonly LOG_CONSOLE_MESSAGES = process.env.LOG_CONSOLE_MESSAGES || 'false';
  static readonly LOG_NETWORK_ERRORS = process.env.LOG_NETWORK_ERRORS || 'false';
  static readonly IGNORE_CONSOLE_ERRORS = process.env.IGNORE_CONSOLE_ERRORS || 'true';
  static readonly DEBUG_MODE = process.env.DEBUG_MODE === 'true';
  static readonly PAUSE_ON_FAILURE = process.env.PAUSE_ON_FAILURE === 'true';

  // Logging Configuration
  static readonly LOG_CONSOLE_LEVEL = process.env.LOG_CONSOLE_LEVEL || 'info';
  static readonly LOG_FILE_LEVEL = process.env.LOG_FILE_LEVEL || 'debug';
  static readonly LOG_SHOW_TIMESTAMP = process.env.LOG_SHOW_TIMESTAMP !== 'false';
  static readonly LOG_SHOW_MODULE_NAME = process.env.LOG_SHOW_MODULE_NAME !== 'false';
  static readonly LOG_CONSOLE_ENABLED = process.env.LOG_CONSOLE_ENABLED !== 'false';
  static readonly LOG_FILE_ENABLED = process.env.LOG_FILE_ENABLED !== 'false';
  static readonly LOG_CONCISE = process.env.LOG_CONCISE !== 'false';

  // Visual testing
  static readonly VISUAL_TESTING_ENABLED = process.env.VISUAL_TESTING_ENABLED === 'true';
  static readonly VISUAL_BASELINE_DIR = process.env.VISUAL_BASELINE_DIR || './test-results/visual-baseline';
  static readonly VISUAL_DIFF_DIR = process.env.VISUAL_DIFF_DIR || './test-results/visual-diff';
  static readonly VISUAL_THRESHOLD = parseFloat(process.env.VISUAL_THRESHOLD || '0.1');

  // Credentials (for demo purposes - in real projects use secure storage)
  static readonly ADMIN_USERNAME = process.env.ADMIN_USERNAME;
  static readonly ADMIN_PASSWORD = process.env.ADMIN_PASSWORD;
  static readonly STANDARD_USERNAME = process.env.STANDARD_USERNAME;
  static readonly STANDARD_PASSWORD = process.env.STANDARD_PASSWORD;

  /**
   * Get the current environment name
   * @returns The current environment name (dev, staging, prod)
   */
  static getEnvironment(): string {
    return process.env.NODE_ENV || 'dev';
  }

  /**
   * Check if the current environment is production
   * @returns True if the current environment is production
   */
  static isProduction(): boolean {
    return this.getEnvironment() === 'prod';
  }

  /**
   * Get a configuration value with a fallback
   * @param key The environment variable key
   * @param defaultValue The default value if the key is not found
   * @returns The configuration value
   */
  static get(key: string, defaultValue: string = ''): string {
    return process.env[key] || defaultValue;
  }

  /**
   * Get a boolean configuration value
   * @param key The environment variable key
   * @param defaultValue The default value if the key is not found
   * @returns The boolean value
   */
  static getBoolean(key: string, defaultValue: boolean = false): boolean {
    const value = process.env[key];
    if (value === undefined) return defaultValue;
    return value.toLowerCase() === 'true';
  }

  /**
   * Get a number configuration value
   * @param key The environment variable key
   * @param defaultValue The default value if the key is not found
   * @returns The number value
   */
  static getNumber(key: string, defaultValue: number = 0): number {
    const value = process.env[key];
    if (value === undefined) return defaultValue;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? defaultValue : parsed;
  }

  /**
   * Get an array configuration value
   * @param key The environment variable key
   * @param separator The separator to split the string (default: comma)
   * @param defaultValue The default value if the key is not found
   * @returns The array value
   */
  static getArray(key: string, separator: string = ',', defaultValue: string[] = []): string[] {
    const value = process.env[key];
    if (!value) return defaultValue;
    return value.split(separator).map(item => item.trim());
  }

  /**
   * Check if debugging is enabled
   * @returns True if debugging is enabled
   */
  static isDebugEnabled(): boolean {
    return this.DEBUG_MODE || this.getEnvironment() === 'dev';
  }

  /**
   * Get the path to a directory, creating it if it doesn't exist
   * @param dirPath The directory path
   * @returns The absolute path to the directory
   */
  static getOrCreateDir(dirPath: string): string {
    const absolutePath = path.isAbsolute(dirPath)
      ? dirPath
      : path.join(process.cwd(), dirPath);

    if (!fs.existsSync(absolutePath)) {
      fs.mkdirSync(absolutePath, { recursive: true });
    }

    return absolutePath;
  }
}
