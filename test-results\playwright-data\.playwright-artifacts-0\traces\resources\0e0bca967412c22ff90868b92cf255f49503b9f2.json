[{"time": "2025-06-05T07:19:38.865Z", "iKey": "0b456e85-ed56-4af9-9d42-c8421de22c78", "name": "Microsoft.ApplicationInsights.0b456e85ed564af99d42c8421de22c78.PageviewPerformance", "tags": {"ai.user.id": "6bMG0rlpIAuVIhyh9yOW4N", "ai.session.id": "m6g4sIS3CRfThXtAHro8fW", "ai.device.id": "browser", "ai.device.type": "Browser", "ai.operation.name": "/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx", "ai.operation.id": "d5e40bcb4ca3443a88dbaa5e87ca56e3", "ai.internal.sdkVersion": "javascript:3.3.8"}, "data": {"baseType": "PageviewPerformanceData", "baseData": {"ver": 2, "name": "Results", "url": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx?q=kpmg&datasource=intranet&origin=portal&caller=https%3A%2F%2Fspo-global.kpmg.com%2Fsites%2FGO-oi-bus-kpmgfinddeventerprisesearch&premium=false", "duration": "00:00:05.438", "perfTotal": "00:00:05.438", "networkConnect": "00:00:00.587", "sentRequest": "00:00:00.347", "receivedResponse": "00:00:00.263", "domProcessing": "00:00:04.234", "properties": {"KPMG Source": "OneIntranet", "KPMG HubSiteId": "36072d22-c34a-430f-96a5-6f4e3e097513", "OriginSiteUrl": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch", "OriginFullSiteUrl": "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch"}, "measurements": {"duration": 0}}}}]