<!DOCTYPE html>

<html>
<head>
    <script type="text/javascript" src="https://res-1.cdn.office.net/shellux/suiteux.shell.msaltokenfactoryiframe.f11d0cf8b9d8aeb5d88d.js"></script>
</head>
<body>
    <script type="text/javascript" nonce="9W8GP3uQt&#x2B;ueckA0WTspI&#x2B;LRE3mZHXQeTxuSEAThVNw=">
        
                var tokenFactorySettings = {
                    clientId: "89bee1f7-5e6e-4d8a-9f3d-ecd601259da7",
                    authority: "https\u003A//login.microsoftonline.com/organizations",
                    cacheLocation: "sessionStorage",
                    parentPageOrigin: null,
                    upn: null
                };

                O365MSALTokenFactoryIframe.TokenFactoryInsideIframe.init(tokenFactorySettings);
    </script>
</body>
</html>