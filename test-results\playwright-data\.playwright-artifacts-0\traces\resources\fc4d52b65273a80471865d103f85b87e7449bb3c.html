

<!-- Copyright (C) Microsoft Corporation. All rights reserved. -->
<!DOCTYPE html>
<html>
<head>
    <title>Redirecting</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0, user-scalable=yes">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="-1">
    <meta name="PageID" content="BssoInterrupt" />
    <meta name="SiteID" content="" />
    <meta name="ReqLC" content="1033" />
    <meta name="LocLC" content="en-US" />

    
<meta name="robots" content="none" />

<script type="text/javascript" nonce='A2moBrtYUP-no8mJhhAR0Q'>//<![CDATA[
$Config={"oPostParams":{"wa":"wsignin1.0","wresult":"\u0026lt;t:RequestSecurityTokenResponse xmlns:t=\u0026quot;http://schemas.xmlsoap.org/ws/2005/02/trust\u0026quot;\u0026gt;\u0026lt;t:Lifetime\u0026gt;\u0026lt;wsu:Created xmlns:wsu=\u0026quot;http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd\u0026quot;\u0026gt;2025-06-05T06:26:02.207Z\u0026lt;/wsu:Created\u0026gt;\u0026lt;wsu:Expires xmlns:wsu=\u0026quot;http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd\u0026quot;\u0026gt;2025-06-05T07:26:02.207Z\u0026lt;/wsu:Expires\u0026gt;\u0026lt;/t:Lifetime\u0026gt;\u0026lt;wsp:AppliesTo xmlns:wsp=\u0026quot;http://schemas.xmlsoap.org/ws/2004/09/policy\u0026quot;\u0026gt;\u0026lt;wsa:EndpointReference xmlns:wsa=\u0026quot;http://www.w3.org/2005/08/addressing\u0026quot;\u0026gt;\u0026lt;wsa:Address\u0026gt;urn:federation:MicrosoftOnline\u0026lt;/wsa:Address\u0026gt;\u0026lt;/wsa:EndpointReference\u0026gt;\u0026lt;/wsp:AppliesTo\u0026gt;\u0026lt;t:RequestedSecurityToken\u0026gt;\u0026lt;saml:Assertion MajorVersion=\u0026quot;1\u0026quot; MinorVersion=\u0026quot;1\u0026quot; AssertionID=\u0026quot;_8aa68785-2ffd-41e0-98f9-6a51560a9e77\u0026quot; Issuer=\u0026quot;http://kpmg.com/adfs/services/trust/\u0026quot; IssueInstant=\u0026quot;2025-06-05T06:26:02.292Z\u0026quot; xmlns:saml=\u0026quot;urn:oasis:names:tc:SAML:1.0:assertion\u0026quot;\u0026gt;\u0026lt;saml:Conditions NotBefore=\u0026quot;2025-06-05T06:26:02.207Z\u0026quot; NotOnOrAfter=\u0026quot;2025-06-05T07:26:02.207Z\u0026quot;\u0026gt;\u0026lt;saml:AudienceRestrictionCondition\u0026gt;\u0026lt;saml:Audience\u0026gt;urn:federation:MicrosoftOnline\u0026lt;/saml:Audience\u0026gt;\u0026lt;/saml:AudienceRestrictionCondition\u0026gt;\u0026lt;/saml:Conditions\u0026gt;\u0026lt;saml:AttributeStatement\u0026gt;\u0026lt;saml:Subject\u0026gt;\u0026lt;saml:NameIdentifier Format=\u0026quot;urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified\u0026quot;\u0026gt;IFbFk0ElSEOfQHlo/OCA6w==\u0026lt;/saml:NameIdentifier\u0026gt;\u0026lt;saml:SubjectConfirmation\u0026gt;\u0026lt;saml:ConfirmationMethod\u0026gt;urn:oasis:names:tc:SAML:1.0:cm:bearer\u0026lt;/saml:ConfirmationMethod\u0026gt;\u0026lt;/saml:SubjectConfirmation\u0026gt;\u0026lt;/saml:Subject\u0026gt;\u0026lt;saml:Attribute AttributeName=\u0026quot;UPN\u0026quot; AttributeNamespace=\u0026quot;http://schemas.xmlsoap.org/claims\u0026quot;\u0026gt;\u0026lt;saml:AttributeValue\u0026gt;<EMAIL>\u0026lt;/saml:AttributeValue\u0026gt;\u0026lt;/saml:Attribute\u0026gt;\u0026lt;saml:Attribute AttributeName=\u0026quot;ImmutableID\u0026quot; AttributeNamespace=\u0026quot;http://schemas.microsoft.com/LiveID/Federation/2008/05\u0026quot;\u0026gt;\u0026lt;saml:AttributeValue\u0026gt;IFbFk0ElSEOfQHlo/OCA6w==\u0026lt;/saml:AttributeValue\u0026gt;\u0026lt;/saml:Attribute\u0026gt;\u0026lt;saml:Attribute AttributeName=\u0026quot;authnmethodsreferences\u0026quot; AttributeNamespace=\u0026quot;http://schemas.microsoft.com/claims\u0026quot;\u0026gt;\u0026lt;saml:AttributeValue\u0026gt;http://schemas.microsoft.com/ws/2008/06/identity/authenticationmethod/windows\u0026lt;/saml:AttributeValue\u0026gt;\u0026lt;saml:AttributeValue\u0026gt;http://schemas.microsoft.com/ws/2008/06/identity/authenticationmethod/kerberos\u0026lt;/saml:AttributeValue\u0026gt;\u0026lt;/saml:Attribute\u0026gt;\u0026lt;saml:Attribute AttributeName=\u0026quot;insidecorporatenetwork\u0026quot; AttributeNamespace=\u0026quot;http://schemas.microsoft.com/ws/2012/01\u0026quot; a:OriginalIssuer=\u0026quot;CLIENT CONTEXT\u0026quot; xmlns:a=\u0026quot;http://schemas.xmlsoap.org/ws/2009/09/identity/claims\u0026quot;\u0026gt;\u0026lt;saml:AttributeValue b:type=\u0026quot;tn:boolean\u0026quot; xmlns:tn=\u0026quot;http://www.w3.org/2001/XMLSchema\u0026quot; xmlns:b=\u0026quot;http://www.w3.org/2001/XMLSchema-instance\u0026quot;\u0026gt;true\u0026lt;/saml:AttributeValue\u0026gt;\u0026lt;/saml:Attribute\u0026gt;\u0026lt;/saml:AttributeStatement\u0026gt;\u0026lt;saml:AuthenticationStatement AuthenticationMethod=\u0026quot;urn:federation:authentication:windows\u0026quot; AuthenticationInstant=\u0026quot;2025-06-05T06:26:02.121Z\u0026quot;\u0026gt;\u0026lt;saml:Subject\u0026gt;\u0026lt;saml:NameIdentifier Format=\u0026quot;urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified\u0026quot;\u0026gt;IFbFk0ElSEOfQHlo/OCA6w==\u0026lt;/saml:NameIdentifier\u0026gt;\u0026lt;saml:SubjectConfirmation\u0026gt;\u0026lt;saml:ConfirmationMethod\u0026gt;urn:oasis:names:tc:SAML:1.0:cm:bearer\u0026lt;/saml:ConfirmationMethod\u0026gt;\u0026lt;/saml:SubjectConfirmation\u0026gt;\u0026lt;/saml:Subject\u0026gt;\u0026lt;/saml:AuthenticationStatement\u0026gt;\u0026lt;ds:Signature xmlns:ds=\u0026quot;http://www.w3.org/2000/09/xmldsig#\u0026quot;\u0026gt;\u0026lt;ds:SignedInfo\u0026gt;\u0026lt;ds:CanonicalizationMethod Algorithm=\u0026quot;http://www.w3.org/2001/10/xml-exc-c14n#\u0026quot; /\u0026gt;\u0026lt;ds:SignatureMethod Algorithm=\u0026quot;http://www.w3.org/2001/04/xmldsig-more#rsa-sha256\u0026quot; /\u0026gt;\u0026lt;ds:Reference URI=\u0026quot;#_8aa68785-2ffd-41e0-98f9-6a51560a9e77\u0026quot;\u0026gt;\u0026lt;ds:Transforms\u0026gt;\u0026lt;ds:Transform Algorithm=\u0026quot;http://www.w3.org/2000/09/xmldsig#enveloped-signature\u0026quot; /\u0026gt;\u0026lt;ds:Transform Algorithm=\u0026quot;http://www.w3.org/2001/10/xml-exc-c14n#\u0026quot; /\u0026gt;\u0026lt;/ds:Transforms\u0026gt;\u0026lt;ds:DigestMethod Algorithm=\u0026quot;http://www.w3.org/2001/04/xmlenc#sha256\u0026quot; /\u0026gt;\u0026lt;ds:DigestValue\u0026gt;W/Hxuha6fwUXCHtbxz7VRy7VT/GrWZqz4dmru9cFws8=\u0026lt;/ds:DigestValue\u0026gt;\u0026lt;/ds:Reference\u0026gt;\u0026lt;/ds:SignedInfo\u0026gt;\u0026lt;ds:SignatureValue\u0026gt;TS1pwCpNe9Gbvpdx76hDDfsQn1u5iMDhcqIJy/tqhSOCaEPqTIW3h4QfFyq36zshymFD0RQnTn5FROfakY86DnwH17rENa5hlPZHKd9Wor8l3VG0R5r6I+G5fV9nfQld6Z6E5g8sGZuRm6u1nCMfr0L71rq78BfvnJBmy7PmhdQJ9mxlPhh4HvWRnXNxUeu6eOYNOnhR/O26OcDnkuIsBYZZiEzsiX9HlpVOIkevb4sOkzJrmyfi5CfXLpI+xbUXPVIuVXqmk/lwSWfCF7P/h0xnE689jvsWGQFcdXd6/Kjym4nOZubox9h8Mdoz/p2/o978/9a4f4X6LavqKBoRag==\u0026lt;/ds:SignatureValue\u0026gt;\u0026lt;KeyInfo xmlns=\u0026quot;http://www.w3.org/2000/09/xmldsig#\u0026quot;\u0026gt;\u0026lt;X509Data\u0026gt;\u0026lt;X509Certificate\u0026gt;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\u0026lt;/X509Certificate\u0026gt;\u0026lt;/X509Data\u0026gt;\u0026lt;/KeyInfo\u0026gt;\u0026lt;/ds:Signature\u0026gt;\u0026lt;/saml:Assertion\u0026gt;\u0026lt;/t:RequestedSecurityToken\u0026gt;\u0026lt;t:TokenType\u0026gt;urn:oasis:names:tc:SAML:1.0:assertion\u0026lt;/t:TokenType\u0026gt;\u0026lt;t:RequestType\u0026gt;http://schemas.xmlsoap.org/ws/2005/02/trust/Issue\u0026lt;/t:RequestType\u0026gt;\u0026lt;t:KeyType\u0026gt;http://schemas.xmlsoap.org/ws/2005/05/identity/NoProofKey\u0026lt;/t:KeyType\u0026gt;\u0026lt;/t:RequestSecurityTokenResponse\u0026gt;","wctx":"LoginOptions=3\u0026amp;estsredirect=2\u0026amp;estsrequest=rQQIARAAlVE9aBNhGL7LpWcTW5sWhY56FIfWy33f_X4XKHj35c6ktTSVVoMi8f7SpPm5S-5SY0sXHXTspvgzWHTpJE7SSRSXTpk7SafSQcSpg6AJIjjWh5eH54WXl-d93mkKpmFmCvyBwA6YBeUyZB1voP5BeyKZev127IIUEL2Y9PPl0_phZ5fkKlEUhBmOCwOfXa37tlVP14LGatrxG1yp7LcbIed6ZatTj9JWGHQ_kGSPJI9IcjfWEpEkYz1rCryAFQClfgsgxqqq6FgxVYAVFetqFmtZUTcEoLAqNk1D1TVZhYJoQhUoElQwxFIW8jpWeRnwSNdMWeY1AyMoqTw2RYM3AIBCFprCQWxsUetEFX5Afru64f2IJQYeS4EfRs-p-GIW3N-lThXHe2qqf1eZF22b5QFSWVHsTyAHeWxZgZ6MgKAgm9-naD_wmlX3kDq_Zq1azaYVVaza1b8Z9eLkcXwEUJnh4WSKmCQuEidxcmeon_UlfuXxpxdcbudzfPLGvcvE_hDXNZe0ed8vtmR9bWmuZRr2cs7JL6vziw_EbqvSsgorodExTJ3PzwoZuE2T2zS9RyeGqRTBULgAv9PkkzPEXuJ_v9Y7Sx6MwGTC8e221XSr7sQUhLaLABJYpHiAFaEtsbatIBbYjig4yEIesg5GxCTt1K1qI5yY3mSqbinya16TyWwy3UZYcpyBWrfqHS9kMneYvkHm7tbW1sPRU23_OEqcnPvy5tGzV7-OvuWOx67kZV8T1-1ba-uKe83XvHw7WoC5hZvSbWHjujJTKxSLMxI3V9SLK7PvUsTXfo0TJ-Pbsd81","canary":"xFQAKooXq6BjQJqFEbTHcIT9KOy4xqhqaPUsEuEFB2I=5:1:CANARY:W7/sz/Mkhv3Y2yC6EBFrBPCS1AD+ZNjI4DD/S1arSoM="},"iMaxStackForKnockoutAsyncComponents":10000,"fShowButtons":true,"urlCdn":"https://aadcdn.msauth.net/shared/1.0/","urlDefaultFavicon":"https://aadcdn.msauth.net/shared/1.0/content/images/favicon_a_eupayfgghqiai7k9sol6lg2.ico","urlPost":"/login.srf?client-request-id=150fa5a1-7035-d000-0235-fc9c976ce275\u0026sso_reload=True","iPawnIcon":0,"sPOST_Username":"","sFTName":"flowToken","fEnableOneDSClientTelemetry":true,"dynamicTenantBranding":null,"staticTenantBranding":null,"oAppCobranding":{},"iBackgroundImage":2,"fApplicationInsightsEnabled":false,"iApplicationInsightsEnabledPercentage":0,"urlSetDebugMode":"https://login.microsoftonline.com/common/debugmode","fEnableCssAnimation":true,"fAllowGrayOutLightBox":true,"fUseMsaSessionState":true,"fIsRemoteNGCSupported":true,"desktopSsoConfig":{"isEdgeAnaheimAllowed":true,"iwaEndpointUrlFormat":"https://autologon.microsoftazuread-sso.com/{0}/winauth/sso?client-request-id=150fa5a1-7035-d000-0235-fc9c976ce275","iwaSsoProbeUrlFormat":"https://autologon.microsoftazuread-sso.com/{0}/winauth/ssoprobe?client-request-id=150fa5a1-7035-d000-0235-fc9c976ce275","iwaIFrameUrlFormat":"https://autologon.microsoftazuread-sso.com/{0}/winauth/iframe?client-request-id=150fa5a1-7035-d000-0235-fc9c976ce275\u0026isAdalRequest=False","iwaRequestTimeoutInMs":10000,"startDesktopSsoOnPageLoad":false,"progressAnimationTimeout":10000,"isEdgeAllowed":false,"minDssoEdgeVersion":"17","isSafariAllowed":true,"redirectUri":"","isIEAllowedForSsoProbe":true,"edgeRedirectUri":"https://autologon.microsoftazuread-sso.com/common/winauth/sso/edgeredirect?client-request-id=150fa5a1-7035-d000-0235-fc9c976ce275\u0026origin=login.microsoftonline.com\u0026is_redirected=1","isFlowTokenPassedInEdge":true},"iSessionPullType":2,"fUseSameSite":true,"isGlobalTenant":true,"uiflavor":1001,"fLoadStringCustomizationPromises":true,"fOfflineAccountVisible":false,"fEnableUserStateFix":true,"fShowAccessPassPeek":true,"fUpdateSessionPollingLogic":true,"fEnableShowPickerCredObservable":true,"fFetchSessionsSkipDsso":true,"fIsCiamUserFlowUxNewLogicEnabled":true,"fUseNonMicrosoftDefaultBrandingForCiam":true,"fRemoveCustomCss":true,"fFixUICrashForApiRequestHandler":true,"fShowUpdatedKoreanPrivacyFooter":true,"fUsePostCssHotfix":true,"fUseHighContrastDetectionMode":true,"fFixUserFlowBranding":true,"fEnablePasskeyNullFix":true,"fEnableRefreshCookiesFix":true,"urlAcmaServerPath":"https://login.microsoftonline.com","sTenantId":"common","sMkt":"en-US","scid":2001,"hpgact":2101,"hpgid":6,"apiCanary":"PAQABDgEAAABVrSpeuWamRam2jAF1XRQE8Nt5tJ2MVaOLvC3VpsOj6vjBWFY9qM1u9c3iHJg6aqCPcAolMPwqg-LTAIc38QMpgJsz2-mUKe1R4oxIa7m9xIwnqG7JFX-7y_QeuzmsC8nKDavHp5HwovjqpOlKUDqBJ6dqJ7zWi7qp_bQqY9MHuoMwF6MUWTxpgEKRidT0wttmDCWMTdFBcs_i-sg1aKDyu_psZQGzUxJToiyAAQ2lwCAA","canary":"xFQAKooXq6BjQJqFEbTHcIT9KOy4xqhqaPUsEuEFB2I=5:1:CANARY:W7/sz/Mkhv3Y2yC6EBFrBPCS1AD+ZNjI4DD/S1arSoM=","sCanaryTokenName":"canary","fSkipRenderingNewCanaryToken":false,"fEnableNewCsrfProtection":true,"correlationId":"150fa5a1-7035-d000-0235-fc9c976ce275","sessionId":"4184a7c6-9998-466c-a78d-0681d6392c00","sRingId":"R6","locale":{"mkt":"en-US","lcid":1033},"slMaxRetry":2,"slReportFailure":true,"strings":{"desktopsso":{"authenticatingmessage":"Trying to sign you in"}},"enums":{"ClientMetricsModes":{"None":0,"SubmitOnPost":1,"SubmitOnRedirect":2,"InstrumentPlt":4}},"urls":{"instr":{"pageload":"https://login.microsoftonline.com/common/instrumentation/reportpageload","dssostatus":"https://login.microsoftonline.com/common/instrumentation/dssostatus"}},"browser":{"ltr":1,"Chrome":1,"_Win":1,"_M136":1,"_D0":1,"Full":1,"Win81":1,"RE_WebKit":1,"b":{"name":"Chrome","major":136,"minor":0},"os":{"name":"Windows","version":"10.0"},"V":"136.0"},"watson":{"url":"/common/handlers/watson","bundle":"https://aadcdn.msauth.net/ests/2.1/content/cdnbundles/watson.min_q5ptmu8aniymd4ftuqdkda2.js","sbundle":"https://aadcdn.msauth.net/ests/2.1/content/cdnbundles/watsonsupportwithjquery.3.5.min_dc940oomzau4rsu8qesnvg2.js","fbundle":"https://aadcdn.msauth.net/ests/2.1/content/cdnbundles/frameworksupport.min_oadrnc13magb009k4d20lg2.js","resetErrorPeriod":5,"maxCorsErrors":-1,"maxInjectErrors":5,"maxErrors":10,"maxTotalErrors":3,"expSrcs":["https://login.microsoftonline.com","https://aadcdn.msauth.net/","https://aadcdn.msftauth.net/",".login.microsoftonline.com"],"envErrorRedirect":true,"envErrorUrl":"/common/handlers/enverror"},"loader":{"cdnRoots":["https://aadcdn.msauth.net/","https://aadcdn.msftauth.net/"],"logByThrowing":true},"serverDetails":{"slc":"ProdSlices","dc":"FRC","ri":"PA3XXXX","ver":{"v":[2,1,20899,4]},"rt":"2025-06-05T06:26:02","et":3},"clientEvents":{"enabled":true,"telemetryEnabled":true,"useOneDSEventApi":true,"flush":60000,"autoPost":true,"autoPostDelay":1000,"minEvents":1,"maxEvents":1,"pltDelay":500,"appInsightsConfig":{"instrumentationKey":"b0c252808e614e949086e019ae1cb300-e0c02060-e3b3-4965-bd7c-415e1a7a9fde-6951","webAnalyticsConfiguration":{"autoCapture":{"jsError":true}}},"defaultEventName":"IDUX_ESTSClientTelemetryEvent_WebWatson","serviceID":3,"endpointUrl":"https://eu-mobile.events.data.microsoft.com/OneCollector/1.0/"},"fApplyAsciiRegexOnInput":true,"country":"IN","fBreakBrandingSigninString":true,"bsso":{"states":{"START":"start","INPROGRESS":"in-progress","END":"end","END_SSO":"end-sso","END_USERS":"end-users"},"nonce":"AwABEgEAAAADAOz_BQD0_zKb-aqaTCFaUYjn_zdo6HYbsiY7OlFBFZW3kqFyDf0PuxL77J7iHTNFk0aXX4x2LKu-o5li21TbrTE4tcz4MMcgAA","overallTimeoutMs":4000,"reloadOnFailure":true,"telemetry":{"type":"ChromeSsoTelemetry","nonce":"AwABDwEAAAADAOz_BQD0_72PjNCuuC2w7R0gROrUKwFA0tO6PNS0QhHHpNUdx8gFBmUkDfi_xMJamYgGK0pBLF7CT8TVTT5KmMoow9uKZR4yfakSHZvd4AfjYBSZ0y4_IAA","reportStates":[]},"redirectEndStates":["end"],"cookieNames":{"aadSso":"AADSSO","winSso":"ESTSSSO","ssoTiles":"ESTSSSOTILES","ssoPulled":"SSOCOOKIEPULLED","userList":"ESTSUSERLIST"},"enabled":true,"type":"chrome","reason":"Pull is needed"},"urlNoCookies":"https://login.microsoftonline.com/cookiesdisabled","fTrimChromeBssoUrl":true,"inlineMode":5,"fShowCopyDebugDetailsLink":true,"fTenantBrandingCdnAddEventHandlers":true,"fAddTryCatchForIFrameRedirects":true};
//]]></script> 
<script type="text/javascript" nonce='A2moBrtYUP-no8mJhhAR0Q'>//<![CDATA[
!function(){var e=window,r=e.$Debug=e.$Debug||{},t=e.$Config||{};if(!r.appendLog){var n=[],o=0;r.appendLog=function(e){var r=t.maxDebugLog||25,i=(new Date).toUTCString()+":"+e;n.push(o+":"+i),n.length>r&&n.shift(),o++},r.getLogs=function(){return n}}}(),function(){function e(e,r){function t(i){var a=e[i];if(i<n-1){return void(o.r[a]?t(i+1):o.when(a,function(){t(i+1)}))}r(a)}var n=e.length;t(0)}function r(e,r,i){function a(){var e=!!s.method,o=e?s.method:i[0],a=s.extraArgs||[],u=n.$WebWatson;try{
var c=t(i,!e);if(a&&a.length>0){for(var d=a.length,l=0;l<d;l++){c.push(a[l])}}o.apply(r,c)}catch(e){return void(u&&u.submitFromException&&u.submitFromException(e))}}var s=o.r&&o.r[e];return r=r||this,s&&(s.skipTimeout?a():n.setTimeout(a,0)),s}function t(e,r){return Array.prototype.slice.call(e,r?1:0)}var n=window;n.$Do||(n.$Do={"q":[],"r":[],"removeItems":[],"lock":0,"o":[]});var o=n.$Do;o.when=function(t,n){function i(e){r(e,a,s)||o.q.push({"id":e,"c":a,"a":s})}var a=0,s=[],u=1;"function"==typeof n||(a=n,
u=2);for(var c=u;c<arguments.length;c++){s.push(arguments[c])}t instanceof Array?e(t,i):i(t)},o.register=function(e,t,n){if(!o.r[e]){o.o.push(e);var i={};if(t&&(i.method=t),n&&(i.skipTimeout=n),arguments&&arguments.length>3){i.extraArgs=[];for(var a=3;a<arguments.length;a++){i.extraArgs.push(arguments[a])}}o.r[e]=i,o.lock++;try{for(var s=0;s<o.q.length;s++){var u=o.q[s];u.id==e&&r(e,u.c,u.a)&&o.removeItems.push(u)}}catch(e){throw e}finally{if(0===--o.lock){for(var c=0;c<o.removeItems.length;c++){
for(var d=o.removeItems[c],l=0;l<o.q.length;l++){if(o.q[l]===d){o.q.splice(l,1);break}}}o.removeItems=[]}}}},o.unregister=function(e){o.r[e]&&delete o.r[e]}}(),function(e,r){function t(){if(!a){if(!r.body){return void setTimeout(t)}a=!0,e.$Do.register("doc.ready",0,!0)}}function n(){if(!s){if(!r.body){return void setTimeout(n)}t(),s=!0,e.$Do.register("doc.load",0,!0),i()}}function o(e){(r.addEventListener||"load"===e.type||"complete"===r.readyState)&&t()}function i(){
r.addEventListener?(r.removeEventListener("DOMContentLoaded",o,!1),e.removeEventListener("load",n,!1)):r.attachEvent&&(r.detachEvent("onreadystatechange",o),e.detachEvent("onload",n))}var a=!1,s=!1;if("complete"===r.readyState){return void setTimeout(n)}!function(){r.addEventListener?(r.addEventListener("DOMContentLoaded",o,!1),e.addEventListener("load",n,!1)):r.attachEvent&&(r.attachEvent("onreadystatechange",o),e.attachEvent("onload",n))}()}(window,document),function(){function e(){
return f.$Config||f.ServerData||{}}function r(e,r){var t=f.$Debug;t&&t.appendLog&&(r&&(e+=" '"+(r.src||r.href||"")+"'",e+=", id:"+(r.id||""),e+=", async:"+(r.async||""),e+=", defer:"+(r.defer||"")),t.appendLog(e))}function t(){var e=f.$B;if(void 0===d){if(e){d=e.IE}else{var r=f.navigator.userAgent;d=-1!==r.indexOf("MSIE ")||-1!==r.indexOf("Trident/")}}return d}function n(){var e=f.$B;if(void 0===l){if(e){l=e.RE_Edge}else{var r=f.navigator.userAgent;l=-1!==r.indexOf("Edge")}}return l}function o(e){
var r=e.indexOf("?"),t=r>-1?r:e.length,n=e.lastIndexOf(".",t);return e.substring(n,n+v.length).toLowerCase()===v}function i(){var r=e();return(r.loader||{}).slReportFailure||r.slReportFailure||!1}function a(){return(e().loader||{}).redirectToErrorPageOnLoadFailure||!1}function s(){return(e().loader||{}).logByThrowing||!1}function u(e){if(!t()&&!n()){return!1}var r=e.src||e.href||"";if(!r){return!0}if(o(r)){var i,a,s;try{i=e.sheet,a=i&&i.cssRules,s=!1}catch(e){s=!0}if(i&&!a&&s){return!0}
if(i&&a&&0===a.length){return!0}}return!1}function c(){function t(e){g.getElementsByTagName("head")[0].appendChild(e)}function n(e,r,t,n){var u=null;return u=o(e)?i(e):"script"===n.toLowerCase()?a(e):s(e,n),r&&(u.id=r),"function"==typeof u.setAttribute&&(u.setAttribute("crossorigin","anonymous"),t&&"string"==typeof t&&u.setAttribute("integrity",t)),u}function i(e){var r=g.createElement("link");return r.rel="stylesheet",r.type="text/css",r.href=e,r}function a(e){
var r=g.createElement("script"),t=g.querySelector("script[nonce]");if(r.type="text/javascript",r.src=e,r.defer=!1,r.async=!1,t){var n=t.nonce||t.getAttribute("nonce");r.setAttribute("nonce",n)}return r}function s(e,r){var t=g.createElement(r);return t.src=e,t}function d(e,r){if(e&&e.length>0&&r){for(var t=0;t<e.length;t++){if(-1!==r.indexOf(e[t])){return!0}}}return!1}function l(r){if(e().fTenantBrandingCdnAddEventHandlers){var t=d(E,r)?E:b;if(!(t&&t.length>1)){return r}for(var n=0;n<t.length;n++){
if(-1!==r.indexOf(t[n])){var o=t[n+1<t.length?n+1:0],i=r.substring(t[n].length);return"https://"!==t[n].substring(0,"https://".length)&&(o="https://"+o,i=i.substring("https://".length)),o+i}}return r}if(!(b&&b.length>1)){return r}for(var a=0;a<b.length;a++){if(0===r.indexOf(b[a])){return b[a+1<b.length?a+1:0]+r.substring(b[a].length)}}return r}function f(e,t,n,o){if(r("[$Loader]: "+(L.failMessage||"Failed"),o),w[e].retry<y){return w[e].retry++,h(e,t,n),void c._ReportFailure(w[e].retry,w[e].srcPath)}n&&n()}
function v(e,t,n,o){if(u(o)){return f(e,t,n,o)}r("[$Loader]: "+(L.successMessage||"Loaded"),o),h(e+1,t,n);var i=w[e].onSuccess;"function"==typeof i&&i(w[e].srcPath)}function h(e,o,i){if(e<w.length){var a=w[e];if(!a||!a.srcPath){return void h(e+1,o,i)}a.retry>0&&(a.srcPath=l(a.srcPath),a.origId||(a.origId=a.id),a.id=a.origId+"_Retry_"+a.retry);var s=n(a.srcPath,a.id,a.integrity,a.tagName);s.onload=function(){v(e,o,i,s)},s.onerror=function(){f(e,o,i,s)},s.onreadystatechange=function(){
"loaded"===s.readyState?setTimeout(function(){v(e,o,i,s)},500):"complete"===s.readyState&&v(e,o,i,s)},t(s),r("[$Loader]: Loading '"+(a.srcPath||"")+"', id:"+(a.id||""))}else{o&&o()}}var p=e(),y=p.slMaxRetry||2,m=p.loader||{},b=m.cdnRoots||[],E=m.tenantBrandingCdnRoots||[],L=this,w=[];L.retryOnError=!0,L.successMessage="Loaded",L.failMessage="Error",L.Add=function(e,r,t,n,o,i){e&&w.push({"srcPath":e,"id":r,"retry":n||0,"integrity":t,"tagName":o||"script","onSuccess":i})},L.AddForReload=function(e,r){
var t=e.src||e.href||"";L.Add(t,"AddForReload",e.integrity,1,e.tagName,r)},L.AddIf=function(e,r,t){e&&L.Add(r,t)},L.Load=function(e,r){h(0,e,r)}}var d,l,f=window,g=f.document,v=".css";c.On=function(e,r,t){if(!e){throw"The target element must be provided and cannot be null."}r?c.OnError(e,t):c.OnSuccess(e,t)},c.OnSuccess=function(e,t){if(!e){throw"The target element must be provided and cannot be null."}if(u(e)){return c.OnError(e,t)}var n=e.src||e.href||"",o=i(),s=a();r("[$Loader]: Loaded",e);var d=new c
;d.failMessage="Reload Failed",d.successMessage="Reload Success",d.Load(null,function(){if(o){throw"Unexpected state. ResourceLoader.Load() failed despite initial load success. ['"+n+"']"}s&&(document.location.href="/error.aspx?err=504")})},c.OnError=function(e,t){var n=e.src||e.href||"",o=i(),s=a();if(!e){throw"The target element must be provided and cannot be null."}r("[$Loader]: Failed",e);var u=new c;u.failMessage="Reload Failed",u.successMessage="Reload Success",u.AddForReload(e,t),
u.Load(null,function(){if(o){throw"Failed to load external resource ['"+n+"']"}s&&(document.location.href="/error.aspx?err=504")}),c._ReportFailure(0,n)},c._ReportFailure=function(e,r){if(s()&&!t()){throw"[Retry "+e+"] Failed to load external resource ['"+r+"'], reloading from fallback CDN endpoint"}},f.$Loader=c}(),function(){function e(){if(!E){var e=new h.$Loader;e.AddIf(!h.jQuery,y.sbundle,"WebWatson_DemandSupport"),y.sbundle=null,delete y.sbundle,e.AddIf(!h.$Api,y.fbundle,"WebWatson_DemandFramework"),
y.fbundle=null,delete y.fbundle,e.Add(y.bundle,"WebWatson_DemandLoaded"),e.Load(r,t),E=!0}}function r(){if(h.$WebWatson){if(h.$WebWatson.isProxy){return void t()}m.when("$WebWatson.full",function(){for(;b.length>0;){var e=b.shift();e&&h.$WebWatson[e.cmdName].apply(h.$WebWatson,e.args)}})}}function t(){if(!h.$WebWatson||h.$WebWatson.isProxy){if(!L&&JSON){try{var e=new XMLHttpRequest;e.open("POST",y.url),e.setRequestHeader("Accept","application/json"),
e.setRequestHeader("Content-Type","application/json; charset=UTF-8"),e.setRequestHeader("canary",p.apiCanary),e.setRequestHeader("client-request-id",p.correlationId),e.setRequestHeader("hpgid",p.hpgid||0),e.setRequestHeader("hpgact",p.hpgact||0);for(var r=-1,t=0;t<b.length;t++){if("submit"===b[t].cmdName){r=t;break}}var o=b[r]?b[r].args||[]:[],i={"sr":y.sr,"ec":"Failed to load external resource [Core Watson files]","wec":55,"idx":1,"pn":p.pgid||"","sc":p.scid||0,"hpg":p.hpgid||0,
"msg":"Failed to load external resource [Core Watson files]","url":o[1]||"","ln":0,"ad":0,"an":!1,"cs":"","sd":p.serverDetails,"ls":null,"diag":v(y)};e.send(JSON.stringify(i))}catch(e){}L=!0}y.loadErrorUrl&&window.location.assign(y.loadErrorUrl)}n()}function n(){b=[],h.$WebWatson=null}function o(r){return function(){var t=arguments;b.push({"cmdName":r,"args":t}),e()}}function i(){var e=["foundException","resetException","submit"],r=this;r.isProxy=!0;for(var t=e.length,n=0;n<t;n++){var i=e[n];i&&(r[i]=o(i))}
}function a(e,r,t,n,o,i,a){var s=h.event;return i||(i=l(o||s,a?a+2:2)),h.$Debug&&h.$Debug.appendLog&&h.$Debug.appendLog("[WebWatson]:"+(e||"")+" in "+(r||"")+" @ "+(t||"??")),$.submit(e,r,t,n,o||s,i,a)}function s(e,r){return{"signature":e,"args":r,"toString":function(){return this.signature}}}function u(e){for(var r=[],t=e.split("\n"),n=0;n<t.length;n++){r.push(s(t[n],[]))}return r}function c(e){for(var r=[],t=e.split("\n"),n=0;n<t.length;n++){var o=s(t[n],[]);t[n+1]&&(o.signature+="@"+t[n+1],n++),r.push(o)
}return r}function d(e){if(!e){return null}try{if(e.stack){return u(e.stack)}if(e.error){if(e.error.stack){return u(e.error.stack)}}else if(window.opera&&e.message){return c(e.message)}}catch(e){}return null}function l(e,r){var t=[];try{for(var n=arguments.callee;r>0;){n=n?n.caller:n,r--}for(var o=0;n&&o<w;){var i="InvalidMethod()";try{i=n.toString()}catch(e){}var a=[],u=n.args||n.arguments;if(u){for(var c=0;c<u.length;c++){a[c]=u[c]}}t.push(s(i,a)),n=n.caller,o++}}catch(e){t.push(s(e.toString(),[]))}
var l=d(e);return l&&(t.push(s("--- Error Event Stack -----------------",[])),t=t.concat(l)),t}function f(e){if(e){try{var r=/function (.{1,})\(/,t=r.exec(e.constructor.toString());return t&&t.length>1?t[1]:""}catch(e){}}return""}function g(e){if(e){try{if("string"!=typeof e&&JSON&&JSON.stringify){var r=f(e),t=JSON.stringify(e);return t&&"{}"!==t||(e.error&&(e=e.error,r=f(e)),(t=JSON.stringify(e))&&"{}"!==t||(t=e.toString())),r+":"+t}}catch(e){}}return""+(e||"")}function v(e){var r=[];try{
if(jQuery?(r.push("jQuery v:"+jQuery().jquery),jQuery.easing?r.push("jQuery.easing:"+JSON.stringify(jQuery.easing)):r.push("jQuery.easing is not defined")):r.push("jQuery is not defined"),e&&e.expectedVersion&&r.push("Expected jQuery v:"+e.expectedVersion),m){var t,n="";for(t=0;t<m.o.length;t++){n+=m.o[t]+";"}for(r.push("$Do.o["+n+"]"),n="",t=0;t<m.q.length;t++){n+=m.q[t].id+";"}r.push("$Do.q["+n+"]")}if(h.$Debug&&h.$Debug.getLogs){var o=h.$Debug.getLogs();o&&o.length>0&&(r=r.concat(o))}if(b){
for(var i=0;i<b.length;i++){var a=b[i];if(a&&"submit"===a.cmdName){try{if(JSON&&JSON.stringify){var s=JSON.stringify(a);s&&r.push(s)}}catch(e){r.push(g(e))}}}}}catch(e){r.push(g(e))}return r}var h=window,p=h.$Config||{},y=p.watson,m=h.$Do;if(!h.$WebWatson&&y){var b=[],E=!1,L=!1,w=10,$=h.$WebWatson=new i;$.CB={},$._orgErrorHandler=h.onerror,h.onerror=a,$.errorHooked=!0,m.when("jQuery.version",function(e){y.expectedVersion=e}),m.register("$WebWatson")}}(),function(){function e(e,r){
for(var t=r.split("."),n=t.length,o=0;o<n&&null!==e&&void 0!==e;){e=e[t[o++]]}return e}function r(r){var t=null;return null===u&&(u=e(i,"Constants")),null!==u&&r&&(t=e(u,r)),null===t||void 0===t?"":t.toString()}function t(t){var n=null;return null===a&&(a=e(i,"$Config.strings")),null!==a&&t&&(n=e(a,t.toLowerCase())),null!==n&&void 0!==n||(n=r(t)),null===n||void 0===n?"":n.toString()}function n(e,r){var n=null;return e&&r&&r[e]&&(n=t("errors."+r[e])),n||(n=t("errors."+e)),n||(n=t("errors."+c)),n||(n=t(c)),n}
function o(t){var n=null;return null===s&&(s=e(i,"$Config.urls")),null!==s&&t&&(n=e(s,t.toLowerCase())),null!==n&&void 0!==n||(n=r(t)),null===n||void 0===n?"":n.toString()}var i=window,a=null,s=null,u=null,c="GENERIC_ERROR";i.GetString=t,i.GetErrorString=n,i.GetUrl=o}(),function(){var e=window,r=e.$Config||{};e.$B=r.browser||{}}(),function(){function e(e,r,t){e&&e.addEventListener?e.addEventListener(r,t):e&&e.attachEvent&&e.attachEvent("on"+r,t)}function r(r,t){e(document.getElementById(r),"click",t)}
function t(r,t){var n=document.getElementsByName(r);n&&n.length>0&&e(n[0],"click",t)}var n=window;n.AddListener=e,n.ClickEventListenerById=r,n.ClickEventListenerByName=t}();
//]]></script> 
<script type="text/javascript" nonce='A2moBrtYUP-no8mJhhAR0Q'>//<![CDATA[
!function(t,e){!function(){var n=e.getElementsByTagName("head")[0];n&&n.addEventListener&&(n.addEventListener("error",function(e){null!==e.target&&"cdn"===e.target.getAttribute("data-loader")&&t.$Loader.OnError(e.target)},!0),n.addEventListener("load",function(e){null!==e.target&&"cdn"===e.target.getAttribute("data-loader")&&t.$Loader.OnSuccess(e.target)},!0))}()}(window,document);
//]]></script>
    <script type="text/javascript" nonce='A2moBrtYUP-no8mJhhAR0Q'>
        ServerData = $Config;
    </script>

    <script data-loader="cdn" crossorigin="anonymous" src="https://aadcdn.msauth.net/shared/1.0/content/js/BssoInterrupt_Core_bazYuVH6rF7OQmuNhACwPg2.js" integrity='sha384-2/93+4gEGMyhh2t1JJcRp/VZLhG3NZzBffANDqw59QhcsDPhV8DRl7FuV3rI6KzZ' nonce='A2moBrtYUP-no8mJhhAR0Q'></script>

    
</head>
<body data-bind="defineGlobals: ServerData" style="display: none">
</body>
</html>